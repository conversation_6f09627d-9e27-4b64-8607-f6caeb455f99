<template>
  <div class="koi-flex flex-row">
    <koi-card class="max-w-160px m-r-6px">
      <template #header>
        <div class="flex justify-between select-none">
          <div class="flex flex-items-center gap-6px">
            <el-icon :size="18">
              <Picture />
            </el-icon>
            <div>图片分类</div>
          </div>
        </div>
      </template>
      <div
        class="h-26px text-14px text-#555 m-t-2px rounded-6px dark:text-#CFD3DC flex flex-items-center p-y-4px p-x-16px hover:bg-[rgba(0,0,0,0.06)] hover:dark:bg-[rgba(255,255,255,0.06)]"
        v-for="item in koiDicts.sys_picture_type"
        :key="item.dict_id"
        :class="selectedIndex === item?.dict_value ? 'bg-[--el-color-primary-light-8]! text-[--el-color-primary]!' : ''"
        @click="handleSelectedIndex(item?.dict_value)"
      >
        <div class="line-clamp-1">{{ item.dict_label }}</div>
      </div>
    </koi-card>
    <KoiCard>
      <!-- 搜索条件 -->
      <el-form v-show="showSearch" :inline="true">
        <el-form-item label="图片后缀" prop="file_ext">
          <el-input
            placeholder="请输入图片后缀"
            v-model="searchParams.file_ext"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item label="服务类型" prop="file_service">
          <el-select
            placeholder="请选择图片服务类型"
            v-model="searchParams.file_service"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          >
            <el-option
              v-for="koi in koiDicts.sys_file_service"
              :key="koi.dict_value"
              :label="koi.dict_label"
              :value="koi.dict_value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch" v-auth="['dogadmin:sysFile:search']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格头部按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5" v-auth="['dogadmin:sysFile:upload']">
          <el-button type="primary" icon="Upload" plain @click="handleUpload()">图片上传</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['dogadmin:sysFile:delete']">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete()" :disabled="multiple">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" icon="Switch" plain @click="handleSwitch()">切换</el-button>
        </el-col>
        <KoiToolbar v-model:showSearch="showSearch" @refreshTable="handleListPage"></KoiToolbar>
      </el-row>

      <div class="h-20px"></div>
      <!-- 图库展示 -->
      <div class="koi-flex flex-row flex-wrap flex-content-start" v-show="isSwitch">
        <div
          v-if="tableList.length > 0"
          class="pos-relative flex flex-justify-center flex-items-center w-160px h-220px m-y-5px m-r-8px border-rounded border-solid border-1px border-[--el-border-color-light] hover:shadow-[--el-box-shadow-light] bg-[rgba(50,50,50,0.06)] dark:bg-[rgba(255,255,255,0.06)]"
          v-for="item in tableList"
          :key="item.pictureId"
        >
          <el-image
            class="w-156px h-156px"
            fit="contain"
            :preview-teleported="true"
            :preview-src-list="[item.file_path]"
            :src="item.file_path"
          >
            <template #error>
              <el-image
                src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"
                fit="cover"
                :preview-src-list="['https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png']"
                :preview-teleported="true"
                class="w-156px h-156px"
              ></el-image>
            </template>
          </el-image>
          <!-- 选择图片 -->
          <div class="pos-absolute right-2px top-2px max-w-100px text-16px">
            <el-checkbox
              size="small"
              :key="item.pictureId"
              :value="item.pictureId"
              label="选择"
              border
              @change="handlePictureCheckBox($event, item.pictureId)"
            />
          </div>
          <!-- 图片下载/详情/删除 -->
          <div class="pos-absolute right-0 bottom-0 max-w-100px text-16px m-r-4px m-b-2px flex select-none">
            <el-tooltip content="删除文件🌻" placement="top">
              <div
                class="m-r-2px hover:text-[--el-color-primary] hover:bg-[rgba(0,0,0,0.06)] rounded hover:dark:bg-[rgba(255,255,255,0.06)]"
                @click="handleDelete(item)"
              >
                <el-icon class="m-4px"><Delete /></el-icon>
              </div>
            </el-tooltip>

            <el-tooltip placement="top">
              <template #content>
                <div class="w-260px text-12px">
                  <div>原名称：{{ item.file_name }}</div>
                  <div>新名称：{{ item.new_name }}</div>
                  <div>文件大小：{{ item.file_size }}</div>
                  <div>上传路径：{{ item.file_upload }}</div>
                  <div>回显路径：{{ item.file_path }}</div>
                  <div>
                    服务类型：{{
                      item.file_service == "1"
                        ? "本地存储"
                        : item.file_service == "2"
                          ? "MINIO"
                          : item.file_service == "3"
                            ? "OSS"
                            : "其他"
                    }}
                  </div>
                </div>
              </template>
              <div
                class="m-r-2px hover:text-[--el-color-primary] hover:bg-[rgba(0,0,0,0.06)] rounded hover:dark:bg-[rgba(255,255,255,0.06)]"
              >
                <el-icon class="m-4px"><Warning /></el-icon>
              </div>
            </el-tooltip>
            <el-tooltip content="下载文件🌻" placement="top">
              <div
                class="hover:text-[--el-color-primary] hover:bg-[rgba(0,0,0,0.06)] rounded hover:dark:bg-[rgba(255,255,255,0.06)]"
                @click="handleDownload(item)"
              >
                <el-icon class="m-4px"><Download /></el-icon>
              </div>
            </el-tooltip>
          </div>
        </div>
        <div v-else class="w-100% h-100%">
          <el-empty :image="noPicture" :image-size="360" description="主人，没有数据了哟👻"></el-empty>
        </div>
      </div>
      <!-- 数据表格 -->
      <el-table
        v-show="!isSwitch"
        v-loading="loading"
        border
        :data="tableList"
        empty-text="暂时没有数据哟🌻"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" prop="pictureId" width="70px" align="center" type="index"></el-table-column>
        
        <el-table-column
          label="图片原始名称"
          prop="file_name"
          width="120px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="图片新名称"
          prop="new_name"
          width="220px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column label="图片回显路径" prop="file_path" width="120px" align="center" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-tooltip :content="scope.row.file_path" placement="top">
              <div class="flex justify-center">
                <el-image
                  class="rounded-8px w-60px h-60px"
                  fit="contain"
                  :preview-teleported="true"
                  :preview-src-list="[scope.row.file_path]"
                  :src="scope.row.file_path"
                >
                  <template #error>
                    <el-image
                      src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"
                      fit="cover"
                      :preview-src-list="['https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png']"
                      :preview-teleported="true"
                      class="w-60px h-60px rounded-8px"
                    ></el-image>
                  </template>
                </el-image>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="图片类型" prop="file_type" width="100px" align="center">
          <template #default="scope">
            <KoiTag :tagOptions="koiDicts.sys_picture_type" :value="scope.row.file_type"></KoiTag>
          </template>
        </el-table-column>
        <el-table-column
          label="图片大小"
          prop="file_size"
          width="120px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="图片后缀"
          prop="file_ext"
          width="120px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="图片上传路径"
          prop="file_upload"
          width="120px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        
        <el-table-column label="服务类型" prop="file_service" width="100px" align="center">
          <template #default="scope">
            <KoiTag :tagOptions="koiDicts.sys_file_service" :value="scope.row.file_service"></KoiTag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="create_time" width="180px" align="center"></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="110"
          fixed="right"
          v-auth="['dogadmin:sysFile:download', 'dogadmin:sysFile:delete']"
        >
          <template #default="{ row }">
            <el-tooltip content="图片下载🌻" placement="top">
              <el-button
                type="info"
                icon="Download"
                circle
                plain
                @click="handleDownload(row)"
                v-auth="['dogadmin:sysFile:download']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button
                type="danger"
                icon="Delete"
                circle
                plain
                @click="handleDelete(row)"
                v-auth="['dogadmin:sysFile:delete']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="h-20px"></div>
      <!-- 分页 -->
      <el-pagination
        background
        v-model:current-page="searchParams.page_no"
        v-model:page-size="searchParams.page_size"
        v-show="total > 0"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleListPage"
        @current-change="handleListPage"
      />

      <!-- 多文件/单文件上传 -->
      <KoiDialog ref="koiDialogRef" :title="title" :footerHidden="true" :height="330">
        <template #content>
          <el-form ref="formRef" :rules="rules" :model="form" label-width="auto" status-icon>
            <el-row>
              <el-col :xs="{ span: 12 }" :sm="{ span: 12 }">
                <el-form-item label="服务类型" prop="file_service">
                  <el-select
                    placeholder="请选择服务类型"
                    v-model="form.file_service"
                    clearable
                    style="width: 220px"
                    @change="handlefile_serviceChange"
                  >
                    <el-option
                      v-for="koi in koiDicts.sys_file_service"
                      :key="koi.dict_value"
                      :label="koi.dict_label"
                      :value="koi.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 12 }" :sm="{ span: 12 }" class="p-l-10px">
                <el-form-item label="图片类型" prop="file_type">
                  <el-select placeholder="请选择图片类型" v-model="form.file_type" clearable style="width: 220px">
                    <el-option
                      v-for="koi in koiDicts.sys_picture_type"
                      v-show="koi.dict_value !== '0'"
                      :key="koi.dict_value"
                      :label="koi.dict_label"
                      :value="koi.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="文件回显路径" prop="file_path">
                  <KoiUploadImages
                    v-model:fileList="form.file_path"
                    :fileParam="form.file_type"
                    folderName="pictures"
                  ></KoiUploadImages>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </KoiDialog>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="picturePage">
import { nextTick, ref, reactive, onMounted } from "vue";
import { getAssets } from "@/utils/index.ts";
import { koiNoticeSuccess, koiNoticeError, koiMsgError, koiMsgWarning, koiMsgBox, koiMsgInfo } from "@/utils/koi.ts";
import { listPage, deleteById, batchDelete } from "@/api/system/picture/index.ts";
import { useKoiDict } from "@/hooks/dicts/index.ts";

const { koiDicts } = useKoiDict(["sys_picture_type", "sys_file_service"]);
const noPicture = ref(`${getAssets("images/error/404.png")}`);
// 数据表格加载页面动画
const loading = ref(false);
/** 是否显示搜索表单 */
const showSearch = ref<boolean>(true); // 默认显示搜索条件
// 数据表格数据
const tableList = ref<any>([]);

// 查询参数
const searchParams = ref<any>({
  page_no: 1, // 第几页
  page_size: 10, // 每页显示多少条
  file_ext: "",
  file_service: ""
});

const total = ref<number>(0);

// 重置搜索参数
const resetSearchParams = () => {
  searchParams.value = {
    page_no: 1,
    page_size: 10,
    file_type: selectedIndex.value,
    file_ext: "",
    file_service: ""
  };
};

/** 搜索 */
const handleSearch = () => {
  searchParams.value.page_no = 1;
  handleTableData();
};

/** 重置 */
const resetSearch = () => {
  resetSearchParams();
  handleListPage();
};

/** 数据表格 */
const handleListPage = async () => {
  try {
    tableList.value = []; // 重置表格数据
    loading.value = true;
    const res: any = await listPage(searchParams.value);
    tableList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

/** 数据表格[不带Loading，删除、批量删除等使用] */
const handleTableData = async () => {
  try {
    const res: any = await listPage(searchParams.value);
    tableList.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

// 获取数据表格数据
onMounted(() => {
  handleListPage();
});

const ids = ref<any>([]); // 选中数组
const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
/** 是否多选 */
const handleSelectionChange = (selection: any) => {
  ids.value = selection.map((item: any) => item.pictureId);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
};

/** 删除 */
const handleDelete = (row: any) => {
  const id = row.pictureId;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认删除该数据么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await deleteById(id);
        handleTableData();
        koiNoticeSuccess("删除成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 批量删除 */
const handleBatchDelete = () => {
  if (ids.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认进行批量删除么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await batchDelete(ids.value);
        handleTableData();
        koiNoticeSuccess("批量删除成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("批量删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 选中颜色
const selectedIndex = ref("0");
/** 查询分类结束 */
// 点击选中分类
const handleSelectedIndex = (value?: string) => {
  if (value) {
    selectedIndex.value = value;
    searchParams.value.file_type = selectedIndex.value;
    handleListPage();
  }
};

// 图片上传
const handleUpload = () => {
  // 打开弹出框
  koiDialogRef.value.koiOpen();
  koiMsgInfo("上传图片🌻");
  // 标题
  title.value = "上传操作🌻";
  // 重置表单
  resetForm();
  form.value.file_path = [];
};

/** 多文件/单文件上传弹框 */
const koiDialogRef = ref();
// 标题
const title = ref("图库表");
// form表单Ref
const formRef = ref<any>();

// form表单
let form = ref<any>({
  file_path: [],
  file_service: "1",
  file_type: "9"
});

/** 清空表单数据 */
const resetForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (formRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      formRef.value.resetFields();
    }
  });
  form.value = {
    file_path: [],
    file_service: "1",
    file_type: "9"
  };
};

/** 表单规则 */
const rules = reactive({
  file_path: [{ required: true, message: "请上传图片", trigger: "blur" }],
  file_service: [{ required: true, message: "请选择服务类型", trigger: "blur" }],
  file_type: [{ required: true, message: "请选择图片类型", trigger: "blur" }]
});

/** 选择类型改变 */
const handlefile_serviceChange = () => {
  // 其他服务类型，定义变量，改变组件上传路径接口
  if (form.value.file_service !== "1") {
    koiMsgWarning("亲，暂时仅支持本地上传哟🌻");
  }
};

/** 文件下载🌻 */
const handleDownload = (row: any) => {
  const fileOriginalName = row?.file_name;
  const url = row?.file_path;
  if (url == null || url == "") {
    koiMsgWarning("请选中需要下载的数据🌻");
    return;
  }
  koiMsgBox("您确认下载该数据么？")
    .then(async () => {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Network response was not ok: ${response.status}`);
        }
        // 创建 Blob 对象
        const blob = await response.blob();
        // 创建对象 URL
        const downloadUrl = window.URL.createObjectURL(blob);
        // 创建一个隐藏的下载链接
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = fileOriginalName; // 设置下载文件名
        link.style.display = "none";
        // 添加到 DOM 中
        document.body.appendChild(link);
        // 触发点击事件
        link.click();
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        koiNoticeSuccess("下载成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("下载失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 切换 */
const isSwitch = ref(false);
const handleSwitch = () => {
  multiple.value = !ids.value.length;
  isSwitch.value = !isSwitch.value;
};

/** 选择图片 */
const handlePictureCheckBox = (event: any, id: any) => {
  if (event === true) {
    // 添加
    ids.value.push(id);
  } else {
    // 移除包含ID的数据
    // 当 event 不为 true 时，使用 filter 方法创建一个新的数组，其中不包含与 id 相等的元素。然后将 pictureCheckBoxList 设置为这个新数组。
    ids.value = ids.value.filter((item: any) => item !== id);
  }
  multiple.value = !ids.value.length;
};
</script>

<style lang="scss" scoped></style>
