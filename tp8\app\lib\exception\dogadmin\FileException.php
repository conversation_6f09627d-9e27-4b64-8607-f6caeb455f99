<?php
declare(strict_types=1);

namespace app\lib\exception\dogadmin;

use app\lib\exception\BaseException;
use Throwable;

/**
 * 文件操作异常类
 * 处理文件上传、下载、删除等操作相关的异常
 */
class FileException extends BaseException
{
    /**
     * 错误码映射
     * @var array
     */
    protected static array $errorCodes = [
        1 => '文件上传失败',
        2 => '文件大小超出限制',
        3 => '文件类型不允许',
        4 => '文件保存路径不存在',
        5 => '文件写入失败',
        6 => '文件不存在',
        7 => '文件读取失败',
        8 => '文件删除失败',
        9 => '文件重命名失败',
        10 => '文件上传配置错误',
        11 => '文件服务类型不支持'
    ];

    /**
     * 构造函数
     * 
     * @param array $data 附加数据
     * @param int $code 错误码
     * @param string $message 自定义错误消息，为空时使用错误码映射的消息
     * @param Throwable|null $previous 上一个异常
     */
    public function __construct(array $data = [], int $code = 1, string $message = '', ?Throwable $previous = null)
    {
        parent::__construct($data, $code, $message, $previous);
    }
    
    /**
     * 创建文件大小超出限制异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function fileSizeExceeded(array $data = []): self
    {
        return new static($data, 2);
    }
    
    /**
     * 创建文件类型不允许异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function fileTypeNotAllowed(array $data = []): self
    {
        return new static($data, 3);
    }
    
    /**
     * 创建文件保存路径不存在异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function saveDirNotExists(array $data = []): self
    {
        return new static($data, 4);
    }
    
    /**
     * 创建文件写入失败异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function fileWriteFailed(array $data = []): self
    {
        return new static($data, 5);
    }
    
    /**
     * 创建文件不存在异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function fileNotExists(array $data = []): self
    {
        return new static($data, 6);
    }
    
    /**
     * 创建文件读取失败异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function fileReadFailed(array $data = []): self
    {
        return new static($data, 7);
    }
    
    /**
     * 创建文件删除失败异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function fileDeleteFailed(array $data = []): self
    {
        return new static($data, 8);
    }
    
    /**
     * 创建文件重命名失败异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function fileRenameFailed(array $data = []): self
    {
        return new static($data, 9);
    }
    
    /**
     * 创建文件上传配置错误异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function configError(array $data = []): self
    {
        return new static($data, 10);
    }
    
    /**
     * 创建文件服务类型不支持异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function serviceTypeNotSupported(array $data = []): self
    {
        return new static($data, 11);
    }
    
    /**
     * 创建无效文件类型异常
     * 
     * @param array $data 附加数据
     * @return static
     */
    public static function invalidFileType(array $data = []): self
    {
        return new static($data, 3);
    }
}