<?php
namespace app\dogadmin\common;

use think\facade\Cache;
use think\Response;

class Captcha
{
    private $width = 100;      // 验证码图片宽度
    private $height = 30;      // 验证码图片高度
    private $length = 4;       // 验证码长度
    private $expire = 300;     // 验证码有效期（秒）

    /**
     * 生成验证码
     * @return array 包含验证码图片base64和验证码key
     */
    public function generate(): array
    {
        // 生成随机验证码
        $code = $this->generateCode();

        // 生成唯一key
        $key = uniqid('captcha_');

        // 将验证码存入缓存
        Cache::set($key, strtolower($code), $this->expire);

        // 创建图片
        $image = $this->createImage($code);

        // 将图片转换为base64
        ob_start();
        imagepng($image);
        $imageData = ob_get_clean();
        $base64Image = 'data:image/png;base64,' . base64_encode($imageData);

        // 释放图片资源
        imagedestroy($image);

        return [
            'code_key' => $key,
            'captcha_picture' => $base64Image,
            'expire' => $this->expire
        ];
    }

    /**
     * 验证验证码
     * @param string $key 验证码key
     * @param string $code 用户输入的验证码
     * @return bool
     */
    public function verify(string $key, string $code): bool
    {
        if (empty($key) || empty($code)) {
            return false;
        }

        // 获取缓存中的验证码
        $cacheCode = Cache::get($key);
        if (!$cacheCode) {
            return false;
        }

        // 验证后立即删除缓存
        Cache::delete($key);

        // 比较验证码（不区分大小写）
        return strtolower($code) === strtolower($cacheCode);
    }

    /**
     * 生成随机验证码
     * @return string
     */
    private function generateCode(): string
    {
        // 去掉了容易混淆的字符：0,1,i,l,o
        $characters = '23456789abcdefghjkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ';
        $code = '';
        for ($i = 0; $i < $this->length; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $code;
    }

    /**
     * 创建验证码图片
     * @param string $code 验证码
     * @return resource
     */
    private function createImage(string $code)
    {
        // 创建画布
        $image = imagecreatetruecolor($this->width, $this->height);

        // 设置背景色
        $bgColor = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $bgColor);

        // 添加干扰线
        for ($i = 0; $i < 10; $i++) {
            $lineColor = imagecolorallocate($image, rand(200, 255), rand(200, 255), rand(200, 255));
            imageline($image, rand(0, $this->width), rand(0, $this->height),
                     rand(0, $this->width), rand(0, $this->height), $lineColor);
        }

        // 添加干扰点
        for ($i = 0; $i < 30; $i++) {
            $pointColor = imagecolorallocate($image, rand(200, 255), rand(200, 255), rand(200, 255));
            imagesetpixel($image, rand(0, $this->width), rand(0, $this->height), $pointColor);
        }

        // 添加验证码文字
        $fontSize = 5; // 内置字体大小 1-5
        $x = 10;
        $y = $this->height / 2 - 8;

        for ($i = 0; $i < $this->length; $i++) {
            $textColor = imagecolorallocate($image, rand(0, 100), rand(0, 100), rand(0, 100));
            imagechar($image, $fontSize, $x + $i * 20, $y, $code[$i], $textColor);
        }

        return $image;
    }
}
