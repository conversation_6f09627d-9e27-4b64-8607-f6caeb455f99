<?php
declare(strict_types=1);

namespace app\lib\exception\dogadmin;

use app\lib\exception\BaseException;
use Throwable;

/**
 * 系统异常类
 * 处理系统级别的异常
 */
class SysException extends BaseException
{
    /**
     * 构造函数
     * 
     * @param array $data 附加数据
     * @param int $code 错误码
     * @param string $message 自定义错误消息，为空时使用错误码映射的消息
     * @param Throwable|null $previous 上一个异常
     */
    public function __construct(array $data = [], int $code = 1, string $message = '', ?Throwable $previous = null)
    {
        parent::__construct($data, $code, $message, $previous);
    }
}