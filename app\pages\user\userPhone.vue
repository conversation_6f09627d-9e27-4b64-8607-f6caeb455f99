<template>
	<view class="wrap">
		<!-- #ifndef MP-WEIXIN -->
		<tui-form ref="form">
			<tui-input label="手机号码" placeholder="请输入手机号码" clearable v-model="formData.phone"></tui-input>
			<tui-input label="数字验证" placeholder="请输入数字验证码" v-model="formData.vcode">
				<view slot="right">
					<image :src="baseURL+'login/getCaptcha?key='+key" style="width: 160rpx;height: 70rpx;"
						@click="changeKey"></image>
				</view>
			</tui-input>
			<tui-input label="手机验证" placeholder="请输入手机验证码" v-model="formData.code">
				<view slot="right">
					<tui-countdown-verify :successVal="successSend" :resetVal="resetSend" @send="getCode" :params="1">
					</tui-countdown-verify>
				</view>
			</tui-input>
		</tui-form>
		<view class="btn-wrap">
			<tui-button type="blue" @click="submit" shape="circle">提交</tui-button>
		</view>
		<!-- #endif -->

		<!-- #ifdef MP-WEIXIN -->
		<view class="btn-wrap">
			<button class="mp-login-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" type="primary">一键绑定手机</button>
			<!-- <tui-button height="72rpx" :size="28" type="danger" shape="circle" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">绑定手机</tui-button> -->
		</view>
		<!-- #endif -->
		<view class="tips">如需修改请联系客服</view>
	</view>
</template>

<script>
	import { sendSms, bindPhone, getPhoneNumberWeapp } from '@/api/login.js';
	import { rule_bindPhone } from '@/config/rules.js';
	import { baseURL } from '@/api/config.js';
	import { mapActions } from 'vuex';
	export default {
		data() {
			return {
				key: "",
				formData: {
					phone: "",
					vcode: "",
					code: ""
				},
				rules: rule_bindPhone,
				baseURL: baseURL,
				successSend: 0,
				resetSend: 0
			}
		},
		onReady() {
			this.changeKey();
		},

		methods: {
			...mapActions({
				getUser: 'getUserInfo'
			}),
			changeKey() {
				this.key = this.randomString();
			},
			randomString(len) {
				len = len || 32;
				let chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
				let maxPos = chars.length;
				let pwd = '';
				for (let i = 0; i < len; i++) {
					pwd += chars.charAt(Math.floor(Math.random() * maxPos));
				}
				return pwd;
			},
			submit() {
				this.$refs.form.validate(this.formData, this.rules).then(res => {
					this._bindPhone();
				}).catch(errors => {
				})
			},
			getCode() {
				if (!/^1[123456789]\d{9}$/.test(this.formData.phone)) {
					uni.showToast({
						title: '手机错误',
						icon: 'none'
					})
					this.resetSend += 1;
					return false;
				}
				uni.showLoading({
					title: '正在获取验证码',
					mask: true
				})
				this.sendSms();
			},
			sendSms() {
				let data = {
					phone: this.formData.phone,
					vcode: this.formData.vcode,
					key: this.key,
					type: 'bindPhone'
				}
				uni.hideLoading();
				sendSms(data).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: '验证码已发送',
							icon: 'none'
						})
						this.successSend += 1;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						this.resetSend += 1;
					}
				})
			},
			_bindPhone() {
				let data = {
					...this.formData
				}
				bindPhone(data).then(res => {
					this.phoneBack(res);
				})
			},
			getPhoneNumber(e) {
				if (e.detail.errMsg == "getPhoneNumber:ok") {
					let data = {
						...e.detail
					}
					getPhoneNumberWeapp(data).then(res => {
						console.log('getPhoneNumber', res);
						this.phoneBack(res);
					})
				}else {
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'none'
					})
				}
				
				
			},
			phoneBack(res) {
				if (res.code == 0) {
					uni.showToast({
						title: '绑定成功！',
						icon: "none"
					});
					this.getUser().then(res => {
						// uni.reLaunch({
						// 	url: uni.getStorageSync('beforePageFullPath') ||
						// 		'/pages/index/index'
						// })
						uni.navigateBack();
					}).catch(err => {
						uni.showToast({
							title: err.msg,
							icon: "none"
						});
					});
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.wrap {
		background-color: #FFFFFF;
	}
	.tips {
		text-align: center;
		font-size: 30rpx;
		font-weight: 600;
		color: $uni-color-error;
		padding: 20rpx 0;
	}
</style>
