-- 添加AI角色通用字典表菜单


-- 添加AI角色通用字典表主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `name`, `component`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('AI角色通用字典表', 142, '2', '/robot/robotAiCharacterDict/index', 'robotAiCharacterDictPage', 'robot/robotAiCharacterDict/index', 'Menu', 'robot:robotAiCharacterDict:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的菜单ID
SET @menuId = LAST_INSERT_ID();

-- 添加AI角色通用字典表按钮权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `auth`, `status`, `is_hide`,`create_time`, `update_time`) VALUES
('查询', @menuId, '3', 'robot:robotAiCharacterDict:listPage', '1','0', NOW(), NOW()),
('获取详情', @menuId, '3', 'robot:robotAiCharacterDict:getById', '1','0', NOW(), NOW()),
('获取排序', @menuId, '3', 'robot:robotAiCharacterDict:getSorted', '1','0', NOW(), NOW()),
('新增', @menuId, '3', 'robot:robotAiCharacterDict:add', '1','0', NOW(), NOW()),
('修改', @menuId, '3', 'robot:robotAiCharacterDict:update', '1','0', NOW(), NOW()),
('删除', @menuId, '3', 'robot:robotAiCharacterDict:deleteById', '1','0', NOW(), NOW()),
('批量删除', @menuId, '3', 'robot:robotAiCharacterDict:batchDelete', '1','0', NOW(), NOW()),
('更新状态', @menuId, '3', 'robot:robotAiCharacterDict:updateStatus', '1','0', NOW(), NOW());