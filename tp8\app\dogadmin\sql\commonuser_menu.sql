-- 添加通用管理菜单
-- 添加通用管理目录
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('通用管理管理', 0, '1', 'commonUser', 'FolderOpened','common:commonUser:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的目录ID
SET @parentId = LAST_INSERT_ID();

-- 添加通用管理主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `name`, `component`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('通用管理管理', @parentId, '2', '/common/commonUser/index', 'commonUserPage', 'common/commonUser/index', 'Menu', 'common:commonUser:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的菜单ID
SET @menuId = LAST_INSERT_ID();

-- 添加通用管理按钮权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `auth`, `status`, `is_hide`,`create_time`, `update_time`) VALUES
('查询', @menuId, '3', 'common:commonUser:listPage', '1','0', NOW(), NOW()),
('获取详情', @menuId, '3', 'common:commonUser:getById', '1','0', NOW(), NOW()),
('获取排序', @menuId, '3', 'common:commonUser:getSorted', '1','0', NOW(), NOW()),
('新增', @menuId, '3', 'common:commonUser:add', '1','0', NOW(), NOW()),
('修改', @menuId, '3', 'common:commonUser:update', '1','0', NOW(), NOW()),
('删除', @menuId, '3', 'common:commonUser:deleteById', '1','0', NOW(), NOW()),
('批量删除', @menuId, '3', 'common:commonUser:batchDelete', '1','0', NOW(), NOW()),
('更新状态', @menuId, '3', 'common:commonUser:updateStatus', '1','0', NOW(), NOW());