<?php

namespace app\dogadmin\service;

use app\dogadmin\model\SysUserRoleModel;

class SysUserRoleService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysUserRoleModel();
    }

    //根据用户id获取角色
    public function getRoles(array $params = []){
        $where = [
            ['user_id','=',$params['user_id']]
        ];
        $res = $this->model->field('role_id')->where($where)->select()->toArray();
        return $res;
    }

    /**
     * 获取用户的角色ID列表
     * @param int $userId 用户ID
     * @return array 角色ID数组
     */
    public function getRoleIdsByUserId(int $userId): array
    {
        $where = [
            ['user_id','=',$userId]
        ];
        $res = $this->model->where($where)->column('role_id');
        return $res;
    }

    public function getRolesColumn(array $params = []){
        $where = [
            ['user_id','=',$params['user_id']]
        ];
        $res = $this->model->where($where)->column('role_id');
        return $res;
    }

    public function updateRoles(array $params = []){
        //先全部清除然后再全部插入
        $this->model->where('user_id','=',$params['user_id'])->delete();
        foreach ($params['role_ids'] as $role_id){
            $this->model->insert(['user_id'=>$params['user_id'],'role_id'=>$role_id]);
        }
        return true;
    }

    //删除对应用户id的role
    public function deleteRolesByUserId(array $params = []){
        $where = [
            ['user_id','=',$params['user_id']]
        ];
        $res = $this->model->where($where)->delete();
        return $res;
    }
}