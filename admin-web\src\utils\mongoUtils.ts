/**
 * MongoDB数据处理辅助函数
 */

/**
 * 从MongoDB的_id对象中提取ID字符串
 * @param mongoId MongoDB的_id对象 { $oid: string }
 * @returns string 提取的ID字符串
 */
export const extractMongoId = (mongoId: any): string => {
  if (!mongoId) return '';
  if (typeof mongoId === 'string') return mongoId;
  return mongoId.$oid || '';
};

/**
 * 从resources数组中提取特定类型的资源数量
 * @param resources 资源数组
 * @param type 资源类型
 * @returns number 资源数量
 */
export const extractResourceAmount = (resources: any[] = [], type: string): number => {
  if (!resources || !Array.isArray(resources)) return 0;
  const resource = resources.find(r => r.type === type);
  return resource ? resource.amount : 0;
};

/**
 * 构建resources数组
 * @param lingShi 灵石数量
 * @param xianJing 仙晶数量
 * @returns 构建的resources数组
 */
export const buildResources = (lingShi: number | string, xianJing: number | string): any[] => {
  return [
    {
      type: 'ling_shi',
      amount: Number(lingShi) || 0,
      name: '灵石'
    },
    {
      type: 'xian_jing',
      amount: Number(xianJing) || 0,
      name: '仙晶'
    }
  ];
};

/**
 * 将MongoDB数据转换为表单数据
 * @param mongoData MongoDB返回的数据
 * @returns 转换后的表单数据
 */
export const mongoToForm = (mongoData: any): any => {
  if (!mongoData) return {};
  
  return {
    _id: extractMongoId(mongoData._id),
    user_id: mongoData.user_id || '',
    ling_shi: extractResourceAmount(mongoData.resources, 'ling_shi'),
    xian_jing: extractResourceAmount(mongoData.resources, 'xian_jing'),
    equipment_lock: JSON.stringify(mongoData.equipment_lock || [], null, 2),
    equipment_slots: JSON.stringify(mongoData.equipment_slots || {
      helmet: null,
      chest: null,
      pant: null,
      boot: null,
      shoulder: null,
      glove: null,
      weapon: null,
      necklace: null,
      ring: null,
      offhand: null
    }, null, 2),
    skill_lock: JSON.stringify(mongoData.skill_lock || [], null, 2),
    skill_slots: JSON.stringify(mongoData.skill_slots || {
      main_skill: [],
      sub_skills: []
    }, null, 2),
    secret_slots: JSON.stringify(mongoData.secret_slots || ['', '', ''], null, 2),
    inventory: JSON.stringify(mongoData.inventory || {
      backpack: {
        general: [],
        equipment: [],
        skills: []
      },
      general_storages: [],
      equipment_storages: [],
      skills_storages: []
    }, null, 2),
    sorted: mongoData.sorted || '',
    status: mongoData.status || '1',
    create_time: mongoData.create_time || '',
    update_time: mongoData.update_time || ''
  };
};

/**
 * 将表单数据转换为MongoDB数据
 * @param formData 表单数据
 * @returns 转换后的MongoDB数据
 */
export const formToMongo = (formData: any): any => {
  const mongoData: any = { ...formData };
  
  // 构建resources数组
  mongoData.resources = buildResources(formData.ling_shi, formData.xian_jing);
  
  // 解析JSON字符串为对象
  try {
    if (formData.equipment_lock) {
      mongoData.equipment_lock = JSON.parse(formData.equipment_lock);
    }
    if (formData.equipment_slots) {
      mongoData.equipment_slots = JSON.parse(formData.equipment_slots);
    }
    if (formData.skill_lock) {
      mongoData.skill_lock = JSON.parse(formData.skill_lock);
    }
    if (formData.skill_slots) {
      mongoData.skill_slots = JSON.parse(formData.skill_slots);
    }
    if (formData.secret_slots) {
      mongoData.secret_slots = JSON.parse(formData.secret_slots);
    }
    if (formData.inventory) {
      mongoData.inventory = JSON.parse(formData.inventory);
    }
  } catch (error) {
    console.error('JSON解析错误:', error);
    // 如果解析失败，可以在这里处理错误
  }
  
  // 删除不需要的字段
  delete mongoData.ling_shi;
  delete mongoData.xian_jing;
  
  return mongoData;
};