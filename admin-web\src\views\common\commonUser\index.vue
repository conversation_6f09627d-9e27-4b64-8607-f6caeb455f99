<template>
  <div class="koi-flex">
    <KoiCard>
      <!-- 搜索表单 -->
      <el-form :inline="true">
        <el-form-item label="username" prop="username">
          <el-input v-model="searchParams.username" placeholder="请输入username" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
        <el-form-item label="nickname" prop="nickname">
          <el-input v-model="searchParams.nickname" placeholder="请输入nickname" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
        <el-form-item label="phone" prop="phone">
          <el-input v-model="searchParams.phone" placeholder="请输入phone" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
        <el-form-item label="game_id" prop="game_id">
          <el-input v-model="searchParams.game_id" placeholder="请输入game_id" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch" v-auth="['common:commonUser:listPage']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 操作按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5" v-auth="['common:commonUser:add']">
          <el-button type="primary" icon="plus" plain @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['common:commonUser:update']">
          <el-button type="success" icon="edit" plain @click="handleUpdate" :disabled="single">修改</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['common:commonUser:deleteById']">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete" :disabled="multiple">删除</el-button>
        </el-col>
      </el-row>

      <div class="h-20px"></div>
      
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableList"
        border
        empty-text="暂时没有数据哟🌻"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" prop="id" width="80" align="center"></el-table-column>
        <el-table-column label="union_id" prop="union_id" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="openid_mp" prop="openid_mp" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="openid_qq" prop="openid_qq" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="username" prop="username" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="nickname" prop="nickname" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="avatar_url" prop="avatar_url" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="phone" prop="phone" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="coin" prop="coin" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="money" prop="money" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="性别" prop="gender" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="国家" prop="country" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="省份" prop="province" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="城市" prop="city" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="status" prop="status" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              active-value="1"
              inactive-value="0"
              inline-prompt
              @change="handleSwitch(scope.row)"
              v-auth="['common:commonUser:update']"
            />
          </template>
        </el-table-column>
        <el-table-column label="platform" prop="platform" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="region" prop="region" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="game_id" prop="game_id" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="create_time" prop="create_time" width="180" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="update_time" prop="update_time" width="180" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="操作" align="center" width="120" fixed="right" v-auth="['common:commonUser:update', 'commonUser:deleteById']">
          <template #default="{ row }">
            <el-tooltip content="修改🌻" placement="top">
              <el-button
                type="primary"
                icon="Edit"
                circle
                plain
                @click="handleUpdate(row)"
                v-auth="['common:commonUser:update']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button
                type="danger"
                icon="Delete"
                circle
                plain
                @click="handleDelete(row)"
                v-auth="['common:commonUser:deleteById']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="h-20px"></div>
      
      <!-- 分页 -->
      <el-pagination
        background
        v-model:current-page="searchParams.page_no"
        v-model:page-size="searchParams.page_size"
        v-show="total > 0"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <!-- 添加 OR 修改 -->
      <KoiDrawer
        ref="koiDrawerRef"
        :title="title"
        @koiConfirm="handleConfirm"
        @koiCancel="handleCancel"
        :loading="confirmLoading"
      >
        <template #content>
          <el-form ref="formRef" :rules="rules" :model="form" label-width="80px" status-icon>
            <el-row>
              <el-col :span="12">
                <el-form-item label="id" prop="id">
                  <el-input v-model="form.id" placeholder="id" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="union_id" prop="union_id">
                  <el-input v-model="form.union_id" placeholder="请输入union_id" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="openid_mp" prop="openid_mp">
                  <el-input v-model="form.openid_mp" placeholder="请输入openid_mp" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="openid_qq" prop="openid_qq">
                  <el-input v-model="form.openid_qq" placeholder="请输入openid_qq" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="username" prop="username">
                  <el-input v-model="form.username" placeholder="请输入username" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="nickname" prop="nickname">
                  <el-input v-model="form.nickname" placeholder="请输入nickname" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="avatar_url" prop="avatar_url">
                  <el-input v-model="form.avatar_url" placeholder="请输入avatar_url" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="phone" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入phone" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="coin" prop="coin">
                  <el-input v-model="form.coin" placeholder="请输入coin" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="money" prop="money">
                  <el-input v-model="form.money" placeholder="请输入money" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-input v-model="form.gender" placeholder="请输入性别" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="国家" prop="country">
                  <el-input v-model="form.country" placeholder="请输入国家" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="省份" prop="province">
                  <el-input v-model="form.province" placeholder="请输入省份" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="城市" prop="city">
                  <el-input v-model="form.city" placeholder="请输入城市" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="status" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio value="1">启用</el-radio>
                    <el-radio value="0">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="platform" prop="platform">
                  <el-input v-model="form.platform" placeholder="请输入platform" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="region" prop="region">
                  <el-input v-model="form.region" placeholder="请输入region" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="game_id" prop="game_id">
                  <el-input v-model="form.game_id" placeholder="请输入game_id" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="create_time" prop="create_time">
                  <el-date-picker
                    v-model="form.create_time"
                    type="datetime"
                    placeholder="选择create_time"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="update_time" prop="update_time">
                  <el-date-picker
                    v-model="form.update_time"
                    type="datetime"
                    placeholder="选择update_time"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </KoiDrawer>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="commonUser">
import type { FormInstance } from 'element-plus';
import { nextTick, ref, reactive, onMounted } from 'vue';
import {
  koiNoticeSuccess,
  koiNoticeError,
  koiMsgSuccess,
  koiMsgError,
  koiMsgWarning,
  koiMsgBox,
  koiMsgInfo
} from "@/utils/koi.ts";
import { listPage, getById, add, update, deleteById, batchDelete, updateStatus, getSorted } from '@/api/common/commonUser';
import { useKoiDict } from "@/hooks/dicts/index.ts";

const { koiDicts } = useKoiDict(["sys_switch_status"]);

// 表格数据
const loading = ref(false);
const tableList = ref<any[]>([]);
const selectedIds = ref<any[]>([]);

// 总条数
const total = ref<number>(0);

// 搜索表单
const searchParams = reactive({
  page_no: 1,
  page_size: 10,
  username: '',
  nickname: '',
  phone: '',
  game_id: ''
});

// 重置搜索表单
const resetSearch = () => {
  searchParams.page_no = 1;
  searchParams.page_size = 10;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page_no' && key !== 'page_size') {
      searchParams[key] = '';
    }
  });
  handleListPage();
};

// 查询数据
const handleSearch = () => {
  searchParams.page_no = 1;
  handleListPage();
};

// 加载表格数据
const handleListPage = async () => {
  loading.value = true;
  try {
    const res = await listPage(searchParams);
    if (res.code === 1) {
      tableList.value = res.data.records;
      total.value = res.data.total;
    } else {
      koiMsgError(res.msg || '获取数据失败');
    }
  } catch (error) {
    console.error(error);
    koiMsgError('获取数据失败');
  } finally {
    loading.value = false;
  }
};

const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
// 表格选择
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
};

// 分页变化
const handleSizeChange = (size: number) => {
  searchParams.page_size = size;
  handleListPage();
};

const handleCurrentChange = (page: number) => {
  searchParams.page_no = page;
  handleListPage();
};

// 表单相关
const koiDrawerRef = ref();
const title = ref('通用管理');
const formRef = ref<FormInstance>();
const form = ref<any>({
  id: '',
  union_id: '',
  openid_mp: '',
  openid_qq: '',
  username: '',
  nickname: '',
  avatar_url: '',
  phone: '',
  coin: '',
  money: '',
  gender: '',
  country: '',
  province: '',
  city: '',
  status: '1', // 默认启用
  platform: '',
  region: '',
  game_id: '',
  create_time: '',
  update_time: ''
});

// 表单验证规则
const rules = reactive({
  // 没有必填字段，不进行校验
});

// 确认按钮loading
const confirmLoading = ref(false);

// 新增
const handleAdd = () => {
  // 打开抽屉
  koiDrawerRef.value.koiOpen();
  // 重置表单
  resetForm();
  // 设置标题
  title.value = '添加通用管理';
};

// 清空表单数据
const resetForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (formRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      formRef.value.resetFields();
    }
  });
  form.value = {
  id: '',
  union_id: '',
  openid_mp: '',
  openid_qq: '',
  username: '',
  nickname: '',
  avatar_url: '',
  phone: '',
  coin: '',
  money: '',
  gender: '',
  country: '',
  province: '',
  city: '',
  status: '1', // 默认启用
  platform: '',
  region: '',
  game_id: '',
  create_time: '',
  update_time: ''
  };
};

// 回显数据
const handleEcho = async (id: any) => {
  if (id == null || id == "") {
    koiMsgWarning("请选择需要修改的数据🌻");
    return;
  }
  try {
    const res = await getById(id);
    if (res.code === 1) {
      form.value = res.data;
    } else {
      koiNoticeError(res.msg || '获取详情失败');
    }
  } catch (error) {
    console.log(error);
    koiNoticeError('获取详情失败，请刷新重试🌻');
  }
};

// 修改
const handleUpdate = async (row?: any) => {
  // 打开抽屉
  koiDrawerRef.value.koiOpen();
  // 重置表单
  resetForm();
  // 设置标题
  title.value = '修改通用管理';
  
  const id = row ? row.id : selectedIds.value[0];
  if (id == null || id == "") {
    koiMsgError("请选中需要修改的数据🌻");
    return;
  }
  // 回显数据
  handleEcho(id);
};

// 确认提交
const handleConfirm = () => {
  if (!formRef.value) return;
  confirmLoading.value = true;
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (form.value.id != null && form.value.id != "") {
        try {
          const res = await update(form.value);
          if (res.code === 1) {
            koiMsgSuccess('修改成功🌻');
            confirmLoading.value = false;
            koiDrawerRef.value.koiQuickClose();
            resetForm();
            handleListPage();
          } else {
            koiMsgError(res.msg || '修改失败');
            confirmLoading.value = false;
          }
        } catch (error) {
          console.error(error);
          confirmLoading.value = false;
          koiMsgError('修改失败，请重试');
        }
      } else {
        try {
          const res = await add(form.value);
          if (res.code === 1) {
            koiMsgSuccess('添加成功🌻');
            confirmLoading.value = false;
            koiDrawerRef.value.koiQuickClose();
            resetForm();
            handleListPage();
          } else {
            koiMsgError(res.msg || '添加失败');
            confirmLoading.value = false;
          }
        } catch (error) {
          console.error(error);
          confirmLoading.value = false;
          koiMsgError('添加失败，请重试');
        }
      }
    } else {
      koiMsgError('验证失败，请检查填写内容🌻');
      confirmLoading.value = false;
    }
  });
};

// 取消
const handleCancel = () => {
  koiDrawerRef.value.koiClose();
};

// 删除
const handleDelete = (row: any) => {
  const id = row.id;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认删除该数据么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await deleteById(id);
        handleListPage();
        koiNoticeSuccess("删除成功🌻");
      } catch (error) {
        console.log(error);
        handleListPage();
        koiNoticeError("删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedIds.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认进行批量删除么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await batchDelete(selectedIds.value);
        handleListPage();
        koiNoticeSuccess("批量删除成功🌻");
      } catch (error) {
        console.log(error);
        handleListPage();
        koiNoticeError("批量删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 状态switch
const handleSwitch = (row: any) => {
  let text = row.status === "0" ? "停用" : "启用";
  koiMsgBox("确认要[" + text + "]-[" + row.id + "]吗？")
    .then(async () => {
      if (!row.id || !row.status) {
        row.status = row.status == "0" ? "1" : "0";
        koiMsgWarning("请选择需要修改的数据🌻");
        return;
      }
      try {
        await updateStatus(row.id, row.status);
        koiNoticeSuccess("修改成功🌻");
      } catch (error) {
        handleListPage();
        console.log(error);
        koiNoticeError("修改失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      row.status = row.status == "0" ? "1" : "0";
      koiMsgError("已取消🌻");
    });
};

// 获取最新排序数字
const handleSorted = async () => {
  try {
    const res = await getSorted({});
    if (res.code === 1) {
      form.value.sorted = res.data;
    }
  } catch (error) {
    console.log(error);
    koiMsgError("数据查询失败，请重试🌻");
  }
};

// 初始化
onMounted(() => {
  handleListPage();
});
</script>

<style scoped>
.mb-10px {
  margin-bottom: 10px;
}
.mt-10px {
  margin-top: 10px;
}
</style>