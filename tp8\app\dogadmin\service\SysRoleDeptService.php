<?php
namespace app\dogadmin\service;

use app\dogadmin\model\SysRoleDeptModel;

class SysRoleDeptService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysRoleDeptModel();
    }

    public function listDeptIdsByRoleId($roleId){
        $where  = [
            ['role_id','=',$roleId]
        ];
        $menuIds = $this->model->where($where)->column('dept_id');
        return $menuIds;
    }
    public function saveRoleDept($params){
        $where = [
            ['role_id','=',$params['role_id']]
        ];
        $this->model->where($where)->delete();
        $list = [];
        if($params['dept_ids']){
            foreach ($params['dept_ids'] as $v){
                $list[] = ['role_id'=>$params['role_id'],'dept_id'=>$v];
            }
            $this->model->saveAll($list);
        }
        return $list;
    }
} 