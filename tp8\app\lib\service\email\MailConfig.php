<?php

namespace app\lib\service\email;

class MailConfig
{
    /**
     * @var array 默认邮件配置
     */
    protected static $defaultConfig = [
        'host' => '',
        'port' => 465,
        'username' => '',
        'password' => '',
        'from' => '',
        'fromName' => '',
        'charset' => 'UTF-8',
        'smtpSecure' => 'ssl',
        'timeout' => 30
    ];

    /**
     * 获取邮件配置
     * @return array
     */
    public static function getConfig()
    {
        $config = [];

        // 从系统配置读取
        foreach (self::$defaultConfig as $key => $default) {
            $config[$key] = config("mail.{$key}", $default);
        }

        return $config;
    }

    /**
     * 设置临时配置
     * @param array $config
     */
    public static function setTempConfig(array $config)
    {
        // 实现配置覆盖逻辑
    }

    /**
     * 重置为默认配置
     */
    public static function resetConfig()
    {
        // 实现配置重置逻辑
    }
}
