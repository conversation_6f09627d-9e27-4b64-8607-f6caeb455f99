export const optimumHeaderTheme: any = {
  light: {
    "--el-header-optimum-hover-color": "var(--el-color-primary)",
    "--el-header-optimum-active-color": "var(--el-color-primary)",
    "--el-header-optimum-hover-bg-color": "#f4f4f5",
    "--el-header-optimum-active-bg-color": "var(--el-color-primary-light-8)",
    "--el-header-optimum-border-color": "var(--el-color-primary)"
  },
  inverted: {
    "--el-header-optimum-hover-color": "var(--el-color-primary)",
    "--el-header-optimum-active-color": "#ffffff",
    "--el-header-optimum-hover-bg-color": "transparent",
    "--el-header-optimum-active-bg-color": "var(--el-color-primary)",
    "--el-header-optimum-border-color": "var(--el-color-primary)"
  },
  dark: {
    "--el-header-optimum-hover-color": "var(--el-color-primary)",
    "--el-header-optimum-active-color": "var(--el-color-primary)",
    "--el-header-optimum-hover-bg-color": "var(--el-color-primary-light-9)",
    "--el-header-optimum-active-bg-color": "var(--el-color-primary-light-8)",
    "--el-header-optimum-border-color": "var(--el-color-primary)"
  }
};

export const headerTheme: any = {
  light: {
    "--el-header-bg-color": "#ffffff",
    "--el-header-text-color": "#303133",
    "--el-header-text-color-regular": "#606266"
  },
  inverted: {
    "--el-header-bg-color": "#141414",
    "--el-header-text-color": "#E5EAF3",
    "--el-header-text-color-regular": "#CFD3DC"
  },
  dark: {
    "--el-header-bg-color": "#141414",
    "--el-header-text-color": "#E5EAF3",
    "--el-header-text-color-regular": "#CFD3DC"
  }
};

export const asideTheme: any = {
  light: {
    "--el-aside-logo-text-color": "#303133"
  },
  inverted: {
    "--el-aside-logo-text-color": "#E5EAF3"
  },
  dark: {
    "--el-aside-logo-text-color": "#E5EAF3"
  }
};

export const menuTheme: any = {
  light: {
    "--el-menu-bg-color": "#ffffff",
    "--el-menu-hover-bg-color": "#f4f4f5",
    "--el-menu-active-bg-color": "var(--el-color-primary-light-8)",
    "--el-menu-text-color": "#333639",
    "--el-menu-hover-text-color": "var(--el-color-primary)",
    "--el-menu-active-text-color": "var(--el-color-primary)",
    "--el-menu-border-left-color": "var(--el-color-primary)"
  },
  inverted: {
    "--el-menu-bg-color": "#191A20",
    "--el-menu-hover-bg-color": "#000000",
    "--el-menu-active-bg-color": "var(--el-color-primary)",
    "--el-menu-text-color": "#dBd8d8",
    "--el-menu-hover-text-color": "#ffffff",
    "--el-menu-active-text-color": "#ffffff",
    "--el-menu-border-left-color": "transparent"
  },
  dark: {
    "--el-menu-bg-color": "#141414",
    "--el-menu-hover-bg-color": "var(--el-color-primary-light-9)",
    "--el-menu-active-bg-color": "var(--el-color-primary-light-8)",
    "--el-menu-text-color": "#e5eAf3",
    "--el-menu-hover-text-color": "var(--el-color-primary)",
    "--el-menu-active-text-color": "var(--el-color-primary)",
    "--el-menu-border-left-color": "var(--el-color-primary)"
  }
};
