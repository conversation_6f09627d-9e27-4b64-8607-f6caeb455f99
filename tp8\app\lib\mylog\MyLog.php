<?php
declare(strict_types=1);

namespace app\lib\mylog;

use think\facade\Log;

/**
 * 增强的日志记录类
 */
class MyLog
{
    /**
     * 当前日志通道
     * @var string|null
     */
    protected static $channel = null;

    /**
     * 默认日志通道
     * @var string
     */
    protected static $defaultChannel = 'file';

    /**
     * 设置日志通道
     * @param string $name 通道名称
     * @return self
     */
    public static function channel(string $name): self
    {
        self::$channel = $name;
        return new self();
    }

    /**
     * 记录debug日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public static function debug(string $message, array $context = []): void
    {
        self::log('debug', $message, $context);
    }

    /**
     * 记录info日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public static function info(string $message, array $context = []): void
    {
        self::log('info', $message, $context);
    }

    /**
     * 记录notice日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public static function notice(string $message, array $context = []): void
    {
        self::log('notice', $message, $context);
    }

    /**
     * 记录warning日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public static function warning(string $message, array $context = []): void
    {
        self::log('warning', $message, $context);
    }

    /**
     * 记录error日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public static function error(string $message, array $context = []): void
    {
        self::log('error', $message, $context);
    }

    /**
     * 记录critical日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public static function critical(string $message, array $context = []): void
    {
        self::log('critical', $message, $context);
    }

    /**
     * 记录alert日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public static function alert(string $message, array $context = []): void
    {
        self::log('alert', $message, $context);
    }

    /**
     * 记录emergency日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public static function emergency(string $message, array $context = []): void
    {
        self::log('emergency', $message, $context);
    }

    /**
     * 通用日志记录方法
     * @param string $level 日志级别
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    protected static function log(string $level, string $message, array $context = []): void
    {
        // 添加基础上下文信息
        $baseContext = [
            'timestamp' => date('Y-m-d H:i:s'),
            'request_id' => self::getRequestId(),
        ];

        // 处理上下文数据，确保所有数据都是可序列化的
        $processedContext = self::processContext($context);

        // 合并基础上下文和传入的上下文
        $finalContext = array_merge($baseContext, $processedContext);

        // 如果上下文不为空，将其转换为JSON并附加到消息中
        if (!empty($finalContext)) {
            $jsonContext = json_encode($finalContext, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $message = $message . ' ' . $jsonContext;
        }

        // 使用ThinkPHP的Log类记录日志，根据是否设置通道选择不同的记录方式
        if (self::$channel !== null) {
            Log::channel(self::$channel)->$level($message);
            // 使用后重置通道为默认值
            self::$channel = null;
        } else {
            Log::$level($message);
        }
    }

    /**
     * 处理上下文数据，确保所有数据都是可序列化的
     * @param array $context
     * @return array
     */
    protected static function processContext(array $context): array
    {
        $processed = [];
        foreach ($context as $key => $value) {
            if (is_array($value)) {
                $processed[$key] = self::processContext($value);
            } elseif (is_object($value)) {
                // 如果对象有 toArray 方法，使用它
                if (method_exists($value, 'toArray')) {
                    $processed[$key] = $value->toArray();
                }
                // 如果对象有 __toString 方法，使用它
                elseif (method_exists($value, '__toString')) {
                    $processed[$key] = (string)$value;
                }
                // 否则尝试将对象转换为数组
                else {
                    $processed[$key] = get_object_vars($value);
                }
            } else {
                $processed[$key] = $value;
            }
        }
        return $processed;
    }

    /**
     * 获取请求ID
     * @return string
     */
    protected static function getRequestId(): string
    {
        static $requestId = null;
        if ($requestId === null) {
            $requestId = uniqid('req_', true);
        }
        return $requestId;
    }
}
