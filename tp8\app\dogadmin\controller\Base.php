<?php

namespace app\dogadmin\controller;

use app\BaseController;
use app\dogadmin\common\ApiResponse;
use app\dogadmin\service\BaseService;
use think\Exception;
use think\facade\Route;
/**
 * 通用基础控制器，提供标准的 CRUD 接口和统一响应格式
 */
class Base extends BaseController
{
    /**
     * @var BaseService 服务层实例
     */
    protected $service;
    /**
     * @var array 请求参数
     */
    public $params = [];
    /**
     * @var array 可搜索字段
     */
    public $searchKey = [];
    /**
     * 初始化方法
     * 该方法在构造函数之后自动调用
     * @return void
     */
    protected function initialize(): void
    {
        // 子类可以重写此方法以添加自定义初始化逻辑
        $this->service = new BaseService();
        $this->params = $this->request->param();
    }

    /**
     * 获取分页列表数据
     * 支持的请求参数：
     * @param array $params {
     *     @var int    $page     当前页码
     *     @var int    $limit    每页显示数量
     *     @var string $keyword  搜索关键词（可选）
     *     @var array  $filter   过滤条件（可选）
     *     @var string $order    排序字段（可选）
     *     @var string $sort     排序方式：desc/asc（可选）
     * }
     * @return \think\Response 返回JSON格式的分页数据
     */
    public function listPage()
    {
        try {
            $params = $this->params;
            $searchKey = $this->searchKey;
            $res = $this->service->listPage($params, $searchKey);
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

    /**
     * 获取单条数据
     * 支持的请求参数：
     * @param array $params {
     *     @var int|string 主键值（必需）
     * }
     * @return \think\Response 返回JSON格式的单条数据
     */
    public function getById()
    {
        try {
            $params = $this->params;
            if (!$this->service->checkPrimaryKey($params)) {
                $pkName = $this->service->getPrimaryKeyName();
                return ApiResponse::paramError("缺少主键参数{$pkName}");
            }
            $res = $this->service->getById($params);
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

    /**
     * 删除单条数据
     * 支持的请求参数：
     * @param array $params {
     *     @var int|string 主键值（必需）
     * }
     * @return \think\Response 返回JSON格式的操作结果
     */
    public function deleteById()
    {
        try {
            $params = $this->params;
            if (!$this->service->checkPrimaryKey($params)) {
                $pkName = $this->service->getPrimaryKeyName();
                return ApiResponse::paramError("缺少主键参数{$pkName}");
            }
            $res = $this->service->deleteById($params);
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

    /**
     * 新增数据
     * 支持的请求参数：
     * @param array $params {
     *     表单提交的数据字段，具体字段由业务模型定义
     * }
     * @return \think\Response 返回JSON格式的操作结果，包含新增记录的ID
     */
    public function add()
    {
        try {
            $params = $this->params;
            $res = $this->service->add($params);
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

    /**
     * 更新数据
     * 支持的请求参数：
     * @param array $params {
     *     @var int|string 主键值（必需）
     *     其他表单提交的数据字段，具体字段由业务模型定义
     * }
     * @return \think\Response 返回JSON格式的操作结果
     */
    public function update()
    {
        try {
            $params = $this->params;
            if (!$this->service->checkPrimaryKey($params)) {
                $pkName = $this->service->getPrimaryKeyName();
                return ApiResponse::paramError("缺少主键参数{$pkName}");
            }
            $res = $this->service->update($params);
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

    /**
     * 获取排序数据
     * @param
     * @return \think\Response
     */
    public function getSorted()
    {
        try {
            $res = $this->service->getSorted();
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

    /**
     * 更新状态
     * @param
     * @return \think\Response
     */
    public function updateStatus()
    {
        try {
            $params = $this->params;
            if (!$this->service->checkPrimaryKey($params)) {
                $pkName = $this->service->getPrimaryKeyName();
                return ApiResponse::paramError("缺少主键参数{$pkName}");
            }
            $res = $this->service->updateStatus($params);
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

    /**
     * 批量删除
     * @param
     * @return \think\Response
     */
    public function batchDelete()
    {
        try {
            $params = $this->params;
            if (empty($params['ids']) || !is_array($params['ids'])) {
                $pkName = $this->service->getPrimaryKeyName();
                return ApiResponse::paramError("缺少批量主键参数{$pkName}s");
            }
            $res = $this->service->batchDelete($params);
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

    /**
     * 自动注册CRUD路由
     * 此方法会自动注册以下路由：
     * GET请求：
     * - /listPage    获取分页列表数据
     * - /getById     获取单条数据
     * - /getSorted   获取排序数据
     * 
     * POST请求：
     * - /deleteById   删除单条数据
     * - /update      更新数据
     * - /add         新增数据
     * - /updateStatus 更新状态
     * - /batchDelete  批量删除
     * 
     * 使用示例：
     * ```php
     * // 在路由文件中：
     * use app\dogadmin\controller\Base;
     * Base::registerCrudRoutes('sysUser', 'app\dogadmin\controller\SysUser', [AdminCheck::class]);
     * ```
     * 
     * @param string $prefix 路由前缀，例如 'sysUser'
     * @param string $controller 控制器完整类名，例如 'app\dogadmin\controller\SysUser'
     * @param array $middleware 中间件数组，例如 [AdminCheck::class]
     */
    public static function registerCrudRoutes(string $prefix, string $controller, array $middleware = [])
    {
        Route::group($prefix, function () use ($controller) {
            // GET请求路由
            Route::get('/listPage', [$controller, 'listPage']);
            Route::get('/getById', [$controller, 'getById']);
            Route::get('/getSorted', [$controller, 'getSorted']);
            
            // POST请求路由
            Route::post('/deleteById', [$controller, 'deleteById']);
            Route::post('/update', [$controller, 'update']);
            Route::post('/add', [$controller, 'add']);
            Route::post('/updateStatus', [$controller, 'updateStatus']);
            Route::post('/batchDelete', [$controller, 'batchDelete']);
        })->middleware($middleware);
    }
}