<?php

namespace app\common_api\listener;

use app\common_api\event\UserRegistered;
use app\lib\mylog\MyLog;


/**
 * 用户注册成功事件监听器
 */
class UserRegisteredListener
{
    /**
     * 处理用户注册成功事件
     *
     * @param UserRegistered $event
     * @return void
     */
    public function handle(UserRegistered $event): void
    {
        // 记录用户注册日志
        MyLog::info('新用户注册成功', [
            'user_id' => $event->userId,
            'register_type' => $event->registerType,
            'register_time' => date('Y-m-d H:i:s', $event->registerTime),
            'user_data' => $event->userData
        ]);

        // TODO: 在这里可以添加更多注册成功后的操作
        // 例如：
        // 1. 发送欢迎邮件
        // 2. 初始化用户数据
        // 3. 发放新用户奖励
        // 4. 触发其他相关业务逻辑
    }
}
