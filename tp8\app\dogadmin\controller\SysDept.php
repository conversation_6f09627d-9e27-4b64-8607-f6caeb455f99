<?php

namespace app\dogadmin\controller;

use app\dogadmin\common\ApiResponse;
use app\dogadmin\service\SysDeptService;
use app\dogadmin\service\SysRoleDeptService;


class SysDept extends Base
{
    /**
     * @var SysDeptService
     */
    protected $service;
    public function initialize(): void
    {
        $this->service = new SysDeptService();
        $this->params = $this->request->param();
    }

    /**
     * 获取部门级联列表
     * 
     * @return \think\Response
     */
    public function cascaderList()
    {
        $res = $this->service->cascaderList();
        return ApiResponse::success($res, '获取部门列表成功');
    }

    public function listDeptNormal()
    {
        $list = $this->service->listDeptNormal();
        $res['dept_list'] = [];
        $res['spread_list'] = [];

        // 处理列表数据
        if (!empty($list)) {
            foreach ($list as $item) {
                // 检查isSpread属性
                if (isset($item['isSpread']) && $item['isSpread'] == 0) {
                    // 如果isSpread=0，添加到spreadList
                    $res['spread_list'][] = $item;
                } else {
                    // 否则添加到menuList
                    $res['dept_list'][] = $item;
                }
            }
        }

        return ApiResponse::success($res);
    }

    public function listDeptIdsByRoleId()
    {
        $params = $this->params;
        $roleId = $params['id'];
        $roleDeptService = new SysRoleDeptService();
        $deptIds = $roleDeptService->listDeptIdsByRoleId($roleId);
        return ApiResponse::success($deptIds);
    }

    public function saveRoleDept()
    {
        $params = $this->params;
        $sysRoleDeptService = new SysRoleDeptService();
        $res = $sysRoleDeptService->saveRoleDept($params);
        return ApiResponse::success($res);
    }


}