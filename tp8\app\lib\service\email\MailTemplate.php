<?php

namespace app\lib\service\email;

class MailTemplate
{
    /**
     * @var string 模板目录
     */
    protected $templateDir;

    /**
     * @var array 模板变量
     */
    protected $vars = [];

    public function __construct($templateDir = null)
    {
        $this->templateDir = $templateDir ?: config('mail.template_dir', '');
    }

    /**
     * 设置模板目录
     * @param string $dir
     * @return $this
     */
    public function setTemplateDir($dir)
    {
        $this->templateDir = rtrim($dir, '/');
        return $this;
    }

    /**
     * 分配模板变量
     * @param string|array $name
     * @param mixed $value
     * @return $this
     */
    public function assign($name, $value = null)
    {
        if (is_array($name)) {
            $this->vars = array_merge($this->vars, $name);
        } else {
            $this->vars[$name] = $value;
        }
        return $this;
    }

    /**
     * 渲染模板
     * @param string $template 模板文件名
     * @return string
     */
    public function render($template)
    {
        $templatePath = $this->templateDir . '/' . ltrim($template, '/');

        if (!file_exists($templatePath)) {
            throw new \RuntimeException("Template file not found: {$templatePath}");
        }

        extract($this->vars);
        ob_start();
        include $templatePath;
        $content = ob_get_clean();

        return $content;
    }

    /**
     * 获取模板内容
     * @param string $template
     * @return string
     */
    public function fetch($template)
    {
        return $this->render($template);
    }
}
