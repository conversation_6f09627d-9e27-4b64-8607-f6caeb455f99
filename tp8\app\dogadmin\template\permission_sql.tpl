-- 添加{{table_comment}}菜单
{{parent_menu_sql}}

-- 添加{{table_comment}}主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `name`, `component`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('{{table_comment}}', {{parent_id}}, '2', '/{{module_name}}/{{perm_name}}/index', '{{perm_name}}Page', '{{module_name}}/{{perm_name}}/index', 'Menu', '{{module_name}}:{{perm_name}}:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的菜单ID
SET @menuId = LAST_INSERT_ID();

-- 添加{{table_comment}}按钮权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `auth`, `status`, `is_hide`,`create_time`, `update_time`) VALUES
('查询', @menuId, '3', '{{module_name}}:{{perm_name}}:listPage', '1','0', NOW(), NOW()),
('获取详情', @menuId, '3', '{{module_name}}:{{perm_name}}:getById', '1','0', NOW(), NOW()),
('获取排序', @menuId, '3', '{{module_name}}:{{perm_name}}:getSorted', '1','0', NOW(), NOW()),
('新增', @menuId, '3', '{{module_name}}:{{perm_name}}:add', '1','0', NOW(), NOW()),
('修改', @menuId, '3', '{{module_name}}:{{perm_name}}:update', '1','0', NOW(), NOW()),
('删除', @menuId, '3', '{{module_name}}:{{perm_name}}:deleteById', '1','0', NOW(), NOW()),
('批量删除', @menuId, '3', '{{module_name}}:{{perm_name}}:batchDelete', '1','0', NOW(), NOW()),
('更新状态', @menuId, '3', '{{module_name}}:{{perm_name}}:updateStatus', '1','0', NOW(), NOW());