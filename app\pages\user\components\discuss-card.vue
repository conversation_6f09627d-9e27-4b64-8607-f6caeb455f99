<template>
	<view class="container" @click="toPage(`/pages/guide/comment?guideId=${info.guideId}`)">
		<view class="user-info">
			<image class="avatar" :src="info.user.avatarUrl"></image>
			<view class="user-details">
				<text class="username">{{info.user.nickname}}</text>
				<text class="user-group">
					<text class="platform" v-if="info.platform">{{info.platform.value}}{{info.region}}</text>
					<text class="gameId" v-if="info.gameId" @click="copyGameId($event,info.gameId)">{{info.gameId}}</text>
				</text>
			</view>
			<!-- <view class="follow-btn">关注</view> -->
		</view>
		<view class="post-content">
			<text class="post-text">
				<!-- <zero-markdown-view :markdown="info.content"></zero-markdown-view> -->
				{{info.content}}
			</text>
			<view class="images" v-if="imgList.length > 0">
				<image class="image" v-for="(item,index) in imgList" :src="item" @click="viewImg(index)"></image>
			</view>
		</view>
		<view class="time">{{showTime(info.updateTime)}}</view>
		<view class="type-text" :class="{'type-send':isUser}">{{isUser ? '发出' : '收到'}}</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex';
	import thorui from '@/components/common/tui-clipboard/tui-clipboard.js';
	export default {
		props: {
			info: {
				type: [Object, String],
				default: () => {
					return {

					}
				}
			}
		},
		computed: {
			...mapGetters(['userInfo']),
			isUser(){
				return this.userInfo.user_id == this.info.userId;
			}
		},
		data() {
			return {
				imgList: []
			}
		},
		watch: {
			// 监听父组件传入的默认值
			'info.imgUrl': {
				immediate: true,
				handler(newVal) {
					if (newVal) {
						this.imgList = newVal.split(',');
					} else {
						this.imgList = [];
					}
				}
			}
		},
		methods: {
			viewImg(currentIndex) {
				uni.previewImage({
					current: currentIndex, // 当前显示图片的索引
					urls: this.imgList, // 所有图片的URL数组
					indicator: "number", // 显示页码指示器
					loop: true // 支持循环预览
				});
			},
			copyGameId(event, data) {
				thorui.getClipboardData(data, (res) => {
					// #ifdef H5 || MP-ALIPAY
					if (res) {
						//复制成功
						console.log('复制成功')
					} else {
						//复制失败
						console.log('复制失败')
					}
					// #endif
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					})
				}, event)
			},
			commentInfo(exchangeId) {
				this.$emit('commentInfo', exchangeId)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
		padding: 28rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.user-info {
		display: flex;
		align-items: center;
		// padding: 16px 0;
		background-color: #fff;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 16rpx;
	}

	.user-details {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.username {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 6rpx;
	}

	.user-group {
		font-size: 30rpx;
		color: #888;
		font-weight: bold;

		.platform {
			font-size: 26rpx;
			background-color: #07c160;
			padding: 4rpx 10rpx;
			color: #fff;
			border-radius: 8rpx;
		}

		.gameId {
			display: inline-block;
			margin-left: 12rpx;
			color: #007aff;
		}
	}

	.follow-btn {
		background-color: #007aff;
		color: #fff;
		padding: 4px 8px;
		border-radius: 4px;
		font-size: 28rpx;
	}

	.post-content {
		padding: 10rpx 0;
		background-color: #fff;
	}

	.post-text {
		font-size: 28rpx;
		color: #333;
		// margin-bottom: 16px;
	}

	.images {
		display: flex;
		flex-wrap: wrap;
		// justify-content: space-between;
		padding: 20rpx;

		.image {
			width: 150rpx;
			height: 150rpx;
			display: block;
			margin-right: 10rpx;
			margin-bottom: 10rpx;
			// border: 1px solid #333;
		}
	}



	.actions {
		display: flex;
		justify-content: space-around;
		// justify-content: flex-end;
		background-color: #fff;
		padding-top: 10rpx;
	}

	.action-btn {
		width: 33%;
		// flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
		font-size: 28rpx;
		color: #333;
		border: none;
		// padding: 16rpx 32rpx;
		border-radius: 8rpx;

		.txt {
			padding-left: 12rpx;
		}
	}

	.time {
		text-align: right;
		color: #999;
		font-size: 28rpx;
	}

	.type-text {
		font-size: 28rpx;
		background-color: #07c160;
		display: inline-block;
		color: #fff;
		padding: 6rpx 12rpx;
		border-radius: 12rpx;
		position: absolute;
		top: 20rpx;
		right: 20rpx;
	}
	.type-send{
		background-color: #007aff;
	}
</style>