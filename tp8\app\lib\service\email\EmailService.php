<?php

namespace app\lib\service\email;

use <PERSON>HPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

class EmailService
{
    /**
     * @var array 邮件配置
     */
    protected $config;

    public function __construct()
    {
        // 初始化邮件配置
        $this->config = [
            'host'     => config('mail.host'),
            'port'     => config('mail.port'),
            'username' => config('mail.username'),
            'password' => config('mail.password'),
            'from'     => config('mail.from'),
            'fromName' => config('mail.fromName'),
            'charset'  => 'UTF-8',
            'smtpSecure' => 'ssl',
        ];
    }

    /**
     * 发送邮件
     * @param string|array $to 收件人邮箱(多个收件人使用数组)
     * @param string $subject 邮件主题
     * @param string $content 邮件内容
     * @param bool $isHtml 是否HTML格式
     * @param array $attachments 附件列表
     * @return bool
     */
    public function send($to, $subject, $content, $isHtml = true, $attachments = [])
    {
        try {
            $mail = new PHPMailer(true);

            // 服务器配置
            $mail->isSMTP();
            $mail->Host = $this->config['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $this->config['username'];
            $mail->Password = $this->config['password'];
            $mail->SMTPSecure = $this->config['smtpSecure'];
            $mail->Port = $this->config['port'];
            $mail->CharSet = $this->config['charset'];

            // 发件人
            $mail->setFrom($this->config['from'], $this->config['fromName']);

            // 收件人
            if (is_array($to)) {
                foreach ($to as $email) {
                    $mail->addAddress($email);
                }
            } else {
                $mail->addAddress($to);
            }

            // 附件
            foreach ($attachments as $attachment) {
                $mail->addAttachment($attachment['path'], $attachment['name'] ?? '');
            }

            // 内容
            $mail->isHTML($isHtml);
            $mail->Subject = $subject;
            $mail->Body = $content;

            if (!$isHtml) {
                $mail->AltBody = strip_tags($content);
            }

            return $mail->send();
        } catch (Exception $e) {
            // 记录错误日志
            trace('邮件发送失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 发送HTML邮件
     * @param string|array $to 收件人邮箱
     * @param string $subject 邮件主题
     * @param string $htmlContent HTML内容
     * @param array $attachments 附件列表
     * @return bool
     */
    public function sendHtml($to, $subject, $htmlContent, $attachments = [])
    {
        return $this->send($to, $subject, $htmlContent, true, $attachments);
    }

    /**
     * 发送纯文本邮件
     * @param string|array $to 收件人邮箱
     * @param string $subject 邮件主题
     * @param string $textContent 纯文本内容
     * @param array $attachments 附件列表
     * @return bool
     */
    public function sendText($to, $subject, $textContent, $attachments = [])
    {
        return $this->send($to, $subject, $textContent, false, $attachments);
    }
}
