<?php

namespace app\robot\service;

use app\robot\model\RobotAiCharacterDictModel;
use app\dogadmin\service\BaseService;
/**
 * RobotAiCharacterDict 服务类
 */
class RobotAiCharacterDictService extends BaseService
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new RobotAiCharacterDictModel();
    }
    
    // 所有基础CRUD方法均继承自BaseService
    // 如需自定义方法，请在此处添加
}