<?php

namespace app\robot\service;

use app\robot\model\RobotAiCharacterDictModel;
use app\dogadmin\service\BaseService;
/**
 * RobotAiCharacterDict 服务类
 */
class RobotAiCharacterDictService extends BaseService
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new RobotAiCharacterDictModel();
    }

    /**
     * 获取所有启用状态的字典数据
     * @return array
     */
    public function getAllActiveDict(): array
    {
        $data = $this->model
            ->where('status', '1')
            ->order('dict_type asc, sorted asc')
            ->select()
            ->toArray();

        // 按字典类型分组
        $result = [];
        foreach ($data as $item) {
            $result[$item['dict_type']][] = $item;
        }

        return $result;
    }

    /**
     * 根据字典类型获取启用状态的字典数据
     * @param string $dictType 字典类型
     * @return array
     */
    public function getActiveDictByType(string $dictType): array
    {
        return $this->model
            ->where('status', '1')
            ->where('dict_type', $dictType)
            ->order('sorted asc')
            ->select()
            ->toArray();
    }

    // 所有基础CRUD方法均继承自BaseService
    // 如需自定义方法，请在此处添加
}