<?php

namespace app\dogadmin\service;

use app\dogadmin\model\SysDictDataModel;

class SysDictDataService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysDictDataModel();
    }

    //根据type获取dict
    public function getDictDataByType(array $params){
        $where = [
            ['dict_type','=',$params['dict_type']]
        ];
        $order = [
            'sorted'=>'DESC'
        ];
        $res = $this->model->where($where)->order($order)->select()->toArray();
        return $res;
    }

}