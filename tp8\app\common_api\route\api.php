<?php
use think\facade\Route;
use app\common_api\middleware\ApiCheck;

Route::group('commonUser', function () {
    Route::post('getTokenByPwd', 'app\common_api\controller\CommonUser@getTokenByPwd');
    Route::post('emailRegister', 'app\common_api\controller\CommonUser@emailRegister');
    Route::post('sendEmailCode', 'app\common_api\controller\CommonUser@sendEmailCode');
    Route::post('resetPassword', 'app\common_api\controller\CommonUser@resetPassword');

    Route::post('testEmailRegister', 'app\common_api\controller\TestEventController@testEmailRegister');
});

Route::group('commonUser', function () {
    Route::post('getUserInfo', 'app\common_api\controller\CommonUser@getUserInfo');
    Route::post('updateNickname', 'app\common_api\controller\CommonUser@updateNickname');
    Route::post('uploadAvatar', 'app\common_api\controller\CommonUser@uploadAvatar');
    Route::post('changePassword', 'app\common_api\controller\CommonUser@changePassword');
})->middleware([ApiCheck::class]);