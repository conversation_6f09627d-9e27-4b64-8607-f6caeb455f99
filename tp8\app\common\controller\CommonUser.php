<?php

namespace app\common\controller;

use app\common\service\CommonUserService;
use app\common\common\ApiResponse;
use app\dogadmin\controller\Base;

/**
 * 通用管理 控制器
 */
class CommonUser extends Base
{
    /**
     * @var CommonUserService
     */
    protected $service;
    
    /**
     * 初始化方法
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = new CommonUserService();
        $this->params = $this->request->param();
        $this->searchKey = [
            ['username' => 'like'],
            ['nickname' => 'like'],
            ['phone' => 'like'],
            ['game_id' => 'like'],
        ];
    }
    
    // 所有基础CRUD方法均继承自Base控制器
     // 如需自定义方法，请在此处添加
}