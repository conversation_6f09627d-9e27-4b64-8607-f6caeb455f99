<template>
	<view></view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		mounted() {
			this.loadAd();
		},
		methods:{
			loadAd(){
				// 若在开发者工具中无法预览广告，请切换开发者工具中的基础库版本
				// 在页面中定义插屏广告
				let interstitialAd = null
				
				// 在页面onLoad回调事件中创建插屏广告实例
				if (wx.createInterstitialAd) {
				  interstitialAd = wx.createInterstitialAd({
				    adUnitId: 'adunit-380f64b717254290'
				  })
				  interstitialAd.onLoad(() => {})
				  interstitialAd.onError((err) => {
				    console.error('插屏广告加载失败', err)
				  })
				  interstitialAd.onClose(() => {})
				}
				
				// 在适合的场景显示插屏广告
				if (interstitialAd) {
				  interstitialAd.show().catch((err) => {
				    console.error('插屏广告显示失败', err)
				  })
				}
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>