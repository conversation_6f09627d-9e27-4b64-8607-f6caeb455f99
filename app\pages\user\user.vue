<template>
	<view class="profile-container">
		<!-- 头部区域 -->
		<view class="header" @click="showTips">
			<view class="avatar-wrap">
				<image class="avatar"
					:src="userInfo.avatar_url ? userInfo.avatar_url: 'https://mwdl.miwudalu.com/headicon/head01.png'"
					mode="cover"></image>
			</view>
			<view class="info-wrap">
				<view class="nickname">{{userInfo.nickname}}</view>
				<view>
					<view class="id">ID：{{userInfo.user_id}}</view>
					<!-- <view class="group">微1区</view> -->
				</view>
			</view>
			<view class="set-btn">
				<tui-icon name="setup-fill"></tui-icon>
			</view>
		</view>

		<!-- 功能列表 -->
		<view class="functions">
			<view class="function-item" v-for="(item,index) in functions" :key="index" @click="goToPage(item)">
				<image class="icon" :src="item.icon" mode="aspectFit"></image>
				<text class="label">{{ item.label }}</text>
				<text class="label-new" v-if="item.new > 0">{{item.new}}</text>
			</view>
		</view>

		<!-- 底部版权 -->
		<view class="footer">
			<text>© 迷雾小助手 保留所有权利 </text>
		</view>
	</view>
</template>

<script>
	import { mapGetters,mapActions } from 'vuex';

	export default {
		
		computed: {
			...mapGetters(['userInfo','exchangeCommentNum'])
		},
		data() {
			return {
				functions: [{
						icon: require('@/static/images/user/jl.png'),
						label: '我的问答',
						path: '/pages/user/exchange',
						type: 'navigateTo',
						new: 0,
					},
					{
						icon: require('@/static/images/user/pl.png'),
						label: '问答评论',
						path: '/pages/user/comment',
						type: 'navigateTo',
						new: 0,
					},
					{
						icon: require('@/static/images/user/sc.png'),
						label: '我的收藏',
						path: '/pages/user/collect',
						type: 'navigateTo',
						new: 0,
					},
					{
						icon: require('@/static/images/user/tl.png'),
						label: '攻略评论',
						path: '/pages/user/discuss',
						type: 'navigateTo',
						new: 0,
					},
					{
						icon: require('@/static/images/user/gy.png'),
						label: '关于我们',
						path: '/pages/fab/about',
						type: 'navigateTo',
						new: 0,
					},
					// { icon: 'https://mwdl.miwudalu.com/headicon/head03.png', label: '退出登录' },
				],
			};
		},
		onLoad() {
			
		},
		onShow() {
			this.initData();
		},
		methods: {
			...mapActions({
				getCommentCount:'getCommentCount'
			}),
			initData(){
				this.getCommentCount().then(res=>{
					let functions = this.functions;
					functions[1].new = this.exchangeCommentNum;
				});
			},
			showTips() {
				uni.showToast({
					title: '无法修改，等待后续开放',
					icon: 'none'
				})
			},
			handleFunction(item) {
				uni.showToast({
					title: `点击了${item.label}`,
					icon: 'none',
				});
				uni.navigateTo({
					path
				})
				// 根据需要添加跳转逻辑
			},
			goToPage(item) {
				let type = item.type;
				let path = item.path;
				uni[type]({
					url: path
				})
			}
		},
	};
</script>

<style lang="scss" scoped>
	.profile-container {
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;

		.header {
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 30rpx 60rpx;
			background-color: #ffffff;

			.info-wrap {
				display: flex;
				flex-direction: column;
				align-items: center;
				text-align: center;

				.nickname {
					font-size: 36rpx;
					color: #333333;
					font-weight: bold;
				}

				.id {
					font-size: 26rpx;
					padding-top: 6rpx;
					color: #999999;
				}

				.group {
					font-size: 30rpx;
					padding-top: 6rpx;
					color: #666;
					font-weight: bold;
				}
			}

			.avatar {
				width: 150rpx;
				height: 150rpx;
				border-radius: 50%;
				// margin-bottom: 10px;
			}

			.set-btn {
				position: absolute;
				top: 20rpx;
				right: 20rpx;

				.icon {
					width: 50rpx;
					height: 50rpx;
				}
			}

		}

		.functions {
			flex: 1;
			padding: 20rpx;
			overflow-y: auto;

			.function-item {
				display: flex;
				flex-direction: row;
				align-items: center;
				padding: 30rpx;
				margin-bottom: 20rpx;
				background-color: #ffffff;
				border-radius: 16rpx;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				position: relative;
				// background: linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #FFFFFF;
				// box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.1),0px 1px 3px 0px rgba(0, 0, 0, 0.1);
				// border-radius: 14rpx;

				.icon {
					width: 60rpx;
					height: 60rpx;
					margin-right: 30rpx;
				}

				.label {
					font-size: 32rpx;
					color: #333333;
				}
				
				.label-new{
					display: block;
					width: 44rpx;
					height: 44rpx;
					text-align: center;
					line-height: 44rpx;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					right: 40rpx;
					color: #ffffff;
					background-color: #eb0909;
					// padding: 0rpx 10rpx;
					font-size: 30rpx;
					border-radius: 50%;
				}
			}
		}

		.footer {
			padding: 20rpx;
			text-align: center;
			font-size: 24rpx;
			color: #999999;
		}
	}
</style>