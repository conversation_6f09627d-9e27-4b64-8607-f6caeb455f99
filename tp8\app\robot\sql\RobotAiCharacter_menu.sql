-- 添加机器人菜单
-- 添加机器人目录
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('机器人', 0, '1', 'robotAiCharacter', 'FolderOpened','robot:robotAiCharacter:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的目录ID
SET @parentId = LAST_INSERT_ID();

-- 添加机器人主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `name`, `component`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('机器人', @parentId, '2', '/robot/robotAiCharacter/index', 'robotAiCharacterPage', 'robot/robotAiCharacter/index', 'Menu', 'robot:robotAiCharacter:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的菜单ID
SET @menuId = LAST_INSERT_ID();

-- 添加机器人按钮权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `auth`, `status`, `is_hide`,`create_time`, `update_time`) VALUES
('查询', @menuId, '3', 'robot:robotAiCharacter:listPage', '1','0', NOW(), NOW()),
('获取详情', @menuId, '3', 'robot:robotAiCharacter:getById', '1','0', NOW(), NOW()),
('获取排序', @menuId, '3', 'robot:robotAiCharacter:getSorted', '1','0', NOW(), NOW()),
('新增', @menuId, '3', 'robot:robotAiCharacter:add', '1','0', NOW(), NOW()),
('修改', @menuId, '3', 'robot:robotAiCharacter:update', '1','0', NOW(), NOW()),
('删除', @menuId, '3', 'robot:robotAiCharacter:deleteById', '1','0', NOW(), NOW()),
('批量删除', @menuId, '3', 'robot:robotAiCharacter:batchDelete', '1','0', NOW(), NOW()),
('更新状态', @menuId, '3', 'robot:robotAiCharacter:updateStatus', '1','0', NOW(), NOW());