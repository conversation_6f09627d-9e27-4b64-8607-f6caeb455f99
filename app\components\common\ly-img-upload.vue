<template>
	<view>
		<view class="img-upload">
			<view class="tui-input__label">{{labelText}}</view>
			<img-upload-diy :value="picArr" :formData="qiniuData" :serverUrl="serverUrl" @complete="result" @remove="remove"
				:imageFormat="imageFormat" :size="size" :extra="extra" :limit="picLimit"></img-upload-diy>
			<view class="tips" v-if="imgArr.length == 0 && picArr.length == 0">{{tipsText}}</view>
		</view>
	</view>
</template>

<script>
	import { getQiniuToken } from '@/api/common.js'
	import imgUploadDiy from './img-upload-diy/img-upload-diy.vue'
	export default {
		components: {
			imgUploadDiy
		},
		props: {
			picArr: {
				type: Array,
				default () {
					return []
				}
			},
			picLimit: {
				type: Number,
				default: 9
			},
			labelText: {
				type: String,
				default: '图片上传'
			},
			tipsText: {
				type: String,
				default: '上传一些图片可以让你的信息更受关注'
			},
			extra: {
				type: Object,
				default () {
					return {
						picUrl: 'https://pic.duohuiyu.com/',
						suffix: '-dhyt'
					}
				}
			}
		},
		data() {
			return {
				serverUrl: 'https://upload-z2.qiniup.com',
				imageFormat: ['jpg', 'png', 'gif', 'jpeg'],
				qiniuData: {
					token: "",
				},
				size: 10,
				imgArr: []
			}
		},
		mounted() {
			this._getToken();
		},
		methods: {
			_getToken() {
				getQiniuToken({}).then(res => {
					this.qiniuData = {
						token: res.data
					}
				})
			},
			result(e) {
				this.imgArr = e.imgArr;
				this.$emit('changeImgArr', e.imgArr || [])
			},
			remove(e) {
				// console.log('result', e);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.img-upload {
		display: flex;
		align-items: center;
		padding: 26rpx 30rpx;
	}

	.tui-input__label {
		padding-right: 12rpx;
		/* #ifndef APP-NVUE */
		flex-shrink: 0;
		/* #endif */
		font-size: 32rpx;
		min-width: 140rpx;
	}

	.tips {
		color: #FF0000;
		padding-left: 20rpx;
	}
</style>