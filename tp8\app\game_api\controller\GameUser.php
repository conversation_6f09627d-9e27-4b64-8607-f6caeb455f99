<?php

namespace app\game_api\controller;

use app\game_api\service\GameUserService;
use think\facade\Request;

class GameUser extends Base
{
    protected $gameUserService;
    
    public function __construct()
    {
        $this->gameUserService = new GameUserService();
    }
    
    
    /**
     * 根据用户ID获取游戏用户信息
     * @return \think\response\Json
     */
    public function getGameUserByUserId()
    {
        $userId = request()->user_id;;
        
        if (empty($userId)) {
            return fRet('用户ID不能为空');
        }
        
        $gameUser = $this->gameUserService->getOrCreateGameUser($userId);
        return sRet($gameUser);
    }

    
    /**
     * 创建角色
     * @return \think\response\Json
     */
    public function createCharacter()
    {
        $userId = request()->user_id;
        $data = Request::param();

        if (empty($userId)) {
            return fRet('用户ID不能为空');
        }

        // 验证必要字段
        if (empty($data['nickname'])) {
            return fRet('角色名称不能为空');
        }

        if (empty($data['gender'])) {
            return fRet('性别不能为空');
        }

        if (empty($data['avatar_url'])) {
            return fRet('头像不能为空');
        }

        if (empty($data['talent']) || !is_array($data['talent'])) {
            return fRet('天赋数据不能为空');
        }

        if (empty($data['element']) || !is_array($data['element'])) {
            return fRet('灵根数据不能为空');
        }

        try {
            $result = $this->gameUserService->createCharacter($userId, $data);

            if ($result === false) {
                return fRet('角色创建失败');
            }

            return sRet($result, '角色创建成功');

        } catch (\Exception $e) {
            return fRet('角色创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新角色基本信息
     * @return \think\response\Json
     */
    public function updateCharacterBasic()
    {
        $userId = request()->user_id;
        $data = Request::param();

        if (empty($userId)) {
            return fRet('用户ID不能为空');
        }

        // 只允许更新特定字段
        $allowedFields = ['nickname', 'gender', 'avatar_url'];
        $updateData = [];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        if (empty($updateData)) {
            return fRet('没有可更新的数据');
        }

        try {
            $result = $this->gameUserService->updateGameUser($userId, $updateData);

            if ($result === false) {
                return fRet('更新失败');
            }

            return sRet('更新成功');

        } catch (\Exception $e) {
            return fRet('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 更新游戏用户信息
     * @return \think\response\Json
     */
    public function updateGameUser()
    {
        $userId = request()->user_id;;
        $data = Request::param();

        // 移除不需要更新的字段
        unset($data['user_id']);

        if (empty($userId)) {
            return fRet('用户ID不能为空');
        }

        if (empty($data)) {
            return fRet('更新数据不能为空');
        }

        $result = $this->gameUserService->updateGameUser($userId, $data);

        if ($result === false) {
            return fRet('更新失败');
        }

        return sRet('更新成功');
    }

    /**
     * 更新玩家数据（用于前端修炼逻辑）
     * @return \think\response\Json
     */
    public function updatePlayerData()
    {
        $userId = request()->user_id;
        $playerData = Request::param();

        if (empty($userId)) {
            return fRet('用户ID不能为空');
        }

        if (empty($playerData)) {
            return fRet('玩家数据不能为空');
        }

        try {
            $result = $this->gameUserService->updatePlayerData($userId, $playerData);

            if ($result['code'] !== 200) {
                return fRet($result['msg']);
            }

            return sRet($result['msg'], $result['data']);

        } catch (\Exception $e) {
            return fRet('数据更新失败：' . $e->getMessage());
        }
    }
}