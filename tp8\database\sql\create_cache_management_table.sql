-- 创建缓存管理表
CREATE TABLE IF NOT EXISTS `cache_management` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '缓存名称',
  `path` varchar(255) NOT NULL COMMENT '文件路径',
  `version` varchar(50) NOT NULL COMMENT '版本号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用 1启用',
  `desc` varchar(255) DEFAULT NULL COMMENT '描述',
  `sorted` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存管理表';

-- 可选：添加示例数据
INSERT INTO `cache_management` (`name`, `path`, `version`, `status`, `desc`, `sorted`, `create_time`)
VALUES
('equip_data', 'json/v1234567890/equip_data_v1234567890.json', '1234567890', 1, '装备数据缓存', 1, UNIX_TIMESTAMP());
