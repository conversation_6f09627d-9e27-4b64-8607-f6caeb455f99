<template>
  <view class="moment-header-container">
    <!-- 背景图片 -->
    <view class="header-background">
      <image 
        class="bg-image" 
        :src="userInfo.backgroundImage || '/static/images/default-bg.jpg'" 
        mode="aspectFill"
        @tap="changeBackground"
      />
      <view class="bg-overlay"></view>
    </view>
    
    <!-- 用户信息 -->
    <view class="user-profile">
      <view class="user-info">
        <text class="user-name">{{ userInfo.name }}</text>
      </view>
      <image 
        class="user-avatar" 
        :src="userInfo.avatar" 
        mode="aspectFill"
        @tap="viewProfile"
      />
    </view>
    
    <!-- 相机按钮 -->
    <view class="camera-btn" @tap="showPublishOptions">
      <text class="camera-icon">📷</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MomentHeader',
  props: {
    userInfo: {
      type: Object,
      required: true
    }
  },
  methods: {
    changeBackground() {
      this.$emit('changeBackground')
    },
    
    viewProfile() {
      this.$emit('viewProfile')
    },
    
    showPublishOptions() {
      this.$emit('showPublishOptions')
    }
  }
}
</script>

<style scoped>
.moment-header-container {
  position: relative;
  height: 400rpx;
  margin-bottom: 20rpx;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.bg-image {
  width: 100%;
  height: 100%;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.3));
}

.user-profile {
  position: absolute;
  bottom: 32rpx;
  left: 32rpx;
  right: 32rpx;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 1.0);
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  background-color: #f0f0f0;
}

.camera-btn {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-icon {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.9);
}
</style>
