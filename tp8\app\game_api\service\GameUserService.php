<?php

namespace app\game_api\service;

use app\game_api\model\GameUserModel;
use think\facade\Db;
use app\game_api\service\GamePlayerBaseService;
use app\game\model\GameLevelModel;

class GameUserService extends BaseService
{
    public function __construct(){
        $this->model = new GameUserModel();
    }

    /**
     * 获取或创建游戏用户
     * @param int $userId 用户ID
     * @return array 游戏用户信息
     */
    public function getOrCreateGameUser(int $userId)
    {
        // 查询用户是否存在
        $gameUser = $this->getGameUserByUserId($userId);

        // 如果用户不存在，则创建新用户
        if (!$gameUser) {
            $gameUser = $this->createGameUser($userId);
        }

        // 转换为playerJson格式
        return $gameUser;
    }

    /**
     * 创建游戏用户
     * @param int $userId 用户ID
     * @return array 新创建的游戏用户信息
     */
    public function createGameUser(int $userId)
    {
        // 获取默认基础属性
        $baseAttributes = $this->getDefaultBaseAttributes();
        
        // 准备用户数据
        $data = [
            'user_id' => $userId,
            'base' => $baseAttributes,
            'talent' => [],
            'element' => [],
            'resource' => [],
            'equipment' => [],
            'skill_combat' => [],
            'sorted' => 1,
            'status' => '1',
        ];
        
        // 添加创建和更新时间
        $data = $this->addTime($data, ['create_time', 'update_time']);
        
        // 创建用户并返回
        $id = $this->model->insertGetId($data);
        return $this->model->find($id);
    }
    
    /**
     * 获取默认基础属性
     * @return array 默认基础属性数组
     */
    public function getDefaultBaseAttributes()
    {
        // 使用GamePlayerBaseService获取默认属性
        $gamePlayerBaseService = new GamePlayerBaseService();
        return $gamePlayerBaseService->getDefaultBaseAttributes();
    }
    
    /**
     * 根据用户ID获取游戏用户信息
     * @param int $userId 用户ID
     * @return array|null 游戏用户信息
     */
    public function getGameUserByUserId(int $userId)
    {
        $where = [
            ['user_id', '=', $userId],
            ['status', '=', '1'],
        ];
        $user = $this->model->where($where)->findOrEmpty();
        if ($user->isEmpty()) {
            return false;
        }else{
            return $user->toArray();
        }
    }
    
    /**
     * 更新游戏用户信息
     * @param int $userId 用户ID
     * @param array $data 更新数据
     * @return bool 更新结果
     */
    public function updateGameUser(int $userId, array $data)
    {
        // 添加更新时间
        $data = $this->addTime($data);

        $where = [
            ['user_id', '=', $userId],
            ['status', '=', '1'],
        ];
        // 更新用户数据
        return $this->model->where($where)->update($data);
    }
    
    /**
     * 创建角色（更新现有用户的角色信息）
     * @param int $userId 用户ID
     * @param array $characterData 角色数据
     * @return array|false 创建结果
     */
    public function createCharacter(int $userId, array $characterData)
    {
        // 检查用户是否存在
        $gameUser = $this->getGameUserByUserId($userId);
        if (!$gameUser) {
            return false;
        }

        // 创建默认的level数据（炼气1层）
        $defaultLevel = $this->createDefaultLevel();

        // 准备更新数据
        $updateData = [
            'nickname' => $characterData['nickname'],
            'gender' => $characterData['gender'],
            'avatar_url' => $characterData['avatar_url'],
            'talent' => json_encode($characterData['talent']),
            'element' => json_encode($characterData['element']),
            'level' => json_encode($defaultLevel)
        ];

        // 如果有base数据，也更新
        if (isset($characterData['base']) && is_array($characterData['base'])) {
            $updateData['base'] = json_encode($characterData['base']);
        }

        // 添加更新时间
        $updateData = $this->addTime($updateData);

        $where = [
            ['user_id', '=', $userId],
            ['status', '=', '1'],
        ];

        // 更新用户数据
        $result = $this->model->where($where)->update($updateData);

        if ($result === false) {
            return false;
        }

        // 返回更新后的用户数据
        return $this->getGameUserByUserId($userId);
    }

    /**
     * 创建默认的level数据（炼气1层）
     * @return array 默认level数据
     */
    private function createDefaultLevel()
    {
        // 获取炼气境界的数据（id=1）
        $gameLevelModel = new GameLevelModel();
        $levelData = $gameLevelModel->where('id', 1)->find();

        if (!$levelData) {
            // 如果数据库中没有数据，返回默认值
            return [
                'level_id' => 1,
                'level_name' => '炼气',
                'current_layer' => 1,
                'layer_name' => '一层',
                'layer_desc' => '引气入体，经脉初开',
                'use_life_time' => 0,
                'cost_life_time' => 1050,
                'reward_life_time' => 1500,
            ];
        }

        // 解析up_json获取第一层的数据
        $upJson = json_decode_pro($levelData['up_json'], true);
        $firstLayer = $upJson[0] ?? [];

        return [
            'level_id' => $levelData['id'],                                           // 对应game_level表的id
            'level_name' => $levelData['name'],                                      // 境界名称
            'current_layer' => 1,                                                    // 当前层数（第一层）
            'layer_name' => $firstLayer['name'] ?? '一层',                           // 当前层数名称
            'layer_desc' => $firstLayer['desc'] ?? '引气入体，经脉初开',              // 当前层数描述
            'use_life_time' => 0,                                                    // 已消耗寿命
            'cost_life_time' => intval($firstLayer['cost_life_time'] ?? 1050),      // 突破到下一层所需寿命
            'reward_life_time' => intval($firstLayer['reward_life_time'] ?? 1500),  // 当前层奖励寿命
        ];
    }

    /**
     * 更新玩家数据（用于前端修炼逻辑）
     * @param int $userId 用户ID
     * @param array $playerData 玩家数据
     * @return array
     */
    public function updatePlayerData($userId, $playerData)
    {
        try {
            $gameUser = $this->model->where('user_id', $userId)->find();
            if (!$gameUser) {
                return ['code' => 404, 'msg' => '角色不存在'];
            }

            // 准备更新数据，根据表结构分别更新各个JSON字段
            $updateData = [
                'update_time' => date('Y-m-d H:i:s')
            ];

            // 更新基础属性
            if (isset($playerData['base'])) {
                $updateData['base'] = $playerData['base'];
            }

            // 更新修炼境界
            if (isset($playerData['level'])) {
                $updateData['level'] = $playerData['level'];
            }

            // 更新天赋（如果有变化）
            if (isset($playerData['talent'])) {
                $updateData['talent'] = $playerData['talent'];
            }

            // 更新灵根（如果有变化）
            if (isset($playerData['element'])) {
                $updateData['element'] = $playerData['element'];
            }

            // 更新资源（如果有变化）
            if (isset($playerData['resource'])) {
                $updateData['resource'] = $playerData['resource'];
            }

            // 更新装备（如果有变化）
            if (isset($playerData['equipment'])) {
                $updateData['equipment'] = $playerData['equipment'];
            }

            if (isset($playerData['equipment_backpack'])) {
                $updateData['equipment_backpack'] = $playerData['equipment_backpack'];
            }

            // 更新技能（如果有变化）
            if (isset($playerData['skill_combat'])) {
                $updateData['skill_combat'] = $playerData['skill_combat'];
            }

            // 更新技能（如果有变化）
            if (isset($playerData['skill_combat_backpack'])) {
                $updateData['skill_combat_backpack'] = $playerData['skill_combat_backpack'];
            }

            // 更新基本信息（如果有变化）
            if (isset($playerData['nickname'])) {
                $updateData['nickname'] = $playerData['nickname'];
            }
            if (isset($playerData['gender'])) {
                $updateData['gender'] = $playerData['gender'];
            }
            if (isset($playerData['avatar_url'])) {
                $updateData['avatar_url'] = $playerData['avatar_url'];
            }

            if (isset($playerData['sect_json'])) {
                $updateData['sect_json'] = $playerData['sect_json'];
            }

            // 执行更新
            $this->model->where('user_id', $userId)->update($updateData);

            return [
                'code' => 200,
                'msg' => '数据更新成功',
                'data' => $playerData
            ];

        } catch (\Exception $e) {
            return ['code' => 500, 'msg' => '数据更新失败：' . $e->getMessage()];
        }
    }



    /**
     * 删除游戏用户
     * @param int $userId 用户ID
     * @return bool 删除结果
     */
    public function deleteGameUser(int $userId)
    {
        $where = [
            ['user_id', '=', $userId],
            ['status', '=', '1'],
        ];
        return $this->model->where($where)->delete();
    }
}