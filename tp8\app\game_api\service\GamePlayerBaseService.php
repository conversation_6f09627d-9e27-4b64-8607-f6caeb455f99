<?php

namespace app\game_api\service;

use think\facade\Db;

class GamePlayerBaseService extends BaseService
{
    /**
     * 获取默认基础属性
     * @return array 默认基础属性数组
     */
    public function getDefaultBaseAttributes()
    {
        // 从game_player_base表获取默认属性
        $baseAttributes = Db::name('game_player_base')
            ->where('status', '1')
            ->order('sorted')
            ->select()
            ->toArray();
        
        $result = [];
        foreach ($baseAttributes as $attribute) {
            $result[$attribute['key']] = $attribute['default_value'];
        }
        
        return $result;
    }
}