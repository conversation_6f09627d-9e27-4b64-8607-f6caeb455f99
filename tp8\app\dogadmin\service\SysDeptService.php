<?php
namespace app\dogadmin\service;

use app\dogadmin\model\SysDeptModel;

class SysDeptService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysDeptModel();
    }
    
    /**
     * 获取部门级联列表数据
     * 
     * @return array 格式化后的部门数据，包含label、value和parentId
     */
    public function cascaderList()
    {
        $field = 'id as value, dept_name as  label, parent_id';
        $res = $this->model->field($field)->order(['sorted' => 'desc'])->select()->toArray();
        return $res;
    }

    public function listDeptNormal()
    {
        $res = $this->model->select()->toArray();
        return $res;
    }
    
    /**
     * 根据部门ID获取部门名称
     * @param int $deptId 部门ID
     * @return string 部门名称
     */
    public function getDeptNameById($deptId)
    {
        if (empty($deptId)) {
            return '';
        }
        $deptInfo = $this->model->where('id', $deptId)->find();
        return $deptInfo ? $deptInfo['dept_name'] : '';
    }
}