<template>
	<view class="">
		<view class="header-card">
			<image class="header-img" :src="userInfo.headimg ? userInfo.headimg: 'https://petai.lgycloud.com/logo.jpg'">
			</image>
			<view class="header-content">
				<view class="header-name">
					<text class="t1">{{userInfo.nickname}}</text>
					<!-- <text class="t2">(个人账户)</text> -->
				</view>
				<view class="header-overtime" v-if="vipInfo.vip_end_time">
					已开通 {{formatTime(vipInfo.vip_end_time,'YYYY-MM-DD')}}会员到期
				</view>
				<view class="header-overtime" v-else>
					暂未购买任何会员
				</view>
			</view>
		</view>
		<view class="vip-money" v-if="showPay == 1">
			<!-- vip-item-checked -->
			<view class="vip-item" :class="{'vip-item-checked':checkIndex == index}" v-for="(item,index) in list"
				:key="index" @click="checkIndex = index">
				<view class="t1">{{item.name}}</view>
				<view>
					<text class="t2">￥</text>
					<text class="t3">{{showMoney(item.money)}}</text>
				</view>
				<view class="t4">{{item.desc}}</view>
			</view>
		</view>
		<view class="vip-money-tips" v-else>
			<view>因为相关政策原因，暂时无法使用</view>
			<button class="kf-line" open-type="contact">如有疑问请咨询客服</button>
		</view>
		<!-- <view class="line-text"  @click="showTips">
			<view class="line-item">
				<text>服务说明</text>
				<tui-icon name="arrowright" :size="28"></tui-icon>
			</view>
		</view> -->
		<view class="btn-wrap-edit-height"></view>
		<view class="btn-wrap-edit" >
			<view class="money-wrap">
				<view class="txt">
					<text class="t1">实付</text>
					<text class="t2">￥</text>
					<text class="t3" v-if="list.length > 0">{{showMoney(list[checkIndex].money)}}</text>
					<text class="t3" v-else>0</text>
				</view>
				<view class="btn" @click="toBuy">确认协议并缴费</view>
			</view>
			<view class="tips" @click="toPage('/pages/user/agreement')">已阅读并同意《迷雾小助手小程序使用合同》</view>
			<!-- <view class="mt">
				<view class="btn">购买</view>
			</view> -->
		</view>
	</view>
</template>

<script>
	import { getVipGoodsList } from '@/api/vipGoods.js';
	import { addVipOrder } from '@/api/vipOrder.js';
	import { payMoney } from '@/api/pay.js';
	import { mapActions,mapGetters } from 'vuex';
	import dayjs from '@/utils/dayjs.js';
	export default {
		
		data() {
			return {
				list: [],
				checkIndex: 0
			}
		},
		computed: {
			...mapGetters(['isLogin', 'userInfo','vipInfo','showPay'])
		},
		onLoad(options) {
			// if(options.bp == 1){
			// 	this.showBp();
			// }
			this.initData();
		},
		methods: {
			...mapActions({
				getVipInfo: 'getVipInfo'
			}),
			initData() {
				this._getVipGoodsList();
			},
			showBp(){
				uni.showModal({
					title: '白嫖福利',
					content: '点击右上角【···】分享到朋友或者朋友圈后继续使用',
					confirmText: '已知晓',
					showCancel:false,
					success: (res) => {}
				})
			},
			_getVipGoodsList() {
				let data = {
					status: 1
				}
				getVipGoodsList(data).then(res => {
					console.log('res', res);
					this.list = res.data.data
				})
			},
			showMoney(money) {
				let tmpMoney = parseInt(money);
				if (money == tmpMoney) {
					return tmpMoney;
				} else {
					return money;
				}
			},
			showTips(){
				uni.showModal({
					title: '服务说明',
					content: '本咨询内容仅供参考，如宠物发生疾病，请及时就医。',
					showCancel: false,
					cancelText: '',
					confirmText: '我已知晓',
					success: res => {},
					fail: () => {},
					complete: () => {}
				});
			},
			toBuy() {
				let buyInfo = this.list[this.checkIndex];
				let that = this;
				addVipOrder(buyInfo).then(res => {
					if (res.code == 1) {
						let data = {
							trade_type: 'petai',
							trade_id: res.data,
							type: 'wechatpay'
						}
						payMoney(data).then(ret => {
							uni.requestPayment({
								provider: 'wxpay',
								...ret.data,
								success: (res) => {
									uni.showToast({
										title: '购买成功！',
										icon: 'none'
									})
									setTimeout(()=>{
										that.getVipInfo();
									},3000)
								},
								fail: (res) => {

								}
							})
						})
					}
				})
			}
		}
	}
</script>


<style lang="scss" scoped>
	.header-card {
		display: flex;
		align-items: center;
		padding: 40rpx;
		// background-color: #292929;

		.header-img {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			border: 1px solid #fff;
		}

		.header-content {
			padding-left: 40rpx;
			
			.header-name {
				.t1 {
					color: #fff;
					font-size: 36rpx;
				}

				.t2 {
					color: #7F7F7F;
					font-size: 34rpx;
				}
			}

			.header-overtime {
				color: #7F7F7F;
				font-size: 30rpx;
			}
		}
	}

	.vip-money {
		// background-color: #141416;
		border-radius: 60rpx 60rpx 0 0;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 50rpx;
		padding-top: 0;
		padding-bottom: 0;

		.vip-item {
			background-color: #292929;
			text-align: center;
			color: #FFFFFF;
			// padding: 30rpx;
			// padding-top: 40rpx;
			// padding-bottom: 40rpx;
			width: 300rpx;
			height: 300rpx;
			margin-bottom: 50rpx;
			border-radius: 40rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			.t1 {
				font-size: 40rpx;
			}

			.t2 {
				font-size: 36rpx;
				color: #FCE89F;
			}

			.t3 {
				font-size: 120rpx;
				color: #FCE89F;
				font-weight: 800;
			}	

			.t4 {
				font-size: 32rpx;
			}
		}

		.vip-item-checked {
			// background-color: #FEE695;
			background: linear-gradient(to bottom, rgb(251, 236, 163), rgb(253, 185, 112));
			color: #0C0C0C;
			.t2 {
				color: #0C0C0C;
			}
			.t3{
				color: #0C0C0C;
			}
		}
	}

	.line-text {
		.line-item {
			background-color: #1A1B1F;
			color: #fff;
			font-size: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 40rpx;
		}
	}

	.btn-wrap-edit-height {
		height: 280rpx;
		width: 100%;
	}

	.btn-wrap-edit {
		z-index: 999;
		background-color: #292929;
		position: fixed;
		bottom: 30rpx;
		left: 0;
		width: 100%;
		padding-left: 30rpx;
		padding-right: 30rpx;
		padding-top: 60rpx;
		padding-bottom: 30rpx;
		box-sizing: border-box;
		margin-bottom: env(safe-area-inset-bottom);
		border-radius: 60rpx;
		
		.money-wrap{
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
		}
		.txt{
			color: #7F7F7F;
			.t1{
				
			}
			.t2{
				color: #FED8A1;
			}
			.t3{
				color: #FED8A1;
				font-size: 80rpx;
				font-weight: 600;
			}
		}
		.tips{
			color: #7F7F7F;
			text-align: center;
			font-size: 28rpx;
			padding-top: 30rpx;
		}
		.btn {
			background-color: #FFCE87;
			color: #602B0E;
			padding: 20rpx 40rpx;
			text-align: center;
			border-radius: 40rpx;
		}
	}
	
	.vip-money-tips{
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 280rpx;
		color: #fff;
	}
	.kf-line{
		color: #32a1eb;
	}
</style>