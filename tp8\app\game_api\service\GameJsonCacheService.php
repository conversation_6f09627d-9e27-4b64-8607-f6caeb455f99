<?php

namespace app\game_api\service;

use app\game_api\model\GameJsonCacheModel;

class GameJsonCacheService extends BaseService
{
    public function __construct(){
        $this->model = new GameJsonCacheModel();
    }
    //获取id最后一个的值
    public function getGameJsonCache(){
        $where = [
            ['status','=',1],
            ['delete_time','=',null]
        ];
        $data = $this->model->where($where)->order('id desc')->find();
        return $data;
    }
}