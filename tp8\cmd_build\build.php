<?php

/**
 * 目录结构生成工具
 * 用法：php build.php 模块名
 * 例如：php build.php demo
 */

// 定义基础目录
$baseDir = dirname(__DIR__) . '/app/';

// 获取命令行参数
$moduleName = $argv[1] ?? '';

if (empty($moduleName)) {
    die("请指定模块名称，例如：php build.php demo\n");
}

// 定义标准目录结构
$dirs = [
    'common',     // 公共文件目录
    'controller',  // 控制器目录
    'model',       // 模型目录
    'service',     // 服务目录
    'middleware',  // 中间件目录
    'route',       // 路由目录
    'view',        // 视图目录
];

// 定义标准文件
$files = [
    'common.php'     => "<?php\n// 这是系统自动生成的公共文件\n",
    'event.php'      => "<?php\n// 这是系统自动生成的事件定义文件\nreturn [\n\n];\n",
    'middleware.php' => "<?php\n// 这是系统自动生成的中间件定义文件\nreturn [\n\n];\n",
];

// 创建模块目录
$moduleDir = $baseDir . $moduleName;
if (!is_dir($moduleDir)) {
    mkdir($moduleDir, 0755, true);
    echo "创建模块目录: {$moduleDir}\n";
} else {
    echo "模块目录已存在: {$moduleDir}\n";
}

// 创建子目录
foreach ($dirs as $dir) {
    $path = $moduleDir . '/' . $dir;
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
        echo "创建目录: {$path}\n";
    } else {
        echo "目录已存在: {$path}\n";
    }
}

// 创建基础文件
foreach ($files as $file => $content) {
    $path = $moduleDir . '/' . $file;
    if (!file_exists($path)) {
        file_put_contents($path, $content);
        echo "创建文件: {$path}\n";
    } else {
        echo "文件已存在: {$path}\n";
    }
}

// 创建基础模型类
$baseModelContent = "<?php\n\nnamespace app\\{$moduleName}\\model;\n\nuse think\\Model;\nuse think\\model\\concern\\SoftDelete;\n\nclass BaseModel extends Model\n{\n    use SoftDelete;\n    \n    protected \$name;\n    \n    // 软删除字段\n    protected \$deleteTime = 'delete_time';\n    \n    // 软删除默认值\n    protected \$defaultSoftDelete = null;\n}\n";

$baseModelPath = $moduleDir . '/model/BaseModel.php';
if (!file_exists($baseModelPath)) {
    file_put_contents($baseModelPath, $baseModelContent);
    echo "创建基础模型: {$baseModelPath}\n";
} else {
    echo "基础模型已存在: {$baseModelPath}\n";
}

// 创建基础服务类
$baseServiceContent = "<?php\n\nnamespace app\\{$moduleName}\\service;\n\n/**\n * 通用基础服务类\n */\nclass BaseService\n{\n    /**\n     * @var \\think\\Model|null 当前服务的模型实例\n     */\n    protected \$model = null;\n\n    /**\n     * 获取分页列表数据\n     * @param array \$params 请求参数\n     * @param array \$searchKey 搜索字段配置\n     * @return array 分页数据\n     */\n    public function listPage(array \$params, array \$searchKey = []): array\n    {\n        // 构建查询条件\n        \$where = [];\n        // 实现查询逻辑\n        \n        return [];\n    }\n\n    /**\n     * 获取单条数据\n     * @param array \$params 请求参数\n     * @return array 单条数据\n     */\n    public function getById(array \$params): array\n    {\n        // 实现查询逻辑\n        \n        return [];\n    }\n\n    /**\n     * 新增数据\n     * @param array \$params 请求参数\n     * @return int|string 新增ID\n     */\n    public function add(array \$params)\n    {\n        // 实现新增逻辑\n        \n        return 0;\n    }\n\n    /**\n     * 更新数据\n     * @param array \$params 请求参数\n     * @return bool 更新结果\n     */\n    public function update(array \$params): bool\n    {\n        // 实现更新逻辑\n        \n        return true;\n    }\n\n    /**\n     * 删除数据\n     * @param array \$params 请求参数\n     * @return bool 删除结果\n     */\n    public function deleteById(array \$params): bool\n    {\n        // 实现删除逻辑\n        \n        return true;\n    }\n}\n";

$baseServicePath = $moduleDir . '/service/BaseService.php';
if (!file_exists($baseServicePath)) {
    file_put_contents($baseServicePath, $baseServiceContent);
    echo "创建基础服务: {$baseServicePath}\n";
} else {
    echo "基础服务已存在: {$baseServicePath}\n";
}

// 创建基础控制器类
$baseControllerContent = "<?php\n\nnamespace app\\{$moduleName}\\controller;\n\nuse app\\BaseController;\n\n/**\n * 通用基础控制器\n */\nclass Base extends BaseController\n{\n    /**\n     * @var \\app\\{$moduleName}\\service\\BaseService 服务层实例\n     */\n    protected \$service;\n    \n    /**\n     * @var array 请求参数\n     */\n    public \$params = [];\n    \n    /**\n     * @var array 可搜索字段\n     */\n    public \$searchKey = [];\n    \n    /**\n     * 初始化方法\n     * @return void\n     */\n    protected function initialize(): void\n    {\n        // 子类可以重写此方法以添加自定义初始化逻辑\n        \$this->service = new \\app\\{$moduleName}\\service\\BaseService();\n        \$this->params = \$this->request->param();\n    }\n\n    /**\n     * 获取分页列表数据\n     * @return \\think\\Response\n     */\n    public function listPage()\n    {\n        \$params = \$this->params;\n        \$searchKey = \$this->searchKey;\n        \$res = \$this->service->listPage(\$params, \$searchKey);\n        return json(\$res);\n    }\n\n    /**\n     * 获取单条数据\n     * @return \\think\\Response\n     */\n    public function getById()\n    {\n        \$params = \$this->params;\n        \$res = \$this->service->getById(\$params);\n        return json(\$res);\n    }\n\n    /**\n     * 新增数据\n     * @return \\think\\Response\n     */\n    public function add()\n    {\n        \$params = \$this->params;\n        \$res = \$this->service->add(\$params);\n        return json(\$res);\n    }\n\n    /**\n     * 更新数据\n     * @return \\think\\Response\n     */\n    public function update()\n    {\n        \$params = \$this->params;\n        \$res = \$this->service->update(\$params);\n        return json(\$res);\n    }\n\n    /**\n     * 删除数据\n     * @return \\think\\Response\n     */\n    public function deleteById()\n    {\n        \$params = \$this->params;\n        \$res = \$this->service->deleteById(\$params);\n        return json(\$res);\n    }\n}\n";

$baseControllerPath = $moduleDir . '/controller/Base.php';
if (!file_exists($baseControllerPath)) {
    file_put_contents($baseControllerPath, $baseControllerContent);
    echo "创建基础控制器: {$baseControllerPath}\n";
} else {
    echo "基础控制器已存在: {$baseControllerPath}\n";
}

// 创建API响应类
$apiResponseContent = "<?php\n\nnamespace app\\{$moduleName}\\common;\n\n/**\n * API响应类\n */\nclass ApiResponse\n{\n    /**\n     * 成功响应\n     * @param mixed \$data 响应数据\n     * @param string \$msg 响应消息\n     * @param int \$code 响应代码\n     * @return \\think\\Response\n     */\n    public static function success(\$data = null, string \$msg = '操作成功', int \$code = 200)\n    {\n        return json([\n            'code' => \$code,\n            'msg'  => \$msg,\n            'data' => \$data,\n        ]);\n    }\n\n    /**\n     * 错误响应\n     * @param string \$msg 错误消息\n     * @param int \$code 错误代码\n     * @param mixed \$data 错误数据\n     * @return \\think\\Response\n     */\n    public static function error(string \$msg = '操作失败', int \$code = 400, \$data = null)\n    {\n        return json([\n            'code' => \$code,\n            'msg'  => \$msg,\n            'data' => \$data,\n        ]);\n    }\n\n    /**\n     * 参数错误响应\n     * @param string \$msg 错误消息\n     * @param mixed \$data 错误数据\n     * @return \\think\\Response\n     */\n    public static function paramError(string \$msg = '参数错误', \$data = null)\n    {\n        return self::error(\$msg, 400, \$data);\n    }\n\n    /**\n     * 系统错误响应\n     * @param string \$msg 错误消息\n     * @param mixed \$data 错误数据\n     * @return \\think\\Response\n     */\n    public static function systemError(string \$msg = '系统错误', \$data = null)\n    {\n        return self::error(\$msg, 500, \$data);\n    }\n\n    /**\n     * 无权限响应\n     * @param string \$msg 错误消息\n     * @param mixed \$data 错误数据\n     * @return \\think\\Response\n     */\n    public static function noPermission(string \$msg = '无权限', \$data = null)\n    {\n        return self::error(\$msg, 403, \$data);\n    }\n}\n";

$apiResponsePath = $moduleDir . '/common/ApiResponse.php';
if (!is_dir($moduleDir . '/common')) {
    mkdir($moduleDir . '/common', 0755, true);
}

if (!file_exists($apiResponsePath)) {
    file_put_contents($apiResponsePath, $apiResponseContent);
    echo "创建API响应类: {$apiResponsePath}\n";
} else {
    echo "API响应类已存在: {$apiResponsePath}\n";
}

// 创建路由文件
$routeContent = "<?php\nuse think\\facade\\Route;\n\n// 在这里定义模块路由\n\n";

$routePath = $moduleDir . '/route/' . strtolower($moduleName) . '.php';
if (!file_exists($routePath)) {
    file_put_contents($routePath, $routeContent);
    echo "创建路由文件: {$routePath}\n";
} else {
    echo "路由文件已存在: {$routePath}\n";
}

echo "\n模块 {$moduleName} 创建完成!\n";
echo "你可以开始在这个模块中添加你的业务逻辑了。\n";