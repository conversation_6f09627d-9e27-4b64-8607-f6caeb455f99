<?php

return [
    // 默认存储类型
    'default' => 'qiniu',
    
    // 七牛云配置
    'qiniu' => [
        'accessKey' => env('QINIU_ACCESS_KEY', ''),
        'secretKey' => env('QINIU_SECRET_KEY', ''),
        'bucket' => env('QINIU_BUCKET', ''),
        'domain' => env('QINIU_DOMAIN', ''),
        'rules' => [
            'maxSize' => 10 * 1024 * 1024, // 10MB
            'allowedTypes' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'mp3', 'mp4', 'avi', 'mov', 'zip', 'rar', 'json']
        ],
        'suffix'=>'-small',
        'mini_img_suffix'=>'-mini',
        'normal_img_suffix'=>'-normal'
    ],
    
    // 腾讯云COS配置
    'tencent_cos' => [
        'secretId' => env('TENCENT_COS_SECRET_ID', ''),
        'secretKey' => env('TENCENT_COS_SECRET_KEY', ''),
        'bucket' => env('TENCENT_COS_BUCKET', ''),
        'region' => env('TENCENT_COS_REGION', 'ap-beijing'),
        'domain' => env('TENCENT_COS_DOMAIN', ''),
        'rules' => [
            'maxSize' => 10 * 1024 * 1024, // 10MB
            'allowedTypes' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'mp3', 'mp4', 'avi', 'mov', 'zip', 'rar', 'json']
        ]
    ],
    
    // 阿里云OSS配置
    'aliyun_oss' => [
        'accessKeyId' => env('ALIYUN_OSS_ACCESS_KEY_ID', ''),
        'accessKeySecret' => env('ALIYUN_OSS_ACCESS_KEY_SECRET', ''),
        'bucket' => env('ALIYUN_OSS_BUCKET', ''),
        'endpoint' => env('ALIYUN_OSS_ENDPOINT', 'oss-cn-hangzhou.aliyuncs.com'),
        'domain' => env('ALIYUN_OSS_DOMAIN', ''),
        'rules' => [
            'maxSize' => 10 * 1024 * 1024, // 10MB
            'allowedTypes' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'mp3', 'mp4', 'avi', 'mov', 'zip', 'rar', 'json']
        ]
    ],
    
    // 通用上传规则
    'upload_rules' => [
        'image' => [
            'maxSize' => 10 * 1024 * 1024, // 10MB
            'allowedTypes' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
        ],
        'document' => [
            'maxSize' => 20 * 1024 * 1024, // 20MB
            'allowedTypes' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'json']
        ],
        'media' => [
            'maxSize' => 100 * 1024 * 1024, // 100MB
            'allowedTypes' => ['mp3', 'wav', 'flac', 'aac', 'ogg', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
        ],
        'archive' => [
            'maxSize' => 50 * 1024 * 1024, // 50MB
            'allowedTypes' => ['zip', 'rar', '7z', 'tar', 'gz']
        ]
    ],
    
    // 上传路径配置
    'upload_paths' => [
        'image' => 'images/',
        'document' => 'documents/',
        'media' => 'media/',
        'archive' => 'archives/',
        'avatar' => 'avatars/',
        'temp' => 'temp/'
    ]
];