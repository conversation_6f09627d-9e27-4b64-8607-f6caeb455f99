<template>
  <div class="koi-flex">
    <KoiCard>
      <!-- 搜索条件 -->
      <el-form v-show="showSearch" :inline="true">
        <el-form-item label="类型名字" prop="name">
          <el-input
            placeholder="请输入类型名字"
            v-model="searchParams.name"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型状态" prop="status">
          <el-select
            placeholder="请选择类型状态"
            v-model="searchParams.status"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          >
            <el-option
              v-for="koi in koiDicts.sys_switch_status"
              :key="koi.dictValue"
              :label="koi.dictLabel"
              :value="koi.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch" v-auth="['blog:blogType:search']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格头部按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5" v-auth="['blog:blogType:add']">
          <el-button type="primary" icon="plus" plain @click="handleAdd()">新增</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['blog:blogType:update']">
          <el-button type="success" icon="edit" plain @click="handleUpdate()" :disabled="single">修改</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['blog:blogType:delete']">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete()" :disabled="multiple">删除</el-button>
        </el-col>
        <KoiToolbar v-model:showSearch="showSearch" @refreshTable="handleListPage"></KoiToolbar>
      </el-row>

      <div class="h-20px"></div>
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        border
        :data="tableList"
        empty-text="暂时没有数据哟🌻"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" prop="skillId" width="80px" align="center" type="index"></el-table-column>
        <el-table-column
          label="类型名字"
          prop="name"
          width="180px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column label="图标" prop="icon" width="120px" align="center" :show-overflow-tooltip="true">
          <template #default="scope">
            <div class="flex justify-center">
              <el-image
                class="rounded-8px w-100% h-52px"
                :preview-teleported="true"
                :preview-src-list="[scope.row.icon]"
                :src="scope.row.icon"
              >
                <template #error>
                  <el-image
                    src="http://img.miwudalu.com/a75ecfa215ee5c01774ff49c44fafdbd8f3e83a2.png"
                    fit="cover"
                    :preview-teleported="true"
                    :preview-src-list="[
                      'http://img.miwudalu.com/a75ecfa215ee5c01774ff49c44fafdbd8f3e83a2.png'
                    ]"
                    class="w-100% h-52px rounded-8px"
                  ></el-image>
                </template>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="类型状态" prop="status" width="100px" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              active-value="1"
              inactive-value="0"
              :inline-prompt="true"
              @change="handleSwitch(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="介绍" prop="remark" width="180px" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="排序" prop="sorted" width="120px" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="180px" align="center"></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="120"
          fixed="right"
          v-auth="['blog:blogType:update', 'blog:blogType:delete']"
        >
          <template #default="{ row }">
            <el-tooltip content="修改🌻" placement="top">
              <el-button
                type="primary"
                icon="Edit"
                circle
                plain
                @click="handleUpdate(row)"
                v-auth="['blog:blogType:update']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button
                type="danger"
                icon="Delete"
                circle
                plain
                @click="handleDelete(row)"
                v-auth="['blog:blogType:delete']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="h-20px"></div>
      <!-- 分页 -->
      <el-pagination
        background
        v-model:current-page="searchParams.pageNo"
        v-model:page-size="searchParams.pageSize"
        v-show="total > 0"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleListPage"
        @current-change="handleListPage"
      />

      <!-- 添加 OR 修改 -->
      <KoiDrawer
        ref="koiDrawerRef"
        :title="title"
        @koiConfirm="handleConfirm"
        @koiCancel="handleCancel"
        :loading="confirmLoading"
      >
        <template #content>
          <el-form ref="formRef" :rules="rules" :model="form" label-width="80px" status-icon>
            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="类型名字" prop="name">
                  <el-input v-model="form.name" placeholder="请输入类型名字" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="类型状态" prop="status">
                  <el-select placeholder="请选择类型状态" v-model="form.status" clearable>
                    <el-option
                      v-for="koi in koiDicts.sys_switch_status"
                      :key="koi.dictValue"
                      :label="koi.dictLabel"
                      :value="koi.dictValue"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="图标">
                  <!-- 方形示例 -->
                  <KoiUploadImage v-model:imageUrl="form.icon" width="214px">
                    <template #content>
                      <el-icon><Picture /></el-icon>
                      <span>请上传图片</span>
                    </template>
                    <template #tip>图片最大为 3M</template>
                  </KoiUploadImage>
                </el-form-item>
              </el-col>

              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="排序" prop="sorted">
                  <el-input-number v-model="form.sorted" placeholder="请输入数字" :min="1" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="备注" prop="remark">
                  <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" :rows="3" clearable />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </KoiDrawer>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="skillPage">
import { nextTick, ref, reactive, onMounted } from "vue";
import {
  koiNoticeSuccess,
  koiNoticeError,
  koiMsgSuccess,
  koiMsgError,
  koiMsgWarning,
  koiMsgBox,
  koiMsgInfo
} from "@/utils/koi.ts";
import { listPage, getById, add, update, deleteById, batchDelete, updateStatus, getSorted } from "@/api/common/commonUser/index.ts";
import { useKoiDict } from "@/hooks/dicts/index.ts";

const { koiDicts } = useKoiDict(["sys_switch_status"]);
// 数据表格加载页面动画
const loading = ref(false);
/** 是否显示搜索表单 */
const showSearch = ref<boolean>(true); // 默认显示搜索条件
// 数据表格数据
const tableList = ref<any>([]);

// 查询参数
const searchParams = ref({
  pageNo: 1, // 第几页
  pageSize: 10, // 每页显示多少条
  name: "",
  status: ""
});

const total = ref<number>(0);

// 重置搜索参数
const resetSearchParams = () => {
  searchParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: "",
    status: ""
  };
};

/** 搜索 */
const handleSearch = () => {
  searchParams.value.pageNo = 1;
  handleTableData();
};

/** 重置 */
const resetSearch = () => {
  resetSearchParams();
  handleListPage();
};

/** 数据表格 */
const handleListPage = async () => {
  try {
    tableList.value = []; // 重置表格数据
    loading.value = true;
    const res: any = await listPage(searchParams.value);
    tableList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

/** 数据表格[不带Loading，删除、批量删除等使用] */
const handleTableData = async () => {
  try {
    const res: any = await listPage(searchParams.value);
    tableList.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

// 获取数据表格数据
onMounted(() => {
  handleListPage();
});

const ids = ref([]); // 选中数组
const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
/** 是否多选 */
const handleSelectionChange = (selection: any) => {
  ids.value = selection.map((item: any) => item.skillId);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
};

// 获取最新排序数字
const handleSorted = async () => {
  try {
    const res: any = await getSorted({});
    form.value.sorted = res.data;
  } catch (error) {
    console.log(error);
    koiMsgError("数据查询失败，请重试🌻");
  }
}

/** 添加 */
const handleAdd = () => {
  // 打开弹出框
  koiDrawerRef.value.koiOpen();
  koiMsgInfo("添加🌻");
  // 重置表单
  resetForm();
  // 标题
  title.value = "添加操作";
  handleSorted();
};

/** 回显数据 */
const handleEcho = async (id: any) => {
  if (id == null || id == "") {
    koiMsgWarning("请选择需要修改的数据🌻");
    return;
  }
  try {
    const res: any = await getById(id);
    form.value = res.data;
  } catch (error) {
    koiNoticeError("数据获取失败，请刷新重试🌻");
    console.log(error);
  }
};

/** 修改 */
const handleUpdate = async (row?: any) => {
  // 打开弹出框
  koiDrawerRef.value.koiOpen();
  koiMsgInfo("修改🌻");
  // 重置表单
  resetForm();
  // 标题
  title.value = "修改操作";
  const id = row ? row.skillId : ids.value[0];
  if (id == null || id == "") {
    koiMsgError("请选中需要修改的数据🌻");
  }
  // 回显数据
  handleEcho(id);
};

/** 添加 AND 修改抽屉 */
const koiDrawerRef = ref();
// 标题
const title = ref("文章类型表");
// form表单Ref
const formRef = ref<any>();

// form表单
let form = ref<any>({
  name: "",
  status: "1",
  sorted: 1,
  icon:"",
  remark: ""
});

/** 清空表单数据 */
const resetForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (formRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      formRef.value.resetFields();
    }
  });   
  form.value = {
    name: "",
    status: "1",
    icon:"",
    sorted: 1,
    remark: ""
  };
};

/** 表单规则 */
const rules = reactive({
  name: [{ required: true, message: "请输入类型名字", trigger: "blur" }],
  status: [{ required: true, message: "请输入类型状态", trigger: "blur" }],
  sorted: [{ required: true, message: "请输入排序[数字越小越靠前]", trigger: "blur" }]
});

// 确定按钮是否显示loading
const confirmLoading = ref(false);
/** 确定  */
const handleConfirm = () => {
  if (!formRef.value) return;
  confirmLoading.value = true;
  (formRef.value as any).validate(async (valid: any) => {
    if (valid) {
      if (form.value.skillId != null && form.value.skillId != "") {
        try {
          await update(form.value);
          koiMsgSuccess("修改成功🌻");
          confirmLoading.value = false;
          koiDrawerRef.value.koiQuickClose();
          resetForm();
          handleListPage();
        } catch (error) {
          console.log(error);
          confirmLoading.value = false;
          koiNoticeError("修改失败，请刷新重试🌻");
        }
      } else {
        try {
          await add(form.value);
          koiMsgSuccess("添加成功🌻");
          confirmLoading.value = false;
          koiDrawerRef.value.koiQuickClose();
          resetForm();
          handleListPage();
        } catch (error) {
          console.log(error);
          confirmLoading.value = false;
          koiNoticeError("添加失败，请刷新重试🌻");
        }
      }
    } else {
      koiMsgError("验证失败，请检查填写内容🌻");
      confirmLoading.value = false;
    }
  });
};

/** 取消 */
const handleCancel = () => {
  koiDrawerRef.value.koiClose();
};

/** 删除 */
const handleDelete = (row: any) => {
  const id = row.skillId;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认删除该数据么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await deleteById(id);
        handleTableData();
        koiNoticeSuccess("删除成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 批量删除 */
const handleBatchDelete = () => {
  if (ids.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认进行批量删除么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await batchDelete(ids.value);
        handleTableData();
        koiNoticeSuccess("批量删除成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("批量删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 状态switch */
const handleSwitch = (row: any) => {
  let text = row.status === "0" ? "启用" : "停用";
  koiMsgBox("确认要[" + text + "]-[" + row.name + "]吗？")
    .then(async () => {
      if (!row.skillId || !row.status) {
        row.status = row.status == "0" ? "1" : "0";
        koiMsgWarning("请选择需要修改的数据🌻");
        return;
      }
      try {
        await updateStatus(row.skillId, row.status);
        koiNoticeSuccess("修改成功🌻");
      } catch (error) {
        handleTableData();
        console.log(error);
        koiNoticeError("修改失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      row.status = row.status == "0" ? "1" : "0";
      koiMsgError("已取消🌻");
    });
};
</script>

<style lang="scss" scoped></style>
