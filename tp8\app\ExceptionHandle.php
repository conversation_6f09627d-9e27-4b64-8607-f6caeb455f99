<?php
namespace app;

use app\lib\exception\BaseException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException as ThinkValidateException;
use think\Response;
use Throwable;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ThinkValidateException::class,
        BaseException::class, // 添加我们的基础异常类
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param  Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @access public
     * @param \think\Request   $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 处理自定义异常
        if ($e instanceof BaseException) {
            return json($e->toArray());
        }

        // 处理验证器异常
        if ($e instanceof ThinkValidateException) {
            return json([
                'code' => 4001,
                'msg'  => $e->getMessage(),
                'data' => [
                    'field' => $e->getField(),
                    'value' => $e->getValue()
                ]
            ]);
        }

        // 处理HTTP异常
        if ($e instanceof HttpException) {
            return json([
                'code' => $e->getStatusCode(),
                'msg'  => $e->getMessage(),
                'data' => null
            ]);
        }

        // 其他异常处理
        $debug = env('APP_DEBUG', false);
        if ($debug) {
            // 调试模式下显示详细错误信息
            return json([
                'code' => $e->getCode() ?: 500,
                'msg'  => $e->getMessage(),
                'data' => [
                    'file'  => $e->getFile(),
                    'line'  => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]
            ]);
        }

        // 生产环境下返回友好错误信息
        return json([
            'code' => 500,
            'msg'  => '系统错误，请稍后重试',
            'data' => null
        ]);
    }

    /**
     * 获取错误信息
     * @param Throwable $exception
     * @return string
     */
    protected function getMessage(Throwable $exception): string
    {
        if ($exception instanceof BaseException) {
            return $exception->getMsg();
        }
        
        return $exception->getMessage();
    }
}