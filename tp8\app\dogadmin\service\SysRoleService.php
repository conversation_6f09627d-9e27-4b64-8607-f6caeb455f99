<?php
namespace app\dogadmin\service;

use app\dogadmin\model\SysRoleModel;


class SysRoleService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysRoleModel();
    }

    /**
     * 根据角色ID获取角色代码
     * @param array $roleIds 角色ID数组
     * @return array 角色代码数组
     */
    public function getRoleCodesByIds($roleIds)
    {
        if (empty($roleIds)) {
            return [];
        }
        return $this->model->whereIn('id', $roleIds)->column('role_code');
    }

    public function listRoleElSelect(){
        $where = [
            ['status', '=', 1],
        ];
        $field = 'id as value,role_name as label,role_code';
        $res = $this->model->field($field)->where($where)->select()->toArray();
        return $res;
    }
    
    /**
     * 根据角色ID获取角色名称并拼接
     * @param array $roleIds 角色ID数组
     * @return string 拼接后的角色名称
     */
    public function getRoleNamesByIds($roleIds)
    {
        if (empty($roleIds)) {
            return '';
        }
        $roleNames = $this->model->whereIn('id', $roleIds)->column('role_name');
        return implode(',', $roleNames);
    }
}