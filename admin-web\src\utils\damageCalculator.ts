/**
 * 伤害计算系统
 * 用于计算游戏中的伤害数值，包括：
 * 1. 基础伤害计算
 * 2. 词条加成计算
 * 3. 装备效果计算
 * 4. 技能效果计算
 * 5. 最终伤害计算
 */

/**
 * 基础属性接口
 */
export interface BaseAttributes {
  /** 攻击力 */
  attack: number;
  /** 生命值 */
  health: number;
  /** 防御力 */
  defense: number;
  /** 暴击率 (0-100) */
  criticalRate: number;
  /** 暴击伤害 (%) */
  criticalDamage: number;
  /** 元素伤害加成 */
  elementalDamage: {
    [key: string]: number; // 元素类型: 伤害加成百分比
  };
}

/**
 * 词条效果接口
 */
export interface AffixEffects {
  /** 基础属性加成 (%) */
  baseAttributeBonus: {
    attack?: number;
    health?: number;
    defense?: number;
    criticalRate?: number;
    criticalDamage?: number;
  };
  /** 元素伤害加成 (%) */
  elementalDamageBonus: {
    [key: string]: number;
  };
  /** 技能伤害加成 (%) */
  skillDamageBonus: number;
  /** 特殊效果加成 (%) */
  specialEffects: {
    [key: string]: number; // 特殊效果类型: 加成百分比
  };
}

/**
 * 装备效果接口
 */
export interface EquipmentEffects {
  /** 基础属性加成 (%) */
  baseAttributeBonus: {
    attack?: number;
    health?: number;
    defense?: number;
    criticalRate?: number;
    criticalDamage?: number;
  };
  /** 元素伤害加成 (%) */
  elementalDamageBonus: {
    [key: string]: number;
  };
  /** 技能伤害加成 (%) */
  skillDamageBonus: number;
  /** 特殊效果触发 */
  specialEffects: Array<{
    /** 效果类型 */
    type: string;
    /** 触发条件 */
    conditions: Array<{
      type: 'hp' | 'mp' | 'buff' | 'debuff';
      operator: '>' | '<' | '>=' | '<=' | '=';
      value: number;
    }>;
    /** 效果数值 (%) */
    value: number;
  }>;
}

/**
 * 技能效果接口
 */
export interface SkillEffects {
  /** 基础伤害系数 */
  damageCoefficient: number;
  /** 元素类型 */
  elementType: string;
  /** 技能特效 */
  effects: Array<{
    /** 效果类型 */
    type: string;
    /** 效果数值 (%) */
    value: number;
    /** 持续时间 (秒) */
    duration?: number;
  }>;
  /** 技能词条加成 */
  affixBonus: {
    [key: string]: number; // 词条类型: 加成百分比
  };
}

/**
 * 伤害计算结果接口
 */
export interface DamageCalculationResult {
  /** 最终伤害值 */
  finalDamage: number;
  /** 是否暴击 */
  isCritical: boolean;
  /** 伤害分解 */
  breakdown: {
    /** 基础伤害 */
    baseDamage: number;
    /** 词条加成伤害 */
    affixBonus: number;
    /** 装备加成伤害 */
    equipmentBonus: number;
    /** 技能加成伤害 */
    skillBonus: number;
    /** 暴击加成伤害 */
    criticalBonus: number;
    /** 元素加成伤害 */
    elementalBonus: number;
  };
}

/**
 * 计算最终伤害
 * @param baseAttributes 角色基础属性
 * @param affixEffects 词条效果
 * @param equipmentEffects 装备效果
 * @param skillEffects 技能效果
 * @returns 伤害计算结果
 */
export function calculateDamage(
  baseAttributes: BaseAttributes,
  affixEffects: AffixEffects,
  equipmentEffects: EquipmentEffects,
  skillEffects: SkillEffects
): DamageCalculationResult {
  // 1. 计算基础伤害
  let baseDamage = calculateBaseDamage(baseAttributes);

  // 2. 计算词条加成
  const affixBonus = calculateAffixBonus(baseDamage, affixEffects, skillEffects);

  // 3. 计算装备加成
  const equipmentBonus = calculateEquipmentBonus(
    baseDamage + affixBonus,
    equipmentEffects,
    skillEffects
  );

  // 4. 计算技能加成
  const skillBonus = calculateSkillBonus(
    baseDamage + affixBonus + equipmentBonus,
    skillEffects
  );

  // 5. 计算暴击
  const { criticalDamage, isCritical } = calculateCritical(
    baseAttributes,
    affixEffects,
    equipmentEffects
  );

  // 6. 计算元素加成
  const elementalBonus = calculateElementalBonus(
    baseAttributes,
    affixEffects,
    equipmentEffects,
    skillEffects
  );

  // 7. 计算最终伤害
  const finalDamage = Math.floor(
    (baseDamage + affixBonus + equipmentBonus + skillBonus) *
    (1 + (isCritical ? criticalDamage : 0)) *
    (1 + elementalBonus)
  );

  return {
    finalDamage,
    isCritical,
    breakdown: {
      baseDamage,
      affixBonus,
      equipmentBonus,
      skillBonus,
      criticalBonus: isCritical ? criticalDamage : 0,
      elementalBonus
    }
  };
}

/**
 * 计算基础伤害
 * @param baseAttributes 角色基础属性
 * @returns 基础伤害值
 */
function calculateBaseDamage(baseAttributes: BaseAttributes): number {
  return baseAttributes.attack;
}

/**
 * 计算词条加成
 * @param baseDamage 基础伤害
 * @param affixEffects 词条效果
 * @param skillEffects 技能效果（某些技能会影响词条效果）
 * @returns 词条加成伤害值
 */
function calculateAffixBonus(
  baseDamage: number,
  affixEffects: AffixEffects,
  skillEffects: SkillEffects
): number {
  let bonus = 0;

  // 1. 计算基础属性加成
  if (affixEffects.baseAttributeBonus.attack) {
    bonus += baseDamage * (affixEffects.baseAttributeBonus.attack / 100);
  }

  // 2. 计算技能对词条的加成
  if (skillEffects.affixBonus) {
    Object.entries(skillEffects.affixBonus).forEach(([affixType, value]) => {
      if (affixEffects.specialEffects[affixType]) {
        bonus += baseDamage * ((affixEffects.specialEffects[affixType] * value) / 100);
      }
    });
  }

  return Math.floor(bonus);
}

/**
 * 计算装备加成
 * @param currentDamage 当前伤害（基础伤害+词条加成）
 * @param equipmentEffects 装备效果
 * @param skillEffects 技能效果（某些技能会影响装备效果）
 * @returns 装备加成伤害值
 */
function calculateEquipmentBonus(
  currentDamage: number,
  equipmentEffects: EquipmentEffects,
  skillEffects: SkillEffects
): number {
  let bonus = 0;

  // 1. 计算基础属性加成
  if (equipmentEffects.baseAttributeBonus.attack) {
    bonus += currentDamage * (equipmentEffects.baseAttributeBonus.attack / 100);
  }

  // 2. 计算技能伤害加成
  if (equipmentEffects.skillDamageBonus && skillEffects.damageCoefficient) {
    bonus += currentDamage * 
      ((equipmentEffects.skillDamageBonus * skillEffects.damageCoefficient) / 100);
  }

  // 3. 计算特殊效果
  equipmentEffects.specialEffects.forEach(effect => {
    // 检查效果触发条件
    const isTriggered = effect.conditions.every(condition => {
      // 这里需要根据实际游戏状态检查条件
      // 简化处理，假设条件都满足
      return true;
    });

    if (isTriggered) {
      bonus += currentDamage * (effect.value / 100);
    }
  });

  return Math.floor(bonus);
}

/**
 * 计算技能加成
 * @param currentDamage 当前伤害（基础伤害+词条加成+装备加成）
 * @param skillEffects 技能效果
 * @returns 技能加成伤害值
 */
function calculateSkillBonus(
  currentDamage: number,
  skillEffects: SkillEffects
): number {
  let bonus = 0;

  // 1. 计算技能系数加成
  bonus += currentDamage * (skillEffects.damageCoefficient - 1);

  // 2. 计算技能特效加成
  skillEffects.effects.forEach(effect => {
    if (effect.type === 'damage_bonus') {
      bonus += currentDamage * (effect.value / 100);
    }
  });

  return Math.floor(bonus);
}

/**
 * 计算暴击
 * @param baseAttributes 角色基础属性
 * @param affixEffects 词条效果
 * @param equipmentEffects 装备效果
 * @returns 暴击相关数据
 */
function calculateCritical(
  baseAttributes: BaseAttributes,
  affixEffects: AffixEffects,
  equipmentEffects: EquipmentEffects
): { criticalDamage: number; isCritical: boolean } {
  // 1. 计算最终暴击率
  let finalCritRate = baseAttributes.criticalRate;
  if (affixEffects.baseAttributeBonus.criticalRate) {
    finalCritRate += affixEffects.baseAttributeBonus.criticalRate;
  }
  if (equipmentEffects.baseAttributeBonus.criticalRate) {
    finalCritRate += equipmentEffects.baseAttributeBonus.criticalRate;
  }
  finalCritRate = Math.min(finalCritRate, 100); // 暴击率上限100%

  // 2. 计算最终暴击伤害
  let finalCritDamage = baseAttributes.criticalDamage;
  if (affixEffects.baseAttributeBonus.criticalDamage) {
    finalCritDamage += affixEffects.baseAttributeBonus.criticalDamage;
  }
  if (equipmentEffects.baseAttributeBonus.criticalDamage) {
    finalCritDamage += equipmentEffects.baseAttributeBonus.criticalDamage;
  }

  // 3. 判断是否暴击
  const isCritical = Math.random() * 100 < finalCritRate;

  return {
    criticalDamage: finalCritDamage / 100,
    isCritical
  };
}

/**
 * 计算元素加成
 * @param baseAttributes 角色基础属性
 * @param affixEffects 词条效果
 * @param equipmentEffects 装备效果
 * @param skillEffects 技能效果
 * @returns 元素加成总值
 */
function calculateElementalBonus(
  baseAttributes: BaseAttributes,
  affixEffects: AffixEffects,
  equipmentEffects: EquipmentEffects,
  skillEffects: SkillEffects
): number {
  const elementType = skillEffects.elementType;
  let totalBonus = 0;

  // 1. 基础元素加成
  if (baseAttributes.elementalDamage[elementType]) {
    totalBonus += baseAttributes.elementalDamage[elementType];
  }

  // 2. 词条元素加成
  if (affixEffects.elementalDamageBonus[elementType]) {
    totalBonus += affixEffects.elementalDamageBonus[elementType];
  }

  // 3. 装备元素加成
  if (equipmentEffects.elementalDamageBonus[elementType]) {
    totalBonus += equipmentEffects.elementalDamageBonus[elementType];
  }

  return totalBonus / 100;
}

/**
 * 计算技能实际冷却时间
 * @param baseCooldown 基础冷却时间
 * @param cooldownReduction 冷却时间减少百分比
 * @returns 实际冷却时间
 */
export function calculateActualCooldown(
  baseCooldown: number,
  cooldownReduction: number
): number {
  return baseCooldown * (1 - Math.min(cooldownReduction, 100) / 100);
}

/**
 * 计算技能实际消耗
 * @param baseCost 基础消耗
 * @param costReduction 消耗减少百分比
 * @returns 实际消耗
 */
export function calculateActualCost(
  baseCost: number,
  costReduction: number
): number {
  return Math.floor(baseCost * (1 - Math.min(costReduction, 100) / 100));
}

/**
 * 计算持续伤害
 * @param damageResult 单次伤害计算结果
 * @param duration 持续时间
 * @param interval 伤害间隔
 * @returns 总伤害值
 */
export function calculateDotDamage(
  damageResult: DamageCalculationResult,
  duration: number,
  interval: number
): number {
  const tickCount = Math.floor(duration / interval);
  return damageResult.finalDamage * tickCount;
}