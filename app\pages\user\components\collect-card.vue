<template>
	<view>
		<view class="card-wrap" @click="toPage(`/pages/guide/detail?guideId=${info.guideId}`)">
			<image class="card-img" :src="info.picture"></image>
			<view class="card-content">
				<view class="title1">【{{info.type.name}}】{{info.title}}</view>
				<view class="tag">
					<block v-for="(item,index) in info.tags">
						<tui-tag type="primary" padding="8rpx" size="24rpx" margin="0 10rpx 0 0">{{item.name}}</tui-tag>
					</block>
				</view>
				<view class="desc">{{truncateString(info.remark)}}</view>
				<!-- <view class="desc">
					<text class="pd">{{info.author}}</text>
					<tui-icon class="pd" name="eye" :size="12"></tui-icon>
					<text class="pd">100</text>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			info: {
				type: [String, Object],
				default: () => {
					return ''
				}
			},
		},
		data() {
			return {

			}
		},
		methods: {
			truncateString(str) {
				if (str.length > 34) {
					return str.substring(0, 34) + '...';
				}
				return str;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.card-wrap {
		// width: 696rpx;
		width: 100%;
		padding: 28rpx;
		// height: 168rpx;
		background-color: #fff;
		display: flex;
		margin-bottom: 2rpx;
		// background: linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #FFFFFF;
		// box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.1),0px 1px 3px 0px rgba(0, 0, 0, 0.1);
		// border-radius: 14rpx;
		border-radius: 16rpx;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

		// align-items: center;
		.card-img {
			display: block;
			width: 160rpx;
			height: 160rpx;
			border-radius: 14rpx;
			flex-shrink: 0;
		}

		.card-content {
			margin-left: 28rpx;

			.pd {
				padding-right: 12rpx;
			}

			.title1 {
				font-size: 30rpx;
				font-weight: bold;
			}

			.desc {
				padding-top: 14rpx;
				font-size: 24rpx;
				display: flex;
				align-items: center;
				color: #9CA3AF;
			}
		}
	}
</style>