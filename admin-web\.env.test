# 变量必须以 VITE_ 为前缀才能暴露给外部读取
VITE_ENV = 'test'
VITE_WEB_TITLE = 'DOG-ADMIN'
VITE_WEB_EN_TITLE = 'DOG-ADMIN'
VITE_LOGIN_TITLE = 'DOG-ADMIN 管理平台'
VITE_LOGIN_EN_TITLE = 'DOG-ADMIN Platform'
VITE_WEB_BASE_API = '/test-api'
# 本地Mock地址
VITE_SERVER = 'https://game.miwudalu.com88'
# 路由模式[哈希模式 AND WEB模式 [hash | history, 这两个模式是固定死的，不能乱改值]
VITE_ROUTER_MODE = history
# 是否使用全部去除console 和 debugger
VITE_DROP_CONSOLE = false
