<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta name="referrer" content="never">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta http-equiv="pragram" content="no-cache">
		<meta http-equiv="cache-control" content="no-cache,no-store, must-revalidate">
		<meta http-equiv="expires" content="0">
		<meta name="keywords" content="多惠">
		<meta name="description" content="美团 饿了么 滴滴 优惠券">
		
		<meta name="viewport"
			content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
		<title>
			多惠
		</title>
		<style>
			body{
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100vh;
			}
		</style>
		<!-- 正式发布的时候使用，开发期间不启用。↑ -->
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
			})
		</script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.css" />
	</head>
	<body>
		<noscript>
			<strong>本站点必须要开启JavaScript才能运行</strong>
		</noscript>
		<div id="app">
			<div class="html-title">此帐号已被封内容无法查看</div>
		</div>
		<!-- built files will be auto injected -->
		<script>
			/*BAIDU_STAT*/
		</script>
		<div style="display: none">
			<!-- <script src="https://map.qq.com/api/gljs?v=1.exp&key=PZSBZ-3RXRU-NCVV4-BKIQS-YDW5Z-ZVFB4"></script> -->
			<script src="https://h5.duohuiyu.com/vconsole.min.js"></script>
			<script>
				
				function getQueryVariable(variable) {
					let query = window.location.search.substring(1);
					let vars = query.split("&");
					for (let i = 0; i < vars.length; i++) {
						let pair = vars[i].split("=");
						if (pair[0] == variable) {
							return pair[1];
						}
					}
					return false;
				}
				if(getQueryVariable('debug') == 1){
					var vConsole = new window.VConsole();
				}
			</script>
		</div>
	</body>
</html>
