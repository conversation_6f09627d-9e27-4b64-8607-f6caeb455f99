<template>
  <div class="p-4px">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card>
          <div class="text-13px text-#303133 dark:text-#E5EAF3">
            <div class="flex justify-center">
              <KoiUploadImage v-model:imageUrl="mine.avatar">
                <template #content>
                  <el-icon><Avatar /></el-icon>
                  <span>请上传头像</span>
                </template>
              </KoiUploadImage>
            </div>
            <div class="flex justify-center m-t-5px">
              <el-button type="primary" size="small" plain @click="handleUpdateAvatar">保存头像</el-button>
            </div>
            <div class="flex justify-between flex-wrap mt-20px p-y-12px">
              <div class="flex items-center">
                <el-icon size="15"> <UserFilled /> </el-icon>
                <div class="p-l-2px">登录名称</div>
              </div>
              <div v-text="mine.login_name"></div>
            </div>
            <div class="flex justify-between flex-wrap p-y-12px">
              <div class="flex items-center">
                <el-icon size="15"> <User /> </el-icon>
                <div class="p-l-2px">用户名称</div>
              </div>
              <div v-text="mine.user_name"></div>
            </div>
            <div class="flex justify-between flex-wrap p-y-12px">
              <div class="flex items-center">
                <el-icon size="15"> <Iphone /> </el-icon>
                <div class="p-l-2px">手机号码</div>
              </div>
              <div v-text="mine.phone"></div>
            </div>
            <div class="flex justify-between flex-wrap p-y-12px">
              <div class="flex items-center">
                <el-icon size="15"> <Message /> </el-icon>
                <div class="p-l-2px">用户邮箱</div>
              </div>
              <div v-text="mine.email"></div>
            </div>
            <div class="flex justify-between flex-wrap p-y-12px">
              <div class="flex items-center">
                <el-icon size="15"> <Postcard /> </el-icon>
                <div class="p-l-2px">所属部门</div>
              </div>
              <div v-text="mine.dept_name"></div>
            </div>
            <div class="flex justify-between flex-wrap p-y-12px">
              <div class="flex items-center">
                <el-icon size="15"> <Collection /> </el-icon>
                <div class="p-l-2px">所属角色</div>
              </div>
              <div v-text="mine.role_name"></div>
            </div>
            <div class="flex justify-between flex-wrap p-y-12px">
              <div class="flex items-center">
                <el-icon size="15"> <Calendar /> </el-icon>
                <div class="p-l-2px">创建日期</div>
              </div>
              <div v-text="mine.create_time"></div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card :body-style="{ 'padding-top': '6px' }">
          <el-tabs v-model="activeName">
            <el-tab-pane label="基本资料" name="first">
              <el-form ref="mineFormRef" :rules="mineRules" :model="mineForm" label-width="80px" status-icon>
                <el-row>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                    <el-form-item label="登录名称" prop="login_name">
                      <el-input v-model="mineForm.login_name" placeholder="请输入登录名称" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                    <el-form-item label="手机号码" prop="phone">
                      <el-input v-model="mineForm.phone" placeholder="请输入手机号码" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                    <el-form-item label="邮箱" prop="email">
                      <el-input v-model="mineForm.email" placeholder="请输入邮箱" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                    <el-form-item label="性别" prop="sex">
                      <el-radio-group v-model="mineForm.sex" placeholder="请选择性别">
                        <el-radio value="1" border>男</el-radio>
                        <el-radio value="2" border>女</el-radio>
                        <el-radio value="3" border>未知</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }" class="mt-6px">
                    <el-form-item>
                      <el-button type="primary" plain @click="handleMineSave">保存</el-button>
                      <el-button type="danger" plain @click="resetMineForm">重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <!-- {{ mineForm }} -->
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="second">
              <el-form ref="pwdFormRef" :rules="pwdRules" :model="pwdForm" label-width="80px" status-icon>
                <el-row>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                    <el-form-item label="密码" prop="password">
                      <el-input v-model="pwdForm.password" placeholder="请输入旧密码" show-password clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                    <el-form-item label="新密码" prop="newPassword">
                      <el-input v-model="pwdForm.newPassword" placeholder="请输入新密码" show-password clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                    <el-form-item label="确认密码" prop="confirmPassword">
                      <el-input v-model="pwdForm.confirmPassword" placeholder="请输入确认密码" show-password clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="{ span: 24 }" :sm="{ span: 24 }" class="mt-6px">
                    <el-form-item>
                      <el-button type="primary" plain @click="handlePwdSave">保存</el-button>
                      <el-button type="danger" plain @click="resetPwdForm">重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <!-- {{ pwdForm }} -->
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts" name="personagePage">
import { ElLoading } from "element-plus";
import { nextTick, ref, reactive, onMounted } from "vue";
import { koiMsgError, koiNoticeSuccess, koiNoticeError } from "@/utils/koi.ts";
import { getPersonalData, updateBasicData, updateUserPwd } from "@/api/system/user/index.ts";

onMounted(() => {
  handleLeftCard();
});

/** 左侧卡片资料 */
const handleLeftCard = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });
  try {
    const res: any = await getPersonalData();
    mine.value = res.data;
    mineForm.value = res.data;
    if (res.data?.avatar.length == 0) {
      mine.value.avatar = "https://pic4.zhimg.com/v2-702a23ebb518199355099df77a3cfe07_b.webp";
    }
    loading.close();
  } catch (error) {
    console.log(error);
    loading.close();
    koiMsgError("个人信息获取失败🌻");
  }
};

/** 头像修改 */
const handleUpdateAvatar = async () => {
  const avatar = mine.value.avatar;
  try {
    await updateBasicData({ avatar });
    koiNoticeSuccess("头像修改成功🌻");
  } catch (error) {
    console.log(error);
    koiNoticeError("头像修改失败🌻");
  }
};

// 个人信息
const mine = ref({
  avatar: "",
  login_name: "",
  user_name: "",
  phone: "",
  email: "",
  dept_name: "",
  role_name: "",
  create_time: ""
});

// el-card标签选中name
const activeName = ref("first");

/** 基本资料开始  */

// form表单Ref
const mineFormRef = ref<any>();
// form表单
let mineForm = ref<any>({
  login_name: "",
  phone: "",
  email: "",
  sex: ""
});
/** 清空表单数据 */
const resetMineForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (mineFormRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      mineFormRef.value.resetFields();
    }
  });
  mineForm.value = {
    login_name: "",
    phone: "",
    email: "",
    sex: "3"
  };
};
/** 表单规则 */
const mineRules = reactive({
  login_name: [{ required: true, message: "请输入登录名称", trigger: "blur" }],
  phone: [{ required: true, message: "请输入手机号码", trigger: "blur" }]
});

/** 保存 */
const handleMineSave = async () => {
  if (!mineFormRef.value) return;
  (mineFormRef.value as any).validate(async (valid: any) => {
    if (valid) {
      try {
        await updateBasicData(mineForm.value);
        koiNoticeSuccess("保存成功🌻");
      } catch (error) {
        console.log(error);
        koiNoticeError("保存失败🌻");
      }
    } else {
      koiMsgError("验证失败，请检查填写内容🌻");
    }
  });
};

/** 基本资料结束  */

/** 修改密码开始  */
// form表单Ref
const pwdFormRef = ref<any>();
// form表单
let pwdForm = ref<any>({
  password: "",
  newPassword: "",
  confirmPassword: ""
});
/** 清空表单数据 */
const resetPwdForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (pwdFormRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      pwdFormRef.value.resetFields();
    }
  });
  pwdForm.value = {
    password: "",
    newPassword: "",
    confirmPassword: ""
  };
};
/** 表单规则 */
const pwdRules = reactive({
  password: [
    { required: true, message: '请输入旧密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (!/^(?=.*\d)(?=.*[a-zA-Z]).+$/.test(value)) {
          callback(new Error('密码必须包含数字和字母'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (!/^(?=.*\d)(?=.*[a-zA-Z]).+$/.test(value)) {
          callback(new Error('密码必须包含数字和字母'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请输入确认密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (!/^(?=.*\d)(?=.*[a-zA-Z]).+$/.test(value)) {
          callback(new Error('密码必须包含数字和字母'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

/** 保存 */
const handlePwdSave = () => {
  if (!pwdFormRef.value) return;
  (pwdFormRef.value as any).validate(async (valid: any) => {
    if (valid) {
      try {
        await updateUserPwd(pwdForm.value);
        koiNoticeSuccess("保存成功🌻");
      } catch (error) {
        console.log(error);
        koiNoticeError("保存失败🌻");
      }
    } else {
      koiMsgError("验证失败，请检查填写内容🌻");
    }
  });
};
/** 修改密码结束  */
</script>

<style lang="scss" scoped>
</style>
