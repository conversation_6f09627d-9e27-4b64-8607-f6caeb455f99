<template>
	<view class="container">
		<view class="tui-page-title">找回密码</view>
		<view class="tui-form">
			<view class="tui-view-input">
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="mobile" color="#6d7a87" :size="20"></tui-icon>
						<input :value="phone" placeholder="请输入手机号" placeholder-class="tui-phcolor" type="number"
							maxlength="11" @input="inputphone" />
						<view class="tui-icon-close" v-show="phone" @tap="clearInput(1)">
							<tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon>
						</view>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="shield" color="#6d7a87" :size="20"></tui-icon>
						<input placeholder="请输入验证码" placeholder-class="tui-phcolor" type="text" maxlength="6"
							@input="inputCode" />
						<tui-countdown-verify :successVal="successSend" :resetVal="resetSend" @send="getCode" :params="1"></tui-countdown-verify>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="pwd" color="#6d7a87" :size="20"></tui-icon>
						<input :value="password" placeholder="请输入新密码" :password="true" placeholder-class="tui-phcolor"
							type="text" maxlength="40" @input="inputPwd" />
						<view class="tui-icon-close" v-show="password" @tap="clearInput(2)">
							<tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon>
						</view>
					</view>
				</tui-list-cell>
			</view>
			<view class="tui-btn-box">
				<tui-button type="blue" :disabledGray="true" :disabled="disabled" :shadow="true" shape="circle" @click="toReset">确认修改</tui-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { sendSms, resetPassword } from '@/api/login.js'
	import { checkPhone } from '@/utils/common.js'
	export default {
		computed: {
			disabled: function() {
				let bool = true;
				if (this.phone && this.code && this.password) {
					bool = false;
				}
				return bool;
			}
		},
		data() {
			return {
				phone: '',
				password: '',
				code: '',
				isSend: false,
				successSend: 0,
				resetSend:0
			};
		},
		methods: {
			inputCode(e) {
				this.code = e.detail.value;
			},
			inputphone(e) {
				this.phone = e.detail.value;
			},
			inputPwd(e) {
				this.password = e.detail.value;
			},
			clearInput(type) {
				if (type == 1) {
					this.phone = '';
				} else {
					this.password = '';
				}
			},
			getCode() {
				let check = checkPhone(this.phone);
				if (check) {
					let data = {
						phone: this.phone,
						type:'resetPassword'
					}
					sendSms(data).then(res=>{
						if (res.code == 0) {
							uni.showToast({
								title: '验证码已发送',
								icon: 'none'
							})
							this.successSend += 1;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							this.resetSend += 1;
						}
					})
				} else {
					uni.showToast({
						title: '手机格式不正确',
						icon: 'none'
					})
				}
			},
			toReset(){
				let data = {
					phone: this.phone,
					password: this.password,
					code: this.code,
				}
				resetPassword(data).then(res=>{
					if (res.code == 0) {
						uni.showToast({
							title: '修改成功！',
							icon: "none"
						});
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			}
		}
	};
</script>

<style lang="scss">
	.container {
		.tui-page-title {
			width: 100%;
			font-size: 48rpx;
			font-weight: bold;
			color: $uni-text-color;
			line-height: 42rpx;
			padding: 110rpx 40rpx 40rpx 40rpx;
			box-sizing: border-box;
		}

		.tui-form {
			padding-top: 50rpx;

			.tui-view-input {
				width: 100%;
				box-sizing: border-box;
				padding: 0 40rpx;

				.tui-cell-input {
					width: 100%;
					display: flex;
					align-items: center;
					padding-top: 48rpx;
					padding-bottom: $uni-spacing-col-base;
					border-bottom: 1px solid $uni-text-color-disable;

					input {
						flex: 1;
						padding-left: $uni-spacing-row-base;
					}

					.tui-icon-close {
						margin-left: auto;
					}

					.tui-btn-send {
						width: 156rpx;
						text-align: right;
						flex-shrink: 0;
						font-size: $uni-font-size-base;
						color: $uni-color-primary;
					}

					.tui-gray {
						color: $uni-text-color-placeholder;
					}
				}
			}

			.tui-btn-box {
				width: 100%;
				padding: 0 $uni-spacing-row-lg;
				box-sizing: border-box;
				margin-top: 80rpx;
			}
		}
	}
</style>
