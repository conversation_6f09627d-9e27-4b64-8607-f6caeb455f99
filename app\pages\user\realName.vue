<template>
	<view class="wrap">
		<view class="tips-wrap">
			<view class="title">注意事项</view>
			<view class="text">
				<view>
					1、为了规范推广行为，每个账户必须实名
				</view>
				<view>2、实名以后，只能提现到该实名账户下</view>
				<view class="red">
					3、务必如实填写，乱填影响审核！！！
				</view>
				<view class="red">
					4、如有错误立即联系客服修改
				</view>
				
			</view>
		</view>
		<view class="cash-wrap">
			<view class="title">实名认证（请如实填写）</view>
			<tui-form ref="form">
				<tui-input v-model="form.real_name" label="姓名" placeholder="请输入真实姓名"></tui-input>
				<tui-input v-model="form.id_card" label="身份证" placeholder="请输入身份证号码"></tui-input>
				<tui-input v-model="form.contact_phone" label="联系手机" placeholder="请输入联系手机"></tui-input>
				<view class="btn-wrap2">
					<tui-button height="80rpx" shape="circle" :size="30" type="warning" @click="submit">提交实名</tui-button>
				</view>
			</tui-form>
		</view>

		
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';
	import { updUserRealName } from '@/api/user.js'
	import form from "@/components/common/tui-validation/tui-validation.js"
	import { rule_real_name } from '@/config/rules.js';
	export default {
		data() {
			return {
				form: {
					id_card: '',
					real_name: '',
					contact_phone: '',
				},
				rules: rule_real_name,
				submitBtnLock:false
			}
		},
		onLoad() {
			this.initData();
		},
		methods: {
			...mapActions({
				getUserInfo: 'getUserInfo'
			}),
			initData(){
				let form = this.form;
				form.real_name = this.userInfo.real_name;
				form.id_card = this.userInfo.id_card;
				form.contact_phone = this.userInfo.contact_phone;
				this.form = form;
			},
			submit() {
				this.$refs.form.validate(this.form, this.rules).then(res => {
					if (this.submitBtnLock) {
						uni.showToast({
							title: '请勿重复操作',
							icon: 'none'
						})
					} else {
						this.submitBtnLock = true;
						this._updUserRealName();
					}

				}).catch(errors => {
					console.log(errors)
				})
			},
			_updUserRealName() {
				let data = {
					...this.form
				}
				updUserRealName(data).then(res => {
					console.log('updUserRealName', res)
					this.submitBtnLock = false;
					if (res.code == 0) {
						uni.showToast({
							title: '提交成功',
							icon: 'none'
						})
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
					this.getUserInfo()
				}).catch(err=>{
					this.submitBtnLock = false;
				})
			},
		},
		computed: {
			...mapState({
				userInfo: state => state.app.userInfo,
				isLogin: state => state.app.isLogin
			})
		},
	}
</script>

<style lang="scss" scoped>
	.wrap {
		padding: 30rpx;
	}

	.money-wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #020202;
		padding: 30rpx 30rpx;
		// border-radius: 16rpx 16rpx 0 0;
		color: #edb65b;

		.btn2-wrap {
			font-size: 26rpx;
		}

		.txt-wrap {
			.txt1 {
				font-size: 30rpx;
			}

			.m-txt {
				font-size: 38rpx;
				font-weight: 600;
			}

			.txt2 {
				padding-top: 6rpx;
				font-size: 28rpx;
			}
		}
	}

	.cash-wrap {
		margin-top: 40rpx;
		background-color: #fff;

		// padding-bottom: 40rpx;
		// border-radius: 18rpx;
		.title {
			padding-left: 30rpx;
			padding-top: 20rpx;
			color: #999;
			font-size: 28rpx;
		}

		.btn-wrap2 {
			padding: 40rpx;
		}
	}

	.tips-wrap {
		padding: 30rpx 30rpx;
		background-color: #fff;
		color: #999;

		.title {
			color: #333;
		}

		.text {
			padding-top: 20rpx;
			font-size: 28rpx;
		}
	}
	
	.mini-btn{
		font-size: 26rpx;
	}
	
	.red{
		color: red;
	}
</style>