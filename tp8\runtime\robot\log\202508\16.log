[2025-08-16T15:26:28+08:00][sql] CONNECT:[ UseTime:0.000974s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T15:26:28+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.004845s ]
[2025-08-16T15:26:28+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000577s ]
[2025-08-16T15:26:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000618s ]
[2025-08-16T16:14:59+08:00][sql] CONNECT:[ UseTime:0.000772s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:14:59+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002397s ]
[2025-08-16T16:15:31+08:00][sql] CONNECT:[ UseTime:0.000740s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:15:31+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002059s ]
[2025-08-16T16:15:31+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000594s ]
[2025-08-16T16:15:31+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.000415s ]
[2025-08-16T16:15:36+08:00][sql] CONNECT:[ UseTime:0.000782s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:15:36+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002153s ]
[2025-08-16T16:15:36+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000464s ]
[2025-08-16T16:15:36+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000398s ]
[2025-08-16T16:29:00+08:00][sql] CONNECT:[ UseTime:0.001014s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:29:00+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002009s ]
[2025-08-16T16:29:00+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000552s ]
[2025-08-16T16:29:00+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000561s ]
[2025-08-16T16:29:14+08:00][sql] CONNECT:[ UseTime:0.000747s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:29:14+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002057s ]
[2025-08-16T16:29:14+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000377s ]
[2025-08-16T16:29:14+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000371s ]
[2025-08-16T16:29:42+08:00][sql] CONNECT:[ UseTime:0.001035s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:29:42+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002333s ]
[2025-08-16T16:29:42+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000431s ]
[2025-08-16T16:29:42+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000301s ]
[2025-08-16T16:31:38+08:00][sql] CONNECT:[ UseTime:0.001255s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:31:38+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.003237s ]
[2025-08-16T16:31:38+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000732s ]
[2025-08-16T16:31:38+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000588s ]
[2025-08-16T16:31:38+08:00][sql] CONNECT:[ UseTime:0.000779s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:31:38+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002189s ]
[2025-08-16T16:31:38+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000835s ]
[2025-08-16T16:31:53+08:00][sql] CONNECT:[ UseTime:0.000856s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:31:53+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002458s ]
[2025-08-16T16:31:53+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000405s ]
[2025-08-16T16:31:53+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000344s ]
[2025-08-16T16:31:53+08:00][sql] CONNECT:[ UseTime:0.000662s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:31:53+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.000740s ]
[2025-08-16T16:31:53+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000786s ]
[2025-08-16T16:32:11+08:00][sql] CONNECT:[ UseTime:0.001180s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:32:11+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.001307s ]
[2025-08-16T16:32:11+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000588s ]
[2025-08-16T16:32:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000523s ]
[2025-08-16T16:32:11+08:00][sql] CONNECT:[ UseTime:0.010956s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:32:11+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002777s ]
[2025-08-16T16:32:11+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.001072s ]
[2025-08-16T16:32:31+08:00][sql] CONNECT:[ UseTime:0.000858s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:32:31+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002073s ]
[2025-08-16T16:32:31+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000750s ]
[2025-08-16T16:32:31+08:00][sql] CONNECT:[ UseTime:0.024464s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:32:31+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002545s ]
[2025-08-16T16:32:31+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000628s ]
[2025-08-16T16:32:31+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000374s ]
[2025-08-16T16:32:50+08:00][sql] CONNECT:[ UseTime:0.000588s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:32:50+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.000790s ]
[2025-08-16T16:32:50+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000398s ]
[2025-08-16T16:32:50+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000347s ]
[2025-08-16T16:32:50+08:00][sql] CONNECT:[ UseTime:0.000919s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:32:50+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002102s ]
[2025-08-16T16:32:50+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000796s ]
[2025-08-16T16:33:03+08:00][sql] CONNECT:[ UseTime:0.000732s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:33:03+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002362s ]
[2025-08-16T16:33:03+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000354s ]
[2025-08-16T16:33:03+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000283s ]
[2025-08-16T16:33:03+08:00][sql] CONNECT:[ UseTime:0.001308s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:33:03+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002670s ]
[2025-08-16T16:33:03+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000785s ]
[2025-08-16T16:33:20+08:00][sql] CONNECT:[ UseTime:0.000581s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:33:20+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002809s ]
[2025-08-16T16:33:20+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000540s ]
[2025-08-16T16:33:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000368s ]
[2025-08-16T16:33:20+08:00][sql] CONNECT:[ UseTime:0.000827s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:33:20+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.003571s ]
[2025-08-16T16:33:20+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000847s ]
[2025-08-16T16:36:59+08:00][sql] CONNECT:[ UseTime:0.000822s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:36:59+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000858s ]
[2025-08-16T16:36:59+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000472s ]
[2025-08-16T16:36:59+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000755s ]
[2025-08-16T16:36:59+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000427s ]
[2025-08-16T16:36:59+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.001144s ]
[2025-08-16T16:36:59+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.000904s ]
[2025-08-16T16:36:59+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000529s ]
[2025-08-16T16:36:59+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.000521s ]
[2025-08-16T16:37:00+08:00][sql] CONNECT:[ UseTime:0.000735s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:00+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000866s ]
[2025-08-16T16:37:00+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000428s ]
[2025-08-16T16:37:00+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000815s ]
[2025-08-16T16:37:00+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000392s ]
[2025-08-16T16:37:00+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000792s ]
[2025-08-16T16:37:00+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002718s ]
[2025-08-16T16:37:00+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000655s ]
[2025-08-16T16:37:00+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000461s ]
[2025-08-16T16:37:00+08:00][sql] CONNECT:[ UseTime:0.000587s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:00+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000709s ]
[2025-08-16T16:37:00+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000473s ]
[2025-08-16T16:37:00+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000815s ]
[2025-08-16T16:37:00+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000410s ]
[2025-08-16T16:37:00+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.000763s ]
[2025-08-16T16:37:00+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000840s ]
[2025-08-16T16:37:22+08:00][sql] CONNECT:[ UseTime:0.000714s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:22+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000846s ]
[2025-08-16T16:37:22+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000363s ]
[2025-08-16T16:37:22+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000565s ]
[2025-08-16T16:37:22+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000283s ]
[2025-08-16T16:37:22+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.002745s ]
[2025-08-16T16:37:22+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.000760s ]
[2025-08-16T16:37:22+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000343s ]
[2025-08-16T16:37:22+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.000318s ]
[2025-08-16T16:37:30+08:00][sql] CONNECT:[ UseTime:0.000773s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:30+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000790s ]
[2025-08-16T16:37:30+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000479s ]
[2025-08-16T16:37:30+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000581s ]
[2025-08-16T16:37:30+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000258s ]
[2025-08-16T16:37:30+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.001708s ]
[2025-08-16T16:37:30+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.000491s ]
[2025-08-16T16:37:30+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 10,10 [ RunTime:0.000276s ]
[2025-08-16T16:37:30+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.000323s ]
[2025-08-16T16:37:32+08:00][sql] CONNECT:[ UseTime:0.000735s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:32+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000697s ]
[2025-08-16T16:37:32+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000448s ]
[2025-08-16T16:37:32+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000550s ]
[2025-08-16T16:37:32+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000397s ]
[2025-08-16T16:37:32+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.001894s ]
[2025-08-16T16:37:32+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.000574s ]
[2025-08-16T16:37:32+08:00][sql] UPDATE `robot_ai_character_dict`  SET `status` = '0'  WHERE (  (  `id` = 145 ) ) AND `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.020050s ]
[2025-08-16T16:37:34+08:00][sql] CONNECT:[ UseTime:0.014506s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:34+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000782s ]
[2025-08-16T16:37:34+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000332s ]
[2025-08-16T16:37:34+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000819s ]
[2025-08-16T16:37:34+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000372s ]
[2025-08-16T16:37:34+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.001830s ]
[2025-08-16T16:37:34+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.001814s ]
[2025-08-16T16:37:34+08:00][sql] UPDATE `robot_ai_character_dict`  SET `status` = '0'  WHERE (  (  `id` = 146 ) ) AND `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.022378s ]
[2025-08-16T16:37:34+08:00][sql] CONNECT:[ UseTime:0.000940s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:34+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.001484s ]
[2025-08-16T16:37:34+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000592s ]
[2025-08-16T16:37:34+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000992s ]
[2025-08-16T16:37:34+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000402s ]
[2025-08-16T16:37:34+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000695s ]
[2025-08-16T16:37:34+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002314s ]
[2025-08-16T16:37:34+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000422s ]
[2025-08-16T16:37:34+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character_dict` WHERE `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.000454s ]
[2025-08-16T16:37:37+08:00][sql] CONNECT:[ UseTime:0.025551s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:37+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000869s ]
[2025-08-16T16:37:37+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000524s ]
[2025-08-16T16:37:37+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000558s ]
[2025-08-16T16:37:37+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000402s ]
[2025-08-16T16:37:37+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000810s ]
[2025-08-16T16:37:37+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002159s ]
[2025-08-16T16:37:37+08:00][sql] UPDATE `robot_ai_character_dict`  SET `status` = '0'  WHERE (  (  `id` = 147 ) ) AND `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.013967s ]
[2025-08-16T16:37:38+08:00][sql] CONNECT:[ UseTime:0.000813s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:38+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000810s ]
[2025-08-16T16:37:38+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000459s ]
[2025-08-16T16:37:38+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000665s ]
[2025-08-16T16:37:38+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000326s ]
[2025-08-16T16:37:38+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000647s ]
[2025-08-16T16:37:38+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.000535s ]
[2025-08-16T16:37:38+08:00][sql] UPDATE `robot_ai_character_dict`  SET `status` = '0'  WHERE (  (  `id` = 148 ) ) AND `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.022074s ]
[2025-08-16T16:37:39+08:00][sql] CONNECT:[ UseTime:0.018696s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:39+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000787s ]
[2025-08-16T16:37:39+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000366s ]
[2025-08-16T16:37:39+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000554s ]
[2025-08-16T16:37:39+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000230s ]
[2025-08-16T16:37:39+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000856s ]
[2025-08-16T16:37:39+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002066s ]
[2025-08-16T16:37:39+08:00][sql] UPDATE `robot_ai_character_dict`  SET `status` = '0'  WHERE (  (  `id` = 149 ) ) AND `robot_ai_character_dict`.`delete_time` IS NULL [ RunTime:0.013540s ]
[2025-08-16T16:37:47+08:00][sql] CONNECT:[ UseTime:0.000962s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:47+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000759s ]
[2025-08-16T16:37:47+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000425s ]
[2025-08-16T16:37:47+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000875s ]
[2025-08-16T16:37:47+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000316s ]
[2025-08-16T16:37:47+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000838s ]
[2025-08-16T16:37:47+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.000835s ]
[2025-08-16T16:37:47+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000413s ]
[2025-08-16T16:37:47+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000515s ]
[2025-08-16T16:37:47+08:00][sql] CONNECT:[ UseTime:0.017171s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:47+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000882s ]
[2025-08-16T16:37:47+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000647s ]
[2025-08-16T16:37:47+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.001042s ]
[2025-08-16T16:37:47+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000328s ]
[2025-08-16T16:37:47+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.001862s ]
[2025-08-16T16:37:47+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000766s ]
[2025-08-16T16:37:49+08:00][sql] CONNECT:[ UseTime:0.021171s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:49+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000805s ]
[2025-08-16T16:37:49+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000367s ]
[2025-08-16T16:37:49+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000546s ]
[2025-08-16T16:37:49+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000278s ]
[2025-08-16T16:37:49+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000733s ]
[2025-08-16T16:37:49+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.001810s ]
[2025-08-16T16:37:49+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000324s ]
[2025-08-16T16:37:49+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000266s ]
[2025-08-16T16:37:49+08:00][sql] CONNECT:[ UseTime:0.001185s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:37:49+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000819s ]
[2025-08-16T16:37:49+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000367s ]
[2025-08-16T16:37:49+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.008432s ]
[2025-08-16T16:37:49+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000340s ]
[2025-08-16T16:37:49+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002906s ]
[2025-08-16T16:37:49+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000840s ]
[2025-08-16T16:44:56+08:00][sql] CONNECT:[ UseTime:0.000958s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:44:56+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000881s ]
[2025-08-16T16:44:56+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000415s ]
[2025-08-16T16:44:56+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000682s ]
[2025-08-16T16:44:56+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000333s ]
[2025-08-16T16:44:56+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000859s ]
[2025-08-16T16:44:56+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.001377s ]
[2025-08-16T16:44:56+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000590s ]
[2025-08-16T16:44:56+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000596s ]
[2025-08-16T16:44:56+08:00][sql] CONNECT:[ UseTime:0.001763s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:44:56+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.001752s ]
[2025-08-16T16:44:56+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000727s ]
[2025-08-16T16:44:56+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.000854s ]
[2025-08-16T16:44:56+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000471s ]
[2025-08-16T16:44:56+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.002787s ]
[2025-08-16T16:44:56+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.000947s ]
[2025-08-16T16:45:07+08:00][sql] CONNECT:[ UseTime:0.000802s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:45:07+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000675s ]
[2025-08-16T16:45:07+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000459s ]
[2025-08-16T16:45:07+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.001178s ]
[2025-08-16T16:45:07+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000441s ]
[2025-08-16T16:45:07+08:00][sql] SHOW FULL COLUMNS FROM `sys_menu` [ RunTime:0.000888s ]
[2025-08-16T16:45:07+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character` [ RunTime:0.002097s ]
[2025-08-16T16:45:07+08:00][sql] SELECT * FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL ORDER BY `id` DESC LIMIT 0,10 [ RunTime:0.000357s ]
[2025-08-16T16:45:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `robot_ai_character` WHERE `robot_ai_character`.`delete_time` IS NULL [ RunTime:0.000311s ]
[2025-08-16T16:45:07+08:00][sql] CONNECT:[ UseTime:0.016367s ] mysql:host=127.0.0.1;port=3306;dbname=robot_friends;charset=utf8mb4
[2025-08-16T16:45:07+08:00][sql] SHOW FULL COLUMNS FROM `sys_user` [ RunTime:0.000866s ]
[2025-08-16T16:45:07+08:00][sql] SELECT * FROM `sys_user` WHERE (  (  `id` = 1  AND `status` = '1' ) ) AND `sys_user`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000591s ]
[2025-08-16T16:45:07+08:00][sql] SHOW FULL COLUMNS FROM `sys_user_role` [ RunTime:0.001182s ]
[2025-08-16T16:45:07+08:00][sql] SELECT `role_id` FROM `sys_user_role` WHERE  (  `user_id` = 1 ) [ RunTime:0.000368s ]
[2025-08-16T16:45:07+08:00][sql] SHOW FULL COLUMNS FROM `robot_ai_character_dict` [ RunTime:0.000615s ]
[2025-08-16T16:45:07+08:00][sql] SELECT * FROM `robot_ai_character_dict` WHERE (  `status` = '1' ) AND `robot_ai_character_dict`.`delete_time` IS NULL ORDER BY `dict_type` ASC,`sorted` ASC [ RunTime:0.001080s ]
