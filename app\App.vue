<script>
	import { mapActions, mapState } from 'vuex';
	// #ifdef H5
	import { getWxJssdkConfig, oneKeyWxLogin } from "@/utils/wx.js";
	// #endif
	import { isWeiXinBrowser, getCurrentPagePath,isTokenExpired } from "@/utils/common.js";
	
	export default {
		onLaunch: function() {
			console.log('App Launch')
			// uni.hideTabBar();

			// #ifdef H5
			this.getUserInfo();
			// getCurrentPagePath();
			// let iswx = isWeiXinBrowser();
			// this.getUserInfo().then(res => {
				
			// }).catch(err => {
				
			// })
			// #endif
			
			// #ifdef MP

			const updateManager = uni.getUpdateManager();
			updateManager.onCheckForUpdate(function(res) {
				// 请求完新版本信息的回调
			});

			updateManager.onUpdateReady(function(res) {
				uni.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					success(res) {
						if (res.confirm) {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate();
						}
					}
				});
			});

			updateManager.onUpdateFailed(function(res) {
				// 新的版本下载失败
			});
			// this.getShowPay();
			if(isTokenExpired()){
				this.weappAutoLogin().then(res => {
					this.getUserInfo();
				})
			}else{
				this.getUserInfo();
			}
			// #endif
		},
		methods: {
			...mapActions({
				getUserInfo: 'getUserInfo',
				weappAutoLogin: 'weappAutoLogin',
				getShowPay: 'getShowPay',
			}),
		},
		onShow: function() {
			console.log('App Show')
			// uni.hideTabBar();
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	/* start--文本行数限制--start */
	@import './static/style/thorui-extend.css';

	page {
		background-color: #F3F4F6;
	}

	view {
		box-sizing: border-box;
	}

	.h-line-1 {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.h-line-2 {
		-webkit-line-clamp: 2;
	}

	.h-line-3 {
		-webkit-line-clamp: 3;
	}

	.h-line-4 {
		-webkit-line-clamp: 4;
	}

	.h-line-5 {
		-webkit-line-clamp: 5;
	}

	.h-line-2,
	.h-line-3,
	.h-line-4,
	.h-line-5 {
		overflow: hidden;
		word-break: break-all;
		text-overflow: ellipsis;
		display: -webkit-box; // 弹性伸缩盒
		-webkit-box-orient: vertical; // 设置伸缩盒子元素排列方式
	}

	.t-red {
		color: #E1251B !important;
	}

	/* end--文本行数限制--end */

	.bc-white {
		background-color: #FFFFFF;
	}

	.t-bold {
		font-weight: 700 !important;
	}

	.n-link {
		color: #2B85E4;
		text-decoration: underline;
	}

	.btn-wrap {
		padding: 40rpx 60rpx;
	}

	.tips{
		font-size: 28rpx;
		color: #E1251B;
	}
	.pd-20{
		padding-bottom: 20rpx;
	}
	// 默认样式
	button {
		position: relative;
		display: block;
		margin-left: auto;
		margin-right: auto;
		padding-left: 0;
		padding-right: 0;
		box-sizing: border-box;
		font-size: 18px;
		text-align: center;
		text-decoration: none;
		// line-height:1;
		border-radius: 5px;
		-webkit-tap-highlight-color: transparent;
		overflow: hidden;
		color: #000000;
		background-color: #fff;
	}

	button {
		background-color: transparent;
		border: none;
		padding: 0;
		color: inherit;
		/* 或者指定你想要的颜色 */
		font-size: inherit;
		line-height: inherit;
	}

	// 边框样式
	button::after {
		border: 0;
	}

	// 禁用时样式
	button[disabled] {
		background-color: #fff;
		color: #666;
	}

	// 点击时样式
	.button-hover {
		color: rgba(0, 0, 0, 0.6);
		background-color: #fff;
	}
	
	
</style>