<template>
	<view class="tui-banner-swiper">
	 <swiper class="tui-banner__height" @change="bannerChange" circular :indicator-dots="false" autoplay :interval="4000"
	  :duration="150">
	 	<block v-for="(item,index) in background" :key="index">
	 		<swiper-item>
	 			<view class="tui-swiper-item tui-banner__height" :class="[item]">Thor UI</view>
	 		</swiper-item>
	 	</block>
	 </swiper>
	 <tui-swiper-dot :type="1" :count="count" :current="current"></tui-swiper-dot>
	</view>
</template>

<script>
	export default {
	 data() {
	 	return {
	 		background: ['tui-bg__primary', 'tui-bg__green', 'tui-bg__white'],
	 		current: 0,
	 		count: 0
	 	}
	 },
	 onLoad() {
	 	this.count = this.background.length;
	 },
	 methods: {
	 	bannerChange: function(e) {
	 		this.current = e.detail.current;
	 	}
	 }
	}
</script>

<style lang="scss" scoped>
	.tui-banner-swiper {
		position: relative;
	}
	.tui-swiper-item {
		width: 100%;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 34rpx;
		font-weight: 600;
	}
	
	.tui-banner__height {
		height: 336rpx;
	}
	
	.tui-bg__primary {
		background-color: rgba(0, 0, 0, .8);
	}
	
	.tui-bg__green {
		background-color: #07c160;
	}
	
	.tui-bg__white {
		background-color: #F2F2F2;
		color: #000;
	}
</style>