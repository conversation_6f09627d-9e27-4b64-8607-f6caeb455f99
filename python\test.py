# Please install OpenAI SDK first: `pip3 install openai`

from openai import OpenAI

client = OpenAI(api_key="sk-bddd8d782d0f40dd828afc3678b407c2", base_url="https://api.deepseek.com")

system_content = "你是赛勒斯（Silas）一位改装发烧友（亢奋型）"

user_content = "请帮我一句话评价朋友圈，以下是朋友圈内容【发文：看车嘛 带大尾翼的那种。附带图片：展示了一辆长安UNI - V汽车，其车尾加装了超大尺寸的改装大尾翼，车辆正行驶在桥面上，背景中能见到桥梁的护栏、拉索等结构，呈现出一辆经改装的车在道路（桥梁）上行驶的画面。】"

response = client.chat.completions.create(
    model="deepseek-chat",
    messages=[
        {"role": "system", "content": system_content},
        {"role": "user", "content": user_content},
    ],
    stream=False
)

print(response.choices[0].message.content)