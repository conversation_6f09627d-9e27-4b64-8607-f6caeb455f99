# 调试异常类 (DebugException)

这是一个专为调试目的设计的异常类，可以处理各种数据格式，包括字符串、数组、对象和 JSON 等。

## 特点

- 支持多种数据类型：字符串、数组、对象、JSON 等
- 自动识别和处理不同的数据类型
- 提供静态方法快速抛出调试异常或打印调试信息
- 保留原始数据和数据类型信息
- 对对象进行智能转换，支持 JsonSerializable 接口和反射获取私有属性

## 基本用法

### 抛出调试异常

```php
use app\lib\exception\DebugException;

// 抛出包含字符串的调试异常
throw new DebugException('这是一个测试字符串', 9999, '字符串调试');

// 抛出包含数组的调试异常
$data = ['name' => '张三', 'age' => 25];
throw new DebugException($data, 9999, '数组调试');

// 抛出包含对象的调试异常
$user = new User();
throw new DebugException($user, 9999, '对象调试');

// 抛出包含JSON的调试异常
$jsonString = '{"name":"王五","age":35}';
throw new DebugException($jsonString, 9999, 'JSON调试');
```

### 使用静态方法

```php
use app\lib\exception\DebugException;

// 使用静态方法快速抛出调试异常
DebugException::dump(['id' => 1, 'name' => '赵六'], '使用dump方法');

// 使用静态方法打印调试信息（不抛出异常，继续执行）
DebugException::print(['id' => 2, 'name' => '钱七'], '使用print方法');
```

## 捕获和处理调试异常

```php
use app\lib\exception\DebugException;

try {
    // 抛出调试异常
    throw new DebugException($someData, 9999, '调试信息');
} catch (DebugException $e) {
    echo "捕获到调试异常：" . $e->getMessage() . "\n";
    echo "数据类型：" . $e->getDataType() . "\n";
    echo "调试数据：";
    print_r($e->getData());
}
```

## 可用方法

### 构造函数

```php
/**
 * @param mixed $data 调试数据，支持字符串、数组、对象、JSON等
 * @param int $code 错误码
 * @param string $message 自定义错误消息
 * @param Throwable|null $previous 上一个异常
 */
public function __construct($data = null, int $code = 9999, string $message = '调试异常', ?Throwable $previous = null)
```

### 实例方法

- `getRawData()`: 获取原始数据
- `getDataType()`: 获取数据类型
- `getData()`: 获取处理后的数据（从父类 BaseException 继承）

### 静态方法

- `dump($data, string $message = '调试数据')`: 快速抛出调试异常
- `print($data, string $message = '调试数据')`: 打印调试信息（不抛出异常）

## 示例代码

详细的使用示例请参考 `DebugExceptionExample.php` 文件，其中包含了各种数据类型的使用示例。

## 在控制器中使用

```php
use app\lib\exception\DebugException;

class UserController
{
    public function index()
    {
        try {
            $user = $this->getUserData();
            
            // 调试用户数据
            DebugException::dump($user, '用户数据');
            
            // 或者不中断执行，只打印调试信息
            DebugException::print($user, '用户数据');
            
            return $user;
        } catch (DebugException $e) {
            // 处理调试异常
            return json([
                'code' => $e->getCode(),
                'msg' => $e->getMessage(),
                'data' => $e->getData()
            ]);
        }
    }
}
```

## 注意事项

1. 调试异常主要用于开发环境，不建议在生产环境中使用
2. 使用 `print` 方法时，会直接输出 HTML，适合在浏览器环境中使用
3. 对于复杂的嵌套对象，可能需要自定义转换逻辑
