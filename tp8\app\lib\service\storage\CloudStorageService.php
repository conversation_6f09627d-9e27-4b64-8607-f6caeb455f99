<?php
declare(strict_types=1);

namespace app\lib\service\storage;

use app\lib\exception\dogadmin\FileException;
use think\facade\Config;
use think\File;

/**
 * 云存储服务基类
 * 支持七牛云、腾讯云COS、阿里云OSS
 */
abstract class CloudStorageService
{
    /**
     * 配置信息
     * @var array
     */
    protected array $config = [];
    
    /**
     * 存储类型
     * @var string
     */
    protected string $storageType = '';
    
    /**
     * 构造函数
     * 
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->init();
    }
    
    /**
     * 初始化
     */
    abstract protected function init(): void;
    
    /**
     * 上传文件
     * 
     * @param File|string $file 文件对象或本地文件路径
     * @param string $path 上传路径
     * @param string $fileName 文件名（可选）
     * @param string|bool $nameMode 文件名模式：
     *                             - 字符串: 使用自定义文件名（保留扩展名）
     *                             - true: 使用原始文件名
     *                             - false: 生成唯一文件名（默认）
     * @return array 上传结果
     */
    abstract public function upload($file, string $path = '', string $fileName = '', $nameMode = false): array;
    
    /**
     * 删除文件
     * 
     * @param string $key 文件key
     * @return bool
     */
    abstract public function delete(string $key): bool;
    
    /**
     * 获取文件URL
     * 
     * @param string $key 文件key
     * @return string
     */
    abstract public function getUrl(string $key): string;
    
    /**
     * 检查文件是否存在
     * 
     * @param string $key 文件key
     * @return bool
     */
    abstract public function exists(string $key): bool;
    
    /** 
     * 生成文件名 
     * 
     * @param string $originalName 原始文件名 
     * @param string $prefix 前缀 
     * @param string|bool $nameMode 文件名模式：
     *                             - 字符串: 使用自定义文件名（保留扩展名）
     *                             - true: 使用原始文件名
     *                             - false: 生成唯一文件名（默认）
     * @return string 
     */ 
    protected function generateFileName(string $originalName, string $prefix = '', $nameMode = false): string 
    { 
        // 获取文件扩展名
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        
        // 根据不同模式生成文件名
        if (is_string($nameMode) && !empty($nameMode)) {
            // 模式1: 使用自定义文件名，完全按照传入的名称使用
            $fileName = $prefix . $nameMode;
        } elseif ($nameMode === true) {
            // 模式2: 使用原始文件名
            $fileName = $prefix . basename($originalName);
        } else {
            // 模式3: 生成唯一文件名（默认）
            $fileName = $prefix . date('YmdHis') . '_' . uniqid() . '.' . $extension; 
        }
        
        return $fileName; 
    }
    
    /**
     * 验证文件
     * 
     * @param File $file 文件对象
     * @param array $rules 验证规则
     * @throws FileException
     */
    protected function validateFile(File $file, array $rules = []): void
    {
        // 检查文件大小
        if (isset($rules['maxSize']) && $file->getSize() > $rules['maxSize']) {
            throw new FileException([
                'file' => $file->getOriginalName(),
                'size' => $file->getSize(),
                'maxSize' => $rules['maxSize']
            ], 1); // 1 = 文件大小超出限制
        }
        
        // 检查文件类型
        if (isset($rules['allowedTypes']) && !in_array($file->extension(), $rules['allowedTypes'])) {
            throw new FileException([
                'file' => $file->getOriginalName(),
                'type' => $file->extension(),
                'allowedTypes' => $rules['allowedTypes']
            ], 3); // 3 = 文件类型不允许
        }
    }
    
    /**
     * 获取文件类型
     * 
     * @param string $extension 文件扩展名
     * @return string
     */
    protected function getFileType(string $extension): string
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        $documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt','csv','json'];
        $audioTypes = ['mp3', 'wav', 'flac', 'aac', 'ogg'];
        $videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'];
        $archiveTypes = ['zip', 'rar', '7z', 'tar', 'gz'];
        $appTypes = ['exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm'];
        
        $extension = strtolower($extension);
        
        if (in_array($extension, $imageTypes)) {
            return '1'; // 图片
        } elseif (in_array($extension, $documentTypes)) {
            return '2'; // 文档
        } elseif (in_array($extension, $audioTypes)) {
            return '3'; // 音频
        } elseif (in_array($extension, $videoTypes)) {
            return '4'; // 视频
        } elseif (in_array($extension, $archiveTypes)) {
            return '5'; // 压缩包
        } elseif (in_array($extension, $appTypes)) {
            return '6'; // 应用程序
        } else {
            return '9'; // 其他
        }
    }
    
    /**
     * 格式化文件大小
     * 
     * @param int $size 文件大小（字节）
     * @return string
     */
    protected function formatFileSize(int $size): string
    {
        if ($size < 1024) {
            return $size . 'B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . 'KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / (1024 * 1024), 2) . 'MB';
        } else {
            return round($size / (1024 * 1024 * 1024), 2) . 'GB';
        }
    }
    
    /**
     * 统一获取文件信息（支持File对象和本地路径）
     * @param File|string $file
     * @return array [originalName, extension, mime, size, realPath]
     */
    public function parseFileInfo($file): array
    {
        if ($file instanceof File) {
            return [
                'originalName' => $file->getOriginalName(),
                'extension' => $file->extension(),
                'mime' => $file->getMime(),
                'size' => $file->getSize(),
                'realPath' => $file->getRealPath(),
            ];
        } elseif (is_string($file) && is_file($file)) {
            return [
                'originalName' => basename($file),
                'extension' => pathinfo($file, PATHINFO_EXTENSION),
                'mime' => mime_content_type($file),
                'size' => filesize($file),
                'realPath' => $file,
            ];
        } else {
            throw new FileException(['message' => '无效的文件类型'], 6);
        }
    }
}