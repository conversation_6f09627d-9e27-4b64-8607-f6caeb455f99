<?php
use think\facade\Route;
use app\common_api\middleware\ApiCheck;

Route::group('game', function () {
    Route::post('getGameJsonCache', 'app\game_api\controller\Game@getGameJsonCache');
    Route::get('updateDefaultData', 'app\game_api\controller\GamePlayer@updateDefaultData');

});

Route::group('gameUser', function () {
    // 获取用户全部信息
    Route::post('getGameUserByUserId', 'app\game_api\controller\GameUser@getGameUserByUserId');

    // 获取或创建游戏用户
    Route::post('getOrCreateGameUser', 'app\game_api\controller\GameUser@getOrCreateGameUser');

    // 创建角色
    Route::post('createCharacter', 'app\game_api\controller\GameUser@createCharacter');

    // 更新角色基本信息
    Route::post('updateCharacterBasic', 'app\game_api\controller\GameUser@updateCharacterBasic');

    // 更新游戏用户信息
    Route::post('updateGameUser', 'app\game_api\controller\GameUser@updateGameUser');

    // 删除游戏用户
    Route::post('deleteGameUser', 'app\game_api\controller\GameUser@deleteGameUser');

    // 更新玩家数据（用于前端修炼逻辑）
    Route::post('updatePlayerData', 'app\game_api\controller\GameUser@updatePlayerData');

})->middleware([ApiCheck::class]);


