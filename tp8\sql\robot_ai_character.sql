/*
 Navicat Premium Data Transfer

 Source Server         : local
 Source Server Type    : MySQL
 Source Server Version : 50740 (5.7.40)
 Source Host           : localhost:3306
 Source Schema         : robot_friends

 Target Server Type    : MySQL
 Target Server Version : 50740 (5.7.40)
 File Encoding         : 65001

 Date: 16/08/2025 15:32:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for robot_ai_character
-- ----------------------------
DROP TABLE IF EXISTS `robot_ai_character`;
CREATE TABLE `robot_ai_character`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '昵称',
  `gender` enum('male','female','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像URL',
  `occupation` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职业',
  `personality` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '性格描述',
  `speaking_style` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '说话风格',
  `catchphrases` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '口头禅',
  `forbidden_words` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '禁用词汇',
  `emoji_preference` enum('high','medium','low','none') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'medium' COMMENT 'Emoji使用频率',
  `topic_preference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '擅长评论的主题（JSON）',
  `comment_length_range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '10-30字' COMMENT '评论长度偏好',
  `response_speed` enum('fast','normal','slow') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'normal' COMMENT '响应速度',
  `active_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '全天' COMMENT '活跃时间段',
  `backstory` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '背景故事',
  `language_preference` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '中文' COMMENT '语言偏好',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态标识',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间（软删除标识）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI人物表（用于自动评论朋友圈）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of robot_ai_character
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
