<?php

namespace app\game_api\model;

use think\Model;

class CommonUser extends Model
{
    protected $table = 'common_user';
    
    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'union_id' => 'string',
        'openid_mp' => 'string',
        'openid_qq' => 'string',
        'username' => 'string',
        'password' => 'string',
        'nickname' => 'string',
        'avatar_url' => 'string',
        'phone' => 'string',
        'email' => 'string',
        'coin' => 'int',
        'money' => 'decimal',
        'gender' => 'string',
        'country' => 'string',
        'province' => 'string',
        'city' => 'string',
        'status' => 'string',
        'platform' => 'string',
        'region' => 'string',
        'game_id' => 'string',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'delete_time' => 'datetime'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 软删除
    protected $deleteTime = 'delete_time';
    
    /**
     * 关联排行榜记录
     * @return \think\model\relation\HasMany
     */
    public function rankRecords()
    {
        return $this->hasMany(GamePlayerRank::class, 'user_id', 'id')
            ->where('status', '1');
    }
    
    /**
     * 获取用户公开信息
     * @param int $userId
     * @return array|null
     */
    public static function getUserPublicInfo($userId)
    {
        if (empty($userId)) {
            return null;
        }
        
        $user = self::where('id', $userId)
            ->where('status', '1')
            ->field('id,nickname,avatar_url')
            ->find();
            
        return $user ? $user->toArray() : null;
    }
}