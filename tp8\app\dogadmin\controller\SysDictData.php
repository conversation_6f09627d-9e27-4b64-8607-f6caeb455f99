<?php

namespace app\dogadmin\controller;

use app\dogadmin\common\ApiResponse;
use app\dogadmin\service\SysDictDataService;

class SysDictData extends Base
{
    /**
     * @var SysDictDataService
     */
    protected $service;
    public function initialize(): void
    {
        $this->service = new SysDictDataService();
        $this->params = $this->request->param();
        $this->searchKey = [
            ['dict_type'=>'='],
            ['dict_label'=>'like'],
        ];
    }

    public function getDictDataByType()
    {
        $params = $this->params;
        $res = $this->service->getDictDataByType($params);
        return ApiResponse::success($res, '获取字典数据成功');
    }
}