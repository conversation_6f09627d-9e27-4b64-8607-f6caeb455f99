<?php

namespace app\game_api\listener;

use app\common_api\event\UserRegistered;
use app\game_api\lib\exception\GameApiException;
use app\game_api\service\GameUserService;
use app\lib\mylog\MyLog;
use app\lib\exception\DebugException;
use think\facade\Log;

/**
 * 用户注册成功事件监听器
 */
class UserRegisteredListener
{
    /**
     * 处理用户注册成功事件
     *
     * @param UserRegistered $event
     * @return void
     */
    public function handle(UserRegistered $event): void
    {
        try {
            // 获取用户ID
            $userId = $event->userId;
            
            // 记录用户注册日志
            MyLog::info('用户注册成功，开始初始化游戏数据', ['user_id' => $userId]);
            
            // 实例化GameUserService
            $gamePlayerService = new GameUserService();
            
            // 初始化用户游戏数据
            $player = $gamePlayerService->getOrCreateGameUser($userId);
            
            // 记录初始化成功日志
            MyLog::info('用户游戏数据初始化成功', [
                'user_id' => $userId
            ]);
            
            // TODO: 在这里可以添加更多注册成功后的操作
            // 例如：
            // 1. 发送欢迎邮件
            // 2. 发放新用户奖励
            // 3. 触发其他相关业务逻辑
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('用户游戏数据初始化失败', [
                'user_id' => $event->userId ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 如果是开发环境，可以使用DebugException打印更多信息
            if (env('APP_DEBUG', false)) {
//                DebugException::print([
//                    'message' => '用户游戏数据初始化失败',
//                    'user_id' => $event->userId ?? 'unknown',
//                    'error' => $e->getMessage(),
//                    'file' => $e->getFile(),
//                    'line' => $e->getLine()
//                ], '用户注册监听器错误');
                throw new GameApiException('用户游戏数据初始化失败');
            }
        }
    }
}
