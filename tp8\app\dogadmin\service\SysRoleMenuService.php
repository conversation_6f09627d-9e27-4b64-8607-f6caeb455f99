<?php
namespace app\dogadmin\service;

use app\dogadmin\model\SysRoleMenuModel;

class SysRoleMenuService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysRoleMenuModel();
    }

    public function getRoleMenuByRoleId($roleId){
        $where  = [
            ['role_id','=',$roleId]
        ];
        $menuIds = $this->model->where($where)->column('menu_id');
        return $menuIds;
    }

    public function saveRoleMenu($params)
    {
        $where = [
            ['role_id','=',$params['role_id']]
        ];
        $this->model->where($where)->delete();
        $list = [];
        if($params['menu_ids']){
            foreach ($params['menu_ids'] as $v){
                $list[] = ['role_id'=>$params['role_id'],'menu_id'=>$v];
            }
            $this->model->saveAll($list);
        }
        return $list;
    }

} 