// 导入二次封装axios
import koi from "@/utils/axios.ts";

// 统一管理接口
enum API {
  LIST_PAGE = "/dogadmin/sysMenu/listPage",
  CASCADER_LIST = "/dogadmin/sysMenu/cascaderList",
  GET_BY_ID = "/dogadmin/sysMenu/getById",
  UPDATE = "/dogadmin/sysMenu/update",
  ADD = "/dogadmin/sysMenu/add",
  DELETE = "/dogadmin/sysMenu/deleteById",
  BATCH_DELETE = "/dogadmin/sysMenu/batchDelete",
  UPDATE_STATUE = "/dogadmin/sysMenu/updateStatus",
  UPDATE_SPREAD = "/dogadmin/sysMenu/updateSpread",
  LIST_ROUTERS = "/dogadmin/sysMenu/listRouters",
  LIST_MENU_NORMAL = "/dogadmin/sysMenu/listMenuNormal",
  LIST_MENUIDS_BY_ROLEID = "/dogadmin/sysMenu/listMenuIdsByRoleId",
  SAVE_ROLE_MENU = "/dogadmin/sysMenu/saveRoleMenu",
  ADD_BUTTON_AUTH = "/dogadmin/sysMenu/addButtonAuth"
  
}

// 多条件进行查询
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 菜单级联下拉框
export const cascaderList = () => {
  return koi.get(API.CASCADER_LIST);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "?id=" + id);
};

// 根据ID进行修改
export const update = (data: any) => {
  return koi.post(API.UPDATE, data);
};

// 添加
export const add = (data: any) => {
  return koi.post(API.ADD, data);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE + "?id=" + id);
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids:ids});
};

// 修改状态
export const updateStatus = (id: any, status: any) => {
  return koi.post(API.UPDATE_STATUE, { id: id, status });
};

// 修改状态
export const updateSpread = (id: any, spread: any) => {
  return koi.post(API.UPDATE_SPREAD, { id: id, spread });
};

// 获取用户左侧菜单数据
export const listRouters = (id: any) => {
  return koi.get(API.LIST_ROUTERS + "/" + id);
};

// 获取正常所有菜单数据
export const listMenuNormal = () => {
  return koi.get(API.LIST_MENU_NORMAL);
};

// 根据用户拥有的角色ID查询权限菜单
export const listMenuIdsByRoleId = (id: any) => {
  return koi.get(API.LIST_MENUIDS_BY_ROLEID + "?id=" + id);
};
// 一键生成权限按钮
export const addButtonAuth = (data: any) => {
  return koi.get(API.ADD_BUTTON_AUTH, data);
};



// 保存角色和菜单权限之间的关系
export function saveRoleMenu(role_id: any, menu_ids: any) {
  // 处理如果没有选择菜单数据。无法匹配后台数据的问题
  if (menu_ids.length === 0) {
    menu_ids = [];
  }
  return koi.post(API.SAVE_ROLE_MENU, { role_id, menu_ids });
}
