<?php

namespace app\dogadmin\service;

use app\dogadmin\model\SysDeptModel;
use app\dogadmin\model\SysRoleDeptModel;
use app\dogadmin\model\SysRoleModel;
use app\dogadmin\model\SysUserDeptModel;
use app\dogadmin\model\SysUserModel;
use app\dogadmin\model\SysUserRoleModel;

/**
 * 通用基础服务类，适用于 ThinkORM
 */
class BaseService
{
    /**
     * @var \think\Model|null 当前服务的模型实例
     */
    protected $model = null;

    /**
     * @var array 需要自动 JSON 编解码的字段
     */
    protected $jsonFields = [];

    /**
     * @var bool 是否启用数据权限过滤
     */
    protected $dataScope = false;

    /**
     * @var string 用户ID字段名
     */
    protected $userIdField = 'admin_id';

    /**
     * @var string 部门ID字段名
     */
    protected $deptIdField = 'dept_id';
    public function getModel()
    {
        return $this->model;
    }

    /**
     * 构建 where 条件，支持等值、模糊、范围、IN、BETWEEN
     * @param array $params
     * @param array $where
     * @param array $searchKeys
     * @return array
     *
     * 【前端传参使用示例】
     *
     * 1. 等值查询：
     *    {
     *      "username": "admin",
     *      "status": 1
     *    }
     *
     * 2. 模糊查询（需后端 $searchFields 配置）：
     *    {
     *      "nickname": "张三"
     *    }
     *
     * 3. 范围查询（BETWEEN）：
     *    {
     *      "created_at": ["2024-01-01", "2024-01-31"]
     *    }
     *
     * 4. IN 查询：
     *    {
     *      "role_id": [1, 2, 3]
     *    }
     *
     * 5. 分页与排序：
     *    {
     *      "page_no": 1,
     *      "page_size": 10,
     *      "order": {"id": "desc"}
     *    }
     *
     * 6. 混合查询：
     *    {
     *      "username": "admin",
     *      "status": 1,
     *      "role_id": [1, 2],
     *      "created_at": ["2024-01-01", "2024-01-31"]
     *    }
     *
     * 说明：只需传递需要查询的字段，未传递的字段不会参与查询。模糊查询字段需后端 $searchFields 配置。范围/IN 查询均通过数组实现，长度为2为 BETWEEN，否则为 IN。
     */
    public function buildWhere(array $params, array $where, array $searchKeys): array
    {
        foreach ($searchKeys as $search) {
            // 获取字段名和操作符
            $field = key($search);
            $op = current($search);

            // 如果参数中没有这个字段或值为空，则跳过
            if (!isset($params[$field]) || $params[$field] === '' || $params[$field] === null) {
                continue;
            }

            $value = $params[$field];

            // 根据不同的操作符处理查询条件
            switch (strtoupper($op)) {
                case 'LIKE':
                    $where[] = [$field, 'like', "%{$value}%"];
                    break;
                case 'NOT LIKE':
                    $where[] = [$field, 'not like', "%{$value}%"];
                    break;
                case 'LEFT LIKE':
                    $where[] = [$field, 'like', "%{$value}"];
                    break;
                case 'RIGHT LIKE':
                    $where[] = [$field, 'like', "{$value}%"];
                    break;
                case 'IN':
                    $value = is_array($value) ? $value : explode(',', $value);
                    $where[] = [$field, 'in', $value];
                    break;
                case 'NOT IN':
                    $value = is_array($value) ? $value : explode(',', $value);
                    $where[] = [$field, 'not in', $value];
                    break;
                case 'BETWEEN':
                    if (is_array($value) && count($value) === 2) {
                        $where[] = [$field, 'between', $value];
                    }
                    break;
                case 'NOT BETWEEN':
                    if (is_array($value) && count($value) === 2) {
                        $where[] = [$field, 'not between', $value];
                    }
                    break;
                case 'NULL':
                    $where[] = [$field, 'null', ''];
                    break;
                case 'NOT NULL':
                    $where[] = [$field, 'not null', ''];
                    break;
                case 'EXP':
                    $where[] = [$field, 'exp', $value];
                    break;
                case 'FIND_IN_SET':
                    $where[] = [$field, 'find in set', $value];
                    break;
                case '>':
                    $where[] = [$field, '>', $value];
                    break;
                case '>=':
                    $where[] = [$field, '>=', $value];
                    break;
                case '<':
                    $where[] = [$field, '<', $value];
                    break;
                case '<=':
                    $where[] = [$field, '<=', $value];
                    break;
                case '<>':
                case '!=':
                    $where[] = [$field, '<>', $value];
                    break;
                case 'BETWEEN TIME':
                    if (is_array($value) && count($value) === 2) {
                        $where[] = [$field, 'between time', $value];
                    }
                    break;
                case '> TIME':
                    $where[] = [$field, '> time', $value];
                    break;
                case '< TIME':
                    $where[] = [$field, '< time', $value];
                    break;
                case '>= TIME':
                    $where[] = [$field, '>= time', $value];
                    break;
                case '<= TIME':
                    $where[] = [$field, '<= time', $value];
                    break;
                case 'REGEXP':
                    $where[] = [$field, 'regexp', $value];
                    break;
                case 'NOT REGEXP':
                    $where[] = [$field, 'not regexp', $value];
                    break;
                case '=':
                default:
                    $where[] = [$field, '=', $value];
            }
        }

        // 添加数据权限过滤
        if ($this->dataScope) {
            $dataScopeWhere = $this->getDataScopeWhere();
            if (!empty($dataScopeWhere)) {
                $where = array_merge($where, $dataScopeWhere);
            }
        }

        return $where;
    }

    /**
     * 获取数据权限过滤条件
     * @return array
     */
    protected function getDataScopeWhere(): array
    {
        $where = [];
        $userId = request()->admin_id;
        if (empty($userId)) {
            return $where;
        }

        // 如果是超级管理员，不进行数据过滤
        if ($userId == 1) {
            return $where;
        }

        // 获取用户信息
        $userModel = new SysUserModel();
        $user = $userModel->where('id', $userId)->find();
        if (empty($user)) {
            return $where;
        }

        // 获取用户部门
        $userDeptModel = new SysUserDeptModel();
        $deptId = $userDeptModel->where('user_id', $userId)->value('dept_id');
        
        // 获取用户角色数据权限
        $roleDataScope = $this->getUserRoleDataScope($userId);
        
        switch ($roleDataScope) {
            case '1': // 全部数据权限
                break;
            case '2': // 自定义数据权限
                // 获取用户角色
                // $userRoleModel = new SysUserRoleModel();
                // $roleIds = $userRoleModel->where('user_id', $userId)->column('role_id');
                $roleIds = request()->role_ids;
                
                if (!empty($roleIds)) {
                    // 获取角色关联的部门
                    $roleDeptModel = new SysRoleDeptModel();
                    $deptIds = $roleDeptModel->whereIn('role_id', $roleIds)->column('dept_id');
                    
                    if (!empty($deptIds)) {
                        $where[] = [$this->deptIdField, 'in', array_unique($deptIds)];
                    }
                }
                break;
            case '3': // 本部门数据权限
                if (!empty($deptId)) {
                    $where[] = [$this->deptIdField, '=', $deptId];
                }
                break;
            case '4': // 本部门及以下数据权限
                if (!empty($deptId)) {
                    $deptIds = $this->getChildDeptIds($deptId);
                    $deptIds[] = $deptId;
                    $where[] = [$this->deptIdField, 'in', $deptIds];
                }
                break;
            case '5': // 仅本人数据权限
                $where[] = [$this->userIdField, '=', $userId];
                break;
            default:
                $where[] = [$this->userIdField, '=', $userId];
        }

        return $where;
    }

    /**
     * 获取用户角色的数据权限范围
     * @param int $userId 用户ID
     * @return string
     */
    protected function getUserRoleDataScope(int $userId): string
    {
        // 获取用户角色
        // $userRoleModel = new SysUserRoleModel();
        // 先获取用户的角色ID
        // $roleIds = $userRoleModel->where('user_id', $userId)->column('role_id');

        $roleIds = request()->role_ids;
        
        if (empty($roleIds)) {
            return '5'; // 如果没有角色，默认仅本人数据权限
        }
        
        // 获取角色的数据权限范围
        $roleModel = new SysRoleModel();
        $dataScope = $roleModel->whereIn('id', $roleIds)->min('data_scope');
        
        return $dataScope ?: '5'; // 默认仅本人数据权限
    }

    /**
     * 获取自定义权限部门IDs
     * @param int $roleId 角色ID
     * @return array
     */
    protected function getCustomDeptIds(int $roleId): array
    {
        $roleDeptModel = new SysRoleDeptModel();
        return $roleDeptModel->where('role_id', $roleId)
            ->column('dept_id');
    }

    /**
     * 获取子部门IDs
     * @param int $deptId 部门ID
     * @return array
     */
    protected function getChildDeptIds(int $deptId): array
    {
        static $allDepts = null;
        static $deptTree = [];
        
        // 一次性获取所有部门数据
        if ($allDepts === null) {
            $deptModel = new SysDeptModel();
            $allDepts = $deptModel->field('id, parent_id')->select()->toArray();
            
            // 构建部门树
            foreach ($allDepts as $dept) {
                $parentId = $dept['parent_id'];
                if (!isset($deptTree[$parentId])) {
                    $deptTree[$parentId] = [];
                }
                $deptTree[$parentId][] = $dept['id'];
            }
        }
        
        // 递归获取子部门ID
        $childIds = [];
        $this->getChildDeptIdsFromTree($deptId, $deptTree, $childIds);
        
        return $childIds;
    }
    
    /**
     * 从部门树中递归获取子部门IDs
     * @param int $deptId 部门ID
     * @param array $deptTree 部门树
     * @param array &$childIds 子部门ID集合
     */
    private function getChildDeptIdsFromTree(int $deptId, array $deptTree, array &$childIds): void
    {
        if (!isset($deptTree[$deptId])) {
            return;
        }
        
        foreach ($deptTree[$deptId] as $childId) {
            $childIds[] = $childId;
            $this->getChildDeptIdsFromTree($childId, $deptTree, $childIds);
        }
    }

    /**
     * 自动构建时间字段
     * @param array $params
     * @param array $build
     * @return array
     */
    public function addTime(array $params, array $build = ['update_time']): array
    {
        $now = date('Y-m-d H:i:s');
        foreach ($build as $v) {
            $params[$v] = $now;
        }
        return $params;
    }

    /**
     * 处理JSON字段编码
     * @param array $data 待处理的数据
     * @return array
     */
    /**
     * 处理 JSON 字段编码
     * @param array $data
     * @return array
     */
    protected function handleJsonEncode(array $data): array
    {
        foreach ($this->jsonFields as $field) {
            if (isset($data[$field]) && (is_array($data[$field]) || is_object($data[$field]))) {
                $data[$field] = json_encode($data[$field], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }
        }
        return $data;
    }

    /**
     * 处理 JSON 字段解码
     * @param array $data
     * @return array
     */
    protected function handleJsonDecode(array $data): array
    {
        foreach ($this->jsonFields as $field) {
            if (isset($data[$field]) && is_string($data[$field])) {
                $decoded = json_decode($data[$field], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $data[$field] = $decoded;
                }
            }
        }
        return $data;
    }

    /**
     * 启用数据权限过滤
     * @param bool $enable 是否启用
     * @return $this
     */
    public function enableDataScope(bool $enable = true)
    {
        $this->dataScope = $enable;
        return $this;
    }

    /**
     * 设置用户ID字段名
     * @param string $field 字段名
     * @return $this
     */
    public function setUserIdField(string $field)
    {
        $this->userIdField = $field;
        return $this;
    }

    /**
     * 设置部门ID字段名
     * @param string $field 字段名
     * @return $this
     */
    public function setDeptIdField(string $field)
    {
        $this->deptIdField = $field;
        return $this;
    }

    /**
     * 分页查询
     * @param array $params
     * @param array $searchKey
     * @return array
     */
    public function listPage(array $params, array $searchKey = []): array
    {
        $where = $this->buildWhere($params, [], $searchKey);
        // $where[] = ['delete_time', '=', null];
        $page = (int)$params['page_no'] ?? 1;
        $limit = (int)$params['page_size'] ?? 10;
        $order = $params['order'] ?? [$this->model->getPk()=>'desc'];
        $query = $this->model->where($where);
        if ($order) {
            if (is_array($order)) {
                foreach ($order as $field => $dir) {
                    $query = $query->order($field, $dir);
                }
            } else {
                $query = $query->order($order);
            }
        }
        $records = $query->page($page, $limit)->select()->toArray();
        // 处理 JSON 字段
        $records = array_map(function($item) {
            return $this->handleJsonDecode($item);
        }, $records);
        $total = $this->model->where($where)->count();
        return [
            'records' => $records,
            'total' => $total,
            'current' => $page,
            'size' => $limit,
            'pages' => (int)ceil($total / $limit)
        ];
    }

    /**
     * 根据主键获取单条数据
     * @param array $params
     * @return array|null
     */
    public function getById(array $params)
    {
        $idKey = $this->model->getPk();
        if (empty($params[$idKey])) return null;
        $where = [[$idKey, '=', $params[$idKey]]];
        $res = $this->model->where($where)->find();
        if (!$res) return null;
        return $this->handleJsonDecode($res->toArray());
    }
    
    /**
     * 检查主键是否存在
     * @param array $params
     * @return bool
     */
    public function checkPrimaryKey(array $params): bool
    {
        $idKey = $this->model->getPk();
        return !empty($params[$idKey]);
    }
    
    /**
     * 获取主键名称
     * @return string
     */
    public function getPrimaryKeyName(): string
    {
        return $this->model->getPk();
    }

    /**
     * 根据主键删除（软/硬删除）
     * @param array $params
     * @param bool $force
     * @return int
     */
    public function deleteById(array $params, bool $force = false): int
    {
        $idKey = $this->model->getPk();
        if (empty($params[$idKey])) return 0;
        $where = [[$idKey, '=', $params[$idKey]]];
        if ($force) {
            return $this->model->where($where)->delete();
        } else {
            $data = [
                'delete_time' => date('Y-m-d H:i:s'),
                'status' => 0
            ];
            return $this->model->where($where)->update($data);
        }
    }

    /**
     * 新增数据
     * @param array $params
     * @return int|string 新增主键
     */
    public function add(array $params)
    {
        $idKey = $this->model->getPk();
        unset($params[$idKey]);
        $params = $this->addTime($params, ['create_time', 'update_time']);
        $params = $this->handleJsonEncode($params);
        return $this->model->strict(false)->insertGetId($params);
    }

    /**
     * 更新数据
     * @param array $params
     * @return int 影响行数
     */
    public function update(array $params): int
    {
        $idKey = $this->model->getPk();
        if (empty($params[$idKey])) return 0;
        $where = [[$idKey, '=', $params[$idKey]]];
        $params = $this->addTime($params);
        $params = $this->handleJsonEncode($params);
        return $this->model->where($where)->strict(false)->save($params);
    }

    /**
     * 更新状态字段
     * @param array $params
     * @return int
     */
    public function updateStatus(array $params): int
    {
        $idKey = $this->model->getPk();
        if (empty($params[$idKey]) || !isset($params['status'])) return 0;
        $where = [[$idKey, '=', $params[$idKey]]];
        $data = ['status' => $params['status']];
        return $this->model->where($where)->update($data);
    }

    /**
     * 批量删除（软/硬删除）
     * @param array $params
     * @param bool $force
     * @return int
     */
    public function batchDelete(array $params, bool $force = false): int
    {
        $idKey = $this->model->getPk();
        if (empty($params['ids']) || !is_array($params['ids'])) return 0;
        $where = [[$idKey, 'IN', $params['ids']]];
        if ($force) {
            return $this->model->where($where)->delete();
        } else {
            $data = [
                'delete_time' => date('Y-m-d H:i:s'),
                'status' => 0
            ];
            return $this->model->where($where)->update($data);
        }
    }

    /**
     * 获取排序字段最大值+1
     * @param array $params
     * @return int
     */
    public function getSorted(array $params = []): int
    {
        $res = $this->model->field('sorted')->order('sorted', 'desc')->find();
        if ($res && isset($res['sorted'])) {
            return (int)$res['sorted'] + 1;
        }
        return 1;
    }
}