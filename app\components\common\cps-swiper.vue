<template>
	<view>
		<swiper class="sx-swiper" autoplay="true" :indicator-dots="dots" indicator-color="rgba(255, 255, 255, 1)"
			indicator-active-color="#ff552e" interval="5000" :style="{'height':height+'rpx'}">
			<swiper-item v-for="(item,index) in imgList" :key="index">
				<image class="sw-swiper-img" :src="item.img" @click="clickImg(item)"></image>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default {
		props: {
			imgList: {
				type: [Array, Object],
				default: function() {
					return [{ img: 'http://placehold.it/750x250' }];
				}
			},
			height: {
				type: [String, Number],
				default: 360
			},
			dots: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {}
		},
		methods: {
			clickImg(data) {
				if (!data.type) {
					return false;
				}
				if (data.type === 'link') {
					// #ifdef APP-PLUS
					plus.runtime.openURL(data.url);
					// #endif
					// #ifdef H5
					// window.location.href = data.url;
					window.open(data.url);
					// #endif
				} else if (data.type == 'mp-weixin') {
					// #ifdef MP-WEIXIN
					if (data.weappId && data.weappUrl) {
						wx.navigateToMiniProgram({
							appId: data.weappId,
							path: data.weappUrl,
							envVersion: 'release',
							success(res) {
								console.log('成功' + res);
							},
							fail(res) {
								console.log('失败' + res);
							}
						})
					}
					// #endif
				} else {
					uni[data.type]({
						url: data.url
					})
				}
			},
			showPhone(){
				uni.showModal({
					title: '提示',
					content: '有需要请联系客服',
					confirmText: '确定',
					success: (res) => {
						// if (res.confirm) {
						// 	uni.makePhoneCall({
						// 		phoneNumber: '05985833548'
						// 	})
						// } else if (res.cancel) {
						// 	uni.showToast({
						// 		title:'货比三家更实惠',
						// 		icon:'none'
						// 	})
						// }
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.sx-swiper {
		width: 100%;

		// height: 420rpx;
		.sw-swiper-img {
			width: 100%;
			// height: 420rpx;
			height: 100%;
		}
	}

	// .swiper swiper-item {
	// 	border-radius: 0rpx;
	// }
</style>
