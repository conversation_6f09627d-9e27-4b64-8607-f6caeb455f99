<?php
declare(strict_types=1);

namespace app\lib\service\storage;

use app\lib\exception\dogadmin\FileException;
use think\File;
use app\lib\service\storage\CloudStorageService;

/**
 * 七牛云存储服务
 */
class QiniuStorageService extends CloudStorageService
{
    /**
     * 七牛云客户端
     * @var mixed
     */
    private $client;
    
    /**
     * 上传管理器
     * @var mixed
     */
    private $uploadManager;
    
    /**
     * 上传凭证
     * @var string
     */
    private string $uploadToken = '';
    
    /**
     * 存储空间名
     * @var string
     */
    private string $bucket = '';
    
    /**
     * 域名
     * @var string
     */
    private string $domain = '';
    
    /**
     * 初始化七牛云配置
     */
    protected function init(): void
    {
        $this->storageType = 'qiniu';
        
        if (empty($this->config['accessKey']) || empty($this->config['secretKey']) || empty($this->config['bucket'])) {
            throw new FileException(['message' => '七牛云配置信息不完整'], 4); // 4 = 配置错误
        }
        
        $this->bucket = $this->config['bucket'];
        $this->domain = $this->config['domain'] ?? '';
        
        // 检查是否安装了七牛云SDK
        if (!class_exists('\\Qiniu\\Auth')) {
            throw new FileException(['message' => '请先安装七牛云SDK: composer require qiniu/php-sdk'], 4); // 4 = 配置错误
        }
        
        try {
            // 初始化Auth状态
            $auth = new \Qiniu\Auth($this->config['accessKey'], $this->config['secretKey']);
            
            // 生成上传凭证
            $this->uploadToken = $auth->uploadToken($this->bucket);
            
            // 初始化UploadManager
            $this->uploadManager = new \Qiniu\Storage\UploadManager();
            
        } catch (\Exception $e) {
            throw new FileException(['message' => '七牛云初始化失败: ' . $e->getMessage()], 4); // 4 = 配置错误
        }
    }
    
    /**
     * 上传文件到七牛云
     * 
     * @param File|string $file 文件对象或本地路径
     * @param string $path 上传路径
     * @param string $fileName 文件名
     * @param string|bool $nameMode 文件名模式：
     *                             - 字符串: 使用自定义文件名（保留扩展名）
     *                             - true: 使用原始文件名
     *                             - false: 生成唯一文件名（默认）
     * @return array
     */
    public function upload($file, string $path = '', string $fileName = '', $nameMode = false): array
    {
        try {
            $info = $this->parseFileInfo($file);
            $this->validateFile($file, $this->config['rules'] ?? []);
            if (empty($fileName)) {
                $fileName = $this->generateFileName($info['originalName'], $path, $nameMode);
            } else {
                $fileName = $path . $fileName;
            }
            list($ret, $err) = $this->uploadManager->putFile(
                $this->uploadToken,
                $fileName,
                $info['realPath']
            );
            if ($err !== null) {
                throw new FileException([
                    'message' => '七牛云上传失败: ' . $err->message(),
                    'file' => $info['originalName']
                ], 5);
            }
            return [
                'success' => true,
                'key' => $ret['key'],
                'hash' => $ret['hash'],
                'url' => $this->getUrl($ret['key']),
                'size' => $info['size'],
                'originalName' => $info['originalName'],
                'extension' => $info['extension'],
                'mimeType' => $info['mime'],
                'storageType' => $this->storageType
            ];
        } catch (FileException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '七牛云上传异常: ' . $e->getMessage(),
                'file' => $info['originalName'] ?? ''
            ], 5);
        }
    }
    
    /**
     * 删除文件
     * 
     * @param string $key 文件key
     * @return bool
     */
    public function delete(string $key): bool
    {
        try {
            $auth = new \Qiniu\Auth($this->config['accessKey'], $this->config['secretKey']);
            $bucketManager = new \Qiniu\Storage\BucketManager($auth);
            
            $err = $bucketManager->delete($this->bucket, $key);
            
            if ($err !== null) {
                throw new FileException([
                    'message' => '七牛云删除失败: ' . $err->message(),
                    'key' => $key
                ], 8); // 8 = 文件删除失败
            }
            
            return true;
            
        } catch (FileException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '七牛云删除异常: ' . $e->getMessage(),
                'key' => $key
            ], 8); // 8 = 文件删除失败
        }
    }
    
    /**
     * 获取文件URL
     * 
     * @param string $key 文件key
     * @return string
     */
    public function getUrl(string $key): string
    {
        if (empty($this->domain)) {
            return $key;
        }
        
        return rtrim($this->domain, '/') . '/' . ltrim($key, '/');
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param string $key 文件key
     * @return bool
     */
    public function exists(string $key): bool
    {
        try {
            $auth = new \Qiniu\Auth($this->config['accessKey'], $this->config['secretKey']);
            $bucketManager = new \Qiniu\Storage\BucketManager($auth);
            
            list($ret, $err) = $bucketManager->stat($this->bucket, $key);
            
            return $err === null;
            
        } catch (\Exception $e) {
            return false;
        }
    }

    // validateFile方法兼容本地路径
    protected function validateFile($file, array $rules = []): void
    {
        $info = $this->parseFileInfo($file);
        if (isset($rules['maxSize']) && $info['size'] > $rules['maxSize']) {
            throw new FileException([
                'file' => $info['originalName'],
                'size' => $info['size'],
                'maxSize' => $rules['maxSize']
            ], 1);
        }
        if (isset($rules['allowedTypes']) && !in_array($info['extension'], $rules['allowedTypes'])) {
            throw new FileException([
                'file' => $info['originalName'],
                'type' => $info['extension'],
                'allowedTypes' => $rules['allowedTypes']
            ], 3);
        }
    }
}