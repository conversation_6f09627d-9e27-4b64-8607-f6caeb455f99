<?php

namespace app\common_api\service;

class BaseService
{
    /**
     * @var \think\Model|null 当前服务的模型实例
     */
    protected $model = null;

    /**
     * 自动构建时间字段
     * @param array $params
     * @param array $build
     * @return array
     */
    public function addTime(array $params, array $build = ['update_time']): array
    {
        $now = date('Y-m-d H:i:s');
        foreach ($build as $v) {
            $params[$v] = $now;
        }
        return $params;
    }
}