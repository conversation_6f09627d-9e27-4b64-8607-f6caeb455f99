<?php

namespace app\common_api\middleware;

use app\common_api\lib\exception\ApiException;
use app\common_api\service\CommonUserService;
use app\Request;
use Closure;
use thans\jwt\facade\JWTAuth;

class ApiCheck
{
    public function handle(Request $request, Closure $next)
    {
        try {
            // 验证token
            $payload = JWTAuth::auth();
            $userId = $payload['user_id'];

            
            if (!$userId) {
                throw new ApiException([], 2); // token验证失败
            }

            // 检查用户是否存在
            $user = (new CommonUserService())->getUserInfoById($userId);
            if (!$user) {
                throw new ApiException([], 6); // 账号不存在
            }

            // 检查用户状态
            if ($user['status'] !== '1') {
                throw new ApiException([], 5); // 账号已被禁用
            }


            // 将用户ID注入到请求对象中
            $request->user_id = $userId;
            
            return $next($request);
            
        } catch (\Exception $e) {
            throw new ApiException([],20001); // 其他token验证失败
//            return json(['error_code' => 20001, 'msg' => $e->getMessage(), 'data' => []]);
        }
    }
}