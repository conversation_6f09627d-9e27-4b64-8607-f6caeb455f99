<?php
namespace app\dogadmin\controller;

use app\dogadmin\common\ResponseCode;
use app\dogadmin\service\SysFileService;
use app\dogadmin\common\ApiResponse;
use app\lib\exception\dogadmin\FileException;
use think\Request;
use think\Response;
use think\facade\Config;

class SysFile extends Base
{
    /**
     * @var SysFileService
     */
    protected $service;
    
    public function initialize(): void
    {
        $this->service = new SysFileService();
        $this->params = $this->request->param();
    }
    
    /**
     * 上传文件到云存储
     * 
     * @param Request $request
     * @return Response
     */
    public function upload(Request $request): Response
    {
        try {
            // 支持本地路径上传
            $localPath = $request->param('local_path', '');
            if (!empty($localPath) && is_file($localPath)) {
                $file = $localPath;
            } else {
                $file = $request->file('file');
                if (!$file) {
                    throw new FileException(['message' => '未找到上传文件'], 6); // 6 = 文件不存在
                }
            }
            
            // 获取参数
            $storageType = $request->param('storage_type', '');
            $category = $request->param('category', 'image');
            $fileName = $request->param('file_name', '');
            
//            // 获取文件名模式
//            // 1. 检查是否有自定义文件名模式参数
//            $nameMode = $request->param('name_mode', '');
//            // 2. 兼容旧版本的use_original_name参数
//            if (empty($nameMode) && $request->has('use_original_name')) {
//                $useOriginalName = $request->param('use_original_name/b', false);
//                $nameMode = $useOriginalName ? true : false;
//            }
            
            // 验证存储类型
            $supportedTypes = ['qiniu', 'tencent_cos', 'aliyun_oss'];
            if (!empty($storageType) && !in_array($storageType, $supportedTypes)) {
                throw new FileException([
                    'message' => '不支持的存储类型: ' . $storageType,
                    'supported_types' => $supportedTypes
                ], 3); // 3 = 文件类型不允许
            }
            
            // 验证文件分类
            $supportedCategories = ['image', 'document', 'media', 'archive'];
            if (!in_array($category, $supportedCategories)) {
                throw new FileException([
                    'message' => '不支持的文件分类: ' . $category,
                    'supported_categories' => $supportedCategories
                ], 3); // 3 = 文件类型不允许
            }
            
            // 上传文件
            $options = [];
            if (!empty($fileName)) {
                $options['fileName'] = $fileName;
            }
            
            // 处理自定义配置
            $customConfig = $request->param('config/a', []);
            if (!empty($customConfig)) {
                $options['config'] = $customConfig;
                $options['merge_config'] = $request->param('merge_config/b', true);
            }
            
            // 自定义上传路径
            $uploadPath = $request->param('upload_path', '');
            if (!empty($uploadPath)) {
                $options['upload_path'] = $uploadPath;
            }
            
            // 文件名模式
            $nameMode = '';
            if (is_string($file)) {
                $nameMode = basename($file);
            }
            
            $result = $this->service->uploadToCloud($file, $storageType, $category, $options, $nameMode);
            
            return ApiResponse::success($result, '文件上传成功');
            
        } catch (FileException $e) {
            return ApiResponse::error($e->getCode(), $e->getMsg(), $e->getData());
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, '文件上传失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 删除云存储文件
     * 
     * @param Request $request
     * @return Response
     */
    public function delete(Request $request): Response
    {
        try {
            $fileId = $request->param('file_id/d', 0);
            if (!$fileId) {
                throw new FileException(['message' => '文件ID不能为空'], 6); // 6 = 文件不存在
            }
            
            $result = $this->service->deleteCloudFile($fileId);
            
            return ApiResponse::success(['deleted' => $result], '文件删除成功');
            
        } catch (FileException $e) {
            return ApiResponse::error($e->getCode(), $e->getMsg(), $e->getData());
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, '文件删除失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取文件下载链接
     * 
     * @param Request $request
     * @return Response
     */
    public function getDownloadUrl(Request $request): Response
    {
        try {
            $fileId = $request->param('file_id/d', 0);
            if (!$fileId) {
                throw new FileException(['message' => '文件ID不能为空'], 6); // 6 = 文件不存在
            }
            
            $expires = $request->param('expires/d', 3600);
            $url = $this->service->getDownloadUrl($fileId, $expires);
            
            return ApiResponse::success([
                'file_id' => $fileId,
                'download_url' => $url,
                'expires' => $expires
            ], '获取下载链接成功');
            
        } catch (FileException $e) {
            return ApiResponse::error($e->getCode(), $e->getMsg(), $e->getData());
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, '获取下载链接失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取支持的存储类型
     * 
     * @return Response
     */
    public function getSupportedTypes(): Response
    {
        $types = [
            'qiniu' => [
                'name' => '七牛云',
                'code' => 'qiniu',
                'description' => '七牛云对象存储'
            ],
            'tencent_cos' => [
                'name' => '腾讯云COS',
                'code' => 'tencent_cos',
                'description' => '腾讯云对象存储'
            ],
            'aliyun_oss' => [
                'name' => '阿里云OSS',
                'code' => 'aliyun_oss',
                'description' => '阿里云对象存储'
            ]
        ];
        
        $categories = [
            'image' => [
                'name' => '图片',
                'code' => 'image',
                'extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
            ],
            'document' => [
                'name' => '文档',
                'code' => 'document',
                'extensions' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
            ],
            'media' => [
                'name' => '媒体',
                'code' => 'media',
                'extensions' => ['mp3', 'wav', 'flac', 'aac', 'ogg', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
            ],
            'archive' => [
                'name' => '压缩包',
                'code' => 'archive',
                'extensions' => ['zip', 'rar', '7z', 'tar', 'gz']
            ]
        ];
        
        return ApiResponse::success([
            'storage_types' => $types,
            'file_categories' => $categories,
            'default_storage' => Config::get('cloud_storage.default', 'qiniu')
        ], '获取支持类型成功');
    }
    
    /**
     * 批量上传文件
     * 
     * @param Request $request
     * @return Response
     */
    public function batchUpload(Request $request): Response
    {
        try {
            // 支持本地路径批量上传
            $localPaths = $request->param('local_paths', []);
            if (!empty($localPaths) && is_array($localPaths)) {
                $files = $localPaths;
            } else {
                $files = $request->file('files');
                if (!$files || !is_array($files)) {
                    throw new FileException(['message' => '未找到上传文件'], 6); // 6 = 文件不存在
                }
            }
            $storageType = $request->param('storage_type', '');
            $category = $request->param('category', 'image');
            $nameMode = $request->param('name_mode', '');
            if (empty($nameMode) && $request->has('use_original_name')) {
                $useOriginalName = $request->param('use_original_name/b', false);
                $nameMode = $useOriginalName ? true : false;
            }
            $options = [];
            $customConfig = $request->param('config/a', []);
            if (!empty($customConfig)) {
                $options['config'] = $customConfig;
                $options['merge_config'] = $request->param('merge_config/b', true);
            }
            $uploadPath = $request->param('upload_path', '');
            if (!empty($uploadPath)) {
                $options['upload_path'] = $uploadPath;
            }
            $results = [];
            $errors = [];
            foreach ($files as $index => $file) {
                try {
                    // 文件名模式自动适配
                    $curNameMode = $nameMode;
                    if (empty($curNameMode) && is_string($file)) {
                        $curNameMode = basename($file);
                    }
                    $result = $this->service->uploadToCloud($file, $storageType, $category, $options, $curNameMode);
                    $results[] = $result;
                } catch (\Exception $e) {
                    $fileName = is_string($file) ? basename($file) : '';
                    $errors[] = [
                        'index' => $index,
                        'file_name' => $fileName,
                        'error' => $e->getMessage()
                    ];
                }
            }
            return ApiResponse::success([
                'success_count' => count($results),
                'error_count' => count($errors),
                'results' => $results,
                'errors' => $errors
            ], '批量上传完成');
        } catch (FileException $e) {
            return ApiResponse::error($e->getCode(), $e->getMsg(), $e->getData());
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, '批量上传失败: ' . $e->getMessage());
        }
    }
}