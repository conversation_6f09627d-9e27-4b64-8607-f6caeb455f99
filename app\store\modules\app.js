import { getQueryVariable, setTabbarNum } from '@/utils/common.js'
// import options from '@/config/options.js'
import { getUserInit } from '@/api/user.js'
import { resolve } from 'path'
import { loginWeapp } from '@/api/login.js'
const state = {
	showPay: 0,
	isLogin: true,
	userInfo: {},
	exchangeCanReset: false,
	exchangeCommentNum: 0
}
const mutations = {
	SET_USER_INFO(state, info) {
		state.userInfo = info;
	},
	SET_IS_LOGIN(state, data) {
		state.isLogin = data;
	},
	SET_SHOW_PAY(state, info) {
		state.showPay = info;
	},
	SET_EXCHANGE_CANRESET(state, info) {
		state.exchangeCanReset = info;
	},
	SET_EXCHANGE_COMMENT_NUM(state, info) {
		state.exchangeCommentNum = info;
		if (info > 0) {
			setTabbarNum(3, info);
		} else {
			setTabbarNum(3, 0);
		}
	},
}
const actions = {
	weappAutoLogin({ commit, state }) {
		return new Promise((resolve, reject) => {
			uni.login({
				onlyAuthorize: true,
				success: (loginRes) => {
					// return false;
					// console.log('loginRes', loginRes);
					if (loginRes.code) {
						loginWeapp(loginRes).then(res => {
							// console.log('loginWeapp', res);
							if (res.code == 1) {
								uni.setStorageSync('token', res.data.token);
								uni.setStorageSync('tokenExpiration', res.data.exptime);
								resolve(res)
							} else {
								reject(res)
							}
						})
					}
				}
			})
		})
	},
	// 设置name
	getUserInfo({ commit, state }) {
		return new Promise((resolve, reject) => {
			let token = uni.getStorageSync('token');
			if (!token) {
				reject('请登录后操作');
			} else {
				getUserInit().then(res => {
					// console.log('getUserInit', res);
					if (res.code == 1) {
						// #ifdef MP-WEIXIN
						uni.setStorageSync('mp-openid', res.data.openid_mp);
						// #endif
						commit('SET_USER_INFO', res.data);
						commit('SET_IS_LOGIN', true);
						resolve(res);
					} else {
						commit('SET_USER_INFO', '');
						commit('SET_IS_LOGIN', false);
						reject(res)
					}
				}).catch(err => {
					commit('SET_USER_INFO', '');
					commit('SET_IS_LOGIN', false);
					reject(err)
				});
			}
		});
	},
	getShowPay({ commit, state }) {
		return new Promise((resolve, reject) => {
			getVipPayOr().then(res => {
				// console.log('getVipInfo', res);
				if (res.code == 1) {
					commit('SET_SHOW_PAY', res.data);
					resolve(res);
				} else {
					commit('SET_SHOW_PAY', 0);
					reject(res)
				}
			}).catch(err => {
				commit('SET_VIP_INFO', 0);
				reject(err)
			});
		});
	},

}
export default {
	state,
	mutations,
	actions
}