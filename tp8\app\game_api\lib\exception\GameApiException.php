<?php
declare(strict_types=1);

namespace app\game_api\lib\exception;

use app\lib\exception\BaseException;
use Throwable;

/**
 * 认证异常类
 * 处理认证相关的异常，如登录失败、权限不足等
 */
class GameApiException extends BaseException
{
    /**
     * 错误码映射
     * @var array
     */
    protected static array $errorCodes = [
        1 => '出错了请重试',
    ];

    /**
     * 构造函数
     * 
     * @param $data 附加数据
     * @param int $code 错误码
     * @param string $message 自定义错误消息，为空时使用错误码映射的消息
     * @param Throwable|null $previous 上一个异常
     */
    public function __construct($data = [], int $code = 2, string $message = '', ?Throwable $previous = null)
    {
        if(!is_array($data)){
            $message = $data;
            $data = [];
        }
        parent::__construct($data, $code, $message, $previous);
    }
}