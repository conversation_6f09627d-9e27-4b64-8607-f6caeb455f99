<template>
	<view>
		<tui-actionsheet  
		  :show="topShow" 
		  :item-list="orderType.top" 
		  @click="selectClick" 
		  @cancel="close('top')">
		</tui-actionsheet>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex';
	import { orderInfo } from '@/api/order'
	export default {
		props: {
			topShow: {
				type: Boolean,
				default: false
			},
			infoId: {
				type: [String,Number],
				default: ''
			}
		},
		data() {
			return {
				title: '温馨提示',
				content: '是否花费18金币置顶1天？',
				order: ''
			}
		},
		methods: {
			close(type) {
				this.$emit('close', { type })
			},
			setOrder(type){
				let order = this.orderType[type][0];
				this.content = `是否花费${order.money}金币${order.name}该信息？`;
				this.order = order;
				this.showModal();
			},
			selectClick(order) {
				this.close('top')
				// let order = uni.$u.deepClone(val);
				this.content = `是否花费${order.money}金币${order.text}？`;
				this.order = order;
				this.showModal();
			},
			showModal() {
				uni.showModal({
					title: this.title,
					content: this.content,
					success: (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							this._orderInfo();
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			showNoMoney(){
				uni.showModal({
					title: '温馨提示',
					content: '金币不足，请您充值。充值完成后，记得回来继续操作',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/pay/toPay?money=' + this.order.money
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			_orderInfo() {
				let data = {
					info_id: this.infoId,
					order_type_id: this.order.id
				}
				orderInfo(data).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: '操作成功',
							icon: 'none'
						});
						this.resetData();
					} else {
						if (res.error_code == 20004) {
							this.showNoMoney();
						}else{
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					}
				})
			},
			resetData(){
				this.$emit('resetData')
			}
		},
		computed: {
			...mapGetters(['orderType'])
		},
	}
</script>

<style scoped>

</style>
