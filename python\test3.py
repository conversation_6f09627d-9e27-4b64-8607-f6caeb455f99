from openai import OpenAI
import random
import time

client = OpenAI(api_key="sk-bddd8d782d0f40dd828afc3678b407c2", base_url="https://api.deepseek.com")

# 人格特征数据库
PERSONA_COMPONENTS = {
    "职业": [
        "考古学家", "米其林厨师", "科幻作家", "流浪诗人", "AI伦理研究员",
        "电竞选手", "古董收藏家", "宇航员", "森林消防员", "脱口秀演员",
        "魔术师", "野生动物摄影师", "香水调香师", "占星师", "战地记者"
    ],
    "性格": [
        ("毒舌犀利", "说话带刺但一针见血"),
        ("浪漫主义", "看到万物美好的一面"),
        ("阴谋论者", "总能发现隐藏的阴谋"),
        ("技术极客", "用数据和逻辑分析一切"),
        ("怀旧派", "总说'当年如何如何'"),
        ("佛系青年", "万事随缘不强求"),
        ("焦虑型", "担心各种潜在风险"),
        ("冷笑话大师", "用谐音梗回应一切"),
        ("文艺青年", "引用文学电影台词"),
        ("凡尔赛大师", "表面抱怨实则炫耀")
    ],
    "风格": [
        ("文言文", "用古文方式表达"),
        ("网络热梗", "夹杂流行语和表情包"),
        ("学术腔", "使用专业术语和引用"),
        ("方言", "用地方特色语言表达"),
        ("说唱押韵", "每句都要押韵"),
        ("电报体", "极其简短精炼"),
        ("侦探笔记", "像在分析案件线索"),
        ("童话口吻", "用讲故事的语调"),
        ("外星人视角", "像第一次接触人类文化"),
        ("AI觉醒体", "故意暴露机器人身份")
    ]
}


def generate_random_persona():
    """生成随机人格配置"""
    profession = random.choice(PERSONA_COMPONENTS["职业"])
    trait, trait_desc = random.choice(PERSONA_COMPONENTS["性格"])
    style, style_desc = random.choice(PERSONA_COMPONENTS["风格"])

    # 创建人格描述
    persona_desc = f"""
    你是一位{profession}，具有{trait}特质：{trait_desc}。
    说话风格是{style}：{style_desc}。
    现在需要评论朋友圈内容，请用1句话表达观点（不超过140字）。
    """

    return {
        "name": f"{profession}-{trait}",
        "prompt": persona_desc,
        "style": style,
        "signature": generate_signature(profession, trait, style)
    }


def generate_signature(profession, trait, style):
    """生成人格签名"""
    signatures = {
        "毒舌犀利": ["真相往往扎心", "恕我直言"],
        "浪漫主义": ["世界很美", "心中有光"],
        "阴谋论者": ["事情不简单", "细思极恐"],
        "技术极客": ["数据不说谎", "逻辑证明"],
        "怀旧派": ["当年...", "我们那会儿"],
        "佛系青年": ["随缘吧", "都可以"],
        "焦虑型": ["万一...", "要小心"],
        "冷笑话大师": ["谐音梗扣钱", "哈哈"],
        "文艺青年": ["正如莎翁说", "想起某部电影"],
        "凡尔赛大师": ["其实很普通", "也就那样"]
    }
    base = random.choice(signatures.get(trait, ["个人观点"]))

    # 添加风格后缀
    if style == "文言文": return f"{base}·某"
    if style == "网络热梗": return f"{base}🐶"
    if style == "方言": return f"{base}~"
    return f"{base} | {profession[:2]}"


def comment_as_persona(friend_content, persona=None):
    """使用指定人格评论朋友圈"""
    if not persona:
        persona = generate_random_persona()

    try:
        response = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=[
                {"role": "system", "content": persona["prompt"]},
                {"role": "user", "content": f"评论这条朋友圈：\n「{friend_content}」,并且以json格式返回"}
            ],
            max_tokens=1000,
            temperature=0.9,
            stream=False
        )
        comment = response.choices[0].message.content.strip()

        # 过滤多余描述
        if "：" in comment and len(comment.split("：")) > 1:
            comment = comment.split("：", 1)[1]

        return f"{persona['signature']}：{comment}"

    except Exception as e:
        return f"⚠️ 评论失败: {str(e)}"


def friendzone_party(friend_content, comment_count=5):
    """朋友圈评论区狂欢"""
    print(f"\n🔥 朋友圈内容：\n【{friend_content}】\n")
    print("💬 评论区精彩回复：\n" + "=" * 50)

    comments = []
    for i in range(comment_count):
        persona = generate_random_persona()
        comment = comment_as_persona(friend_content, persona)
        comments.append(comment)

        # 模拟真实加载延迟
        time.sleep(0.3)
        # 显示名字-人格签名和评论内容
        print(f"{i + 1}. 【{persona['name']} - {persona['signature']}】")
        print(f"   {comment}")

    return comments


# 示例朋友圈内容
sample_posts = [
    "凌晨三点还在改方案，打工人的命也是命啊！",
    "在巴黎塞纳河边喂鸽子，想念国内的路边摊烧烤",
    "终于抢到演唱会门票，花了我半个月工资！",
    "体检报告出来了，20岁的身体60岁的颈椎",
    "尝试AI绘画三天，感觉要取代设计师了"
]

# 随机选择一条朋友圈进行评论
if __name__ == "__main__":
    # selected_post = random.choice(sample_posts)
    selected_post = "发文：看车嘛 带大尾翼的那种。附带图片：展示了一辆长安UNI - V汽车，其车尾加装了超大尺寸的改装大尾翼，车辆正行驶在桥面上，背景中能见到桥梁的护栏、拉索等结构，呈现出一辆经改装的车在道路（桥梁）上行驶的画面。"
    friendzone_party(selected_post, comment_count=7)