import { reactive, onMounted } from "vue";
import { listDataByType } from "@/api/system/dict/data/index.ts";

export function useKoiDict(dict_type: Array<string>) {
  let koiDicts: any = reactive({});
  onMounted(async () => {
    if (dict_type.length > 0) {
      for (const type of dict_type) {
        const res: any = await listDataByType(type);
        if (res.data != null) {
          koiDicts[type] = res.data;
        }
      }
    }
  });
  return { koiDicts };
}
