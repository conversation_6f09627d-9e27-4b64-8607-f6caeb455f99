<?php

namespace app\robot\controller;

use app\robot\service\RobotAiCharacterDictService;
use app\dogadmin\common\ApiResponse;
use app\dogadmin\controller\Base;

/**
 * AI角色通用字典表 控制器
 */
class RobotAiCharacterDict extends Base
{
    /**
     * @var RobotAiCharacterDictService
     */
    protected $service;

    /**
     * 初始化方法
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = new RobotAiCharacterDictService();
        $this->params = $this->request->param();
        $this->searchKey = [
            ['id' => 'like'],
            ['dict_type' => 'like'],
            ['dict_name' => 'like'],
        ];
    }

    /**
     * 获取所有启用状态的字典数据
     * @return \think\Response
     */
    public function getAllActiveDict()
    {
        try {
            $data = $this->service->getAllActiveDict();
            return ApiResponse::success($data, '获取字典数据成功');
        } catch (\Exception $e) {
            return ApiResponse::error('获取字典数据失败：' . $e->getMessage());
        }
    }

    /**
     * 根据字典类型获取启用状态的字典数据
     * @return \think\Response
     */
    public function getActiveDictByType()
    {
        $dictType = $this->request->param('dict_type', '');
        if (empty($dictType)) {
            return ApiResponse::error('字典类型不能为空');
        }

        try {
            $data = $this->service->getActiveDictByType($dictType);
            return ApiResponse::success($data, '获取字典数据成功');
        } catch (\Exception $e) {
            return ApiResponse::error('获取字典数据失败：' . $e->getMessage());
        }
    }

    // 所有基础CRUD方法均继承自Base控制器
     // 如需自定义方法，请在此处添加
}