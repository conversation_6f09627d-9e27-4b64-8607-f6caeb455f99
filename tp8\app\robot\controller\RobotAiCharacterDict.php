<?php

namespace app\robot\controller;

use app\robot\service\RobotAiCharacterDictService;
use app\dogadmin\common\ApiResponse;
use app\dogadmin\controller\Base;

/**
 * AI角色通用字典表 控制器
 */
class RobotAiCharacterDict extends Base
{
    /**
     * @var RobotAiCharacterDictService
     */
    protected $service;
    
    /**
     * 初始化方法
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = new RobotAiCharacterDictService();
        $this->params = $this->request->param();
        $this->searchKey = [
            ['id' => 'like'],
            ['dict_type' => 'like'],
            ['dict_name' => 'like'],
        ];
    }
    
    // 所有基础CRUD方法均继承自Base控制器
     // 如需自定义方法，请在此处添加
}