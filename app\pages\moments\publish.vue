<template>
  <view class="publish-page">
    <PublishMoment 
      :autoFocus="publishType === 'text'"
      @publish="handlePublish"
      @cancel="handleCancel"
    />
  </view>
</template>

<script>
import PublishMoment from '@/components/moments/PublishMoment.vue'

export default {
  components: {
    PublishMoment
  },
  data() {
    return {
      publishType: 'text'
    }
  },
  onLoad(options) {
    if (options.type) {
      this.publishType = options.type
    }
    
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '发表动态'
    })
  },
  methods: {
    handlePublish(momentData) {
      // 这里可以调用API发布动态
      console.log('发布动态:', momentData)
      
      uni.showToast({
        title: '发布成功',
        icon: 'success'
      })
      
      // 返回朋友圈页面
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    },
    
    handleCancel() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.publish-page {
  background-color: #F7F7F9;
  min-height: 100vh;
}
</style>
