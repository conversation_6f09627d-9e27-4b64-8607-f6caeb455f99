{"name": "koi-ui", "private": true, "version": "1.0.0", "type": "module", "author": {"name": "YU", "email": "<EMAIL>", "url": "https://gitee.com/BigCatHome/koi-ui"}, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:test": "vue-tsc && vite build --mode test", "build:prod": "vue-tsc && vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "fix": "eslint src --fix", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "release": "standard-version", "commit": "git add -A && czg && git push", "commitlint": "commitlint --config commitlint.config.cjs -e -V"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^11.2.0", "aieditor": "^1.3.3", "animate.css": "^4.1.1", "axios": "^1.7.7", "crypto-js": "^4.2.0", "default-passive-events": "^2.0.0", "driver.js": "^1.3.1", "echarts": "^5.5.1", "element-plus": "^2.8.7", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "md-editor-v3": "^4.21.3", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.2.6", "pinia-plugin-persistedstate": "^4.1.2", "pinyin-pro": "^3.26.0", "sortablejs": "^1.15.3", "vue": "^3.5.12", "vue-router": "^4.4.5", "vue3-count-to": "^1.1.2"}, "devDependencies": {"@babel/eslint-parser": "^7.25.9", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@types/crypto-js": "^4.2.2", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "@unocss/reset": "^0.64.0", "@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.20", "cz-git": "^1.10.1", "czg": "^1.10.1", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.30.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "postcss": "^8.4.47", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "sass": "^1.80.6", "sass-loader": "^16.0.3", "sharp": "^0.33.5", "stylelint": "^16.10.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.4", "stylelint-scss": "^6.8.1", "svgo": "^3.3.2", "typescript": "^5.6.3", "unocss": "^0.64.0", "vite": "^5.4.10", "vite-plugin-compression": "^0.5.1", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-mock": "^3.0.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-i18n": "^10.0.4", "vue-tsc": "^2.1.10"}, "commitizen": {"path": "node_modules/cz-git"}}