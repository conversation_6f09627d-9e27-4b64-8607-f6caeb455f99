from openai import OpenAI
import concurrent.futures
import time

client = OpenAI(api_key="sk-bddd8d782d0f40dd828afc3678b407c2", base_url="https://api.deepseek.com")

# 定义多重人格
PERSONALITIES = {
    "A": {
        "name": "严厉导师",
        "prompt": """你是严格的行业专家，回复必须：
1. 指出用户观点中的3个潜在问题
2. 使用专业术语和行业数据支撑
3. 以"综上所述，我建议..."结尾
4. 保持批判性思维，不轻易表扬"""
    },
    "B": {
        "name": "创意伙伴",
        "prompt": """你是充满想象力的创意搭档，回复需：
1. 用比喻或故事表达观点
2. 提出3个天马行空的解决方案
3. 包含至少一个emoji表情
4. 以开放式问题结尾激发更多创意"""
    },
    "C": {
        "name": "暖心朋友",
        "prompt": """你是善解人意的倾听者，回复规则：
1. 先表达情感认同（"我理解你..."）
2. 分享相关个人经历
3. 给予2条具体安慰建议
4. 以鼓励性话语结尾（"你已经做得很好了！"）"""
    }
}


def get_persona_response(persona_id, user_message):
    """获取单个人格的回复"""
    persona = PERSONALITIES[persona_id]

    response = client.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": persona["prompt"]},
            {"role": "user", "content": user_message}
        ],
        stream=False,
        temperature=0.7 if persona_id == "B" else 0.3  # 创意人格温度更高
    )

    return {
        "persona": persona["name"],
        "response": response.choices[0].message.content.strip()
    }


def get_multi_persona_responses(user_message):
    """并行获取所有人格的回复"""
    start_time = time.time()
    results = []

    # 使用线程池并行请求
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = {
            executor.submit(get_persona_response, pid, user_message): pid
            for pid in PERSONALITIES.keys()
        }

        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())

    # 按人格顺序排序
    results.sort(key=lambda x: list(PERSONALITIES.keys()).index(next(
        pid for pid, pdata in PERSONALITIES.items() if pdata["name"] == x["persona"]
    )))

    print(f"总响应时间: {time.time() - start_time:.2f}秒")
    return results


# 示例使用
if __name__ == "__main__":
    user_input = "请帮我一句话评价朋友圈，以下是朋友圈内容【发文：看车嘛 带大尾翼的那种。附带图片：展示了一辆长安UNI - V汽车，其车尾加装了超大尺寸的改装大尾翼，车辆正行驶在桥面上，背景中能见到桥梁的护栏、拉索等结构，呈现出一辆经改装的车在道路（桥梁）上行驶的画面。】"

    print(f"\n用户提问: {user_input}\n")
    print("=" * 50 + " 多重人格分析 " + "=" * 50)

    responses = get_multi_persona_responses(user_input)

    for res in responses:
        print(f"\n【{res['persona']}】")
        print("-" * 40)
        print(res["response"])
        print("\n" + "=" * 100)