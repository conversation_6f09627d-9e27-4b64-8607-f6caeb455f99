<template>
	<view>
		登录成功，请等待自动跳转
	</view>
</template>

<script>
	import { wxLoginBack } from '@/api/login.js'
	import { mapActions } from 'vuex';
	import { getWxJssdkConfig } from "@/utils/wx.js";
	export default {
		onLoad(options) {
			if (options.code) {
				this.wxLoginBack(options.code);
			} else {
				uni.showToast({
					title: '登录失败请重试',
					icon: 'none'
				})
			}
		},
		methods: {
			...mapActions({
				getUserInfo: 'getUserInfo'
			}),
			wxLoginBack(code) {
				let data = {
					code: code
				}
				wxLoginBack(data).then(res => {
					console.log('wxLoginBack',res);
					if (res.code == 0) {
						uni.setStorageSync('token', res.data.token);
						uni.setStorageSync('wx-openid', res.data.openid);
						this.getUserInfo();
						getWxJssdkConfig();
						uni.reLaunch({
							url: uni.getStorageSync('beforePageFullPath') || '/pages/index/index'
						})
					} else {
						uni.showToast({
							title: '登录失败请重试',
							icon: 'none'
						})
						// uni.redirectTo({
						// 	url: '/pages/login/index'
						// })
					}

				})
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>
