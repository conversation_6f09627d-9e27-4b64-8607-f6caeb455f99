// #ifdef H5
const wxh5 = require('./jweixin-1.6.0.js')
// #endif

// import wxh5 from "./jweixin.js"
import {
	isWeiXinBrowser
} from "./common.js"
import wxapi from "@/api/wx.js"
import {wxLogin} from '@/api/login.js'

function oneKeyWxLogin() {
	console.log('oneKeyWxLogin');
	let data = {
		url: window.location.origin + '/pages/login/ok'
	}
	let debug = getQueryVariable('debug');
	let code = getQueryVariable("code");
	let state = getQueryVariable("state");
	if (!code && !state) {
		wxLogin(data).then(res => {
			console.log('wxLogin', res);
			if (debug) {

			} else {
				window.location.href = res.data;
			}
		}).catch(err => {})
	}
}

function getQueryVariable(variable) {
	let query = window.location.search.substring(1);
	let vars = query.split("&");
	for (let i = 0; i < vars.length; i++) {
		let pair = vars[i].split("=");
		if (pair[0] == variable) {
			return pair[1];
		}
	}
	return false;
}

function getWxUrl() {
	let openid = uni.getStorageSync('wx-openid');
	if (openid) {
		getWxJssdkConfig();
	} else {
		let code = getQueryVariable("code");
		let state = getQueryVariable("state");
		let debug = getQueryVariable('debug');
		if (!code && !state) {
			let data = {
				url: window.location.href
			}
			wxapi.getWxUrl(data).then(res => {
				console.log('res', res);
				if (debug) {

				} else {
					window.location.href = res.data;
				}
			})
		} else {
			getWxOpenid();
		}
	}

}

function getWxOpenid() {
	let code = getQueryVariable("code");
	let state = getQueryVariable("state");
	if (code && state) {
		let data = {
			code,
			state
		}
		wxapi.getWxOpenid(data).then(res => {
			uni.setStorageSync('wx-openid', res.data.openid);
		})
	}
}

function getWxJssdkConfig(params) {
	let isWx = isWeiXinBrowser();
	if (!isWx) {
		return '';
	}
	return new Promise((resolve, reject) => {
		let data = params ||  {
			url: window.location.href
		}
		let wxbug = getQueryVariable("wxbug") ? getQueryVariable("wxbug") :  false;
 		wxapi.getWxConfig(data).then(res => {
			console.log('res', res)
			let ret = res.data;
			wxh5.config({
				debug: wxbug, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
				appId: ret.appId, // 必填，公众号的唯一标识
				timestamp: ret.timestamp, // 必填，生成签名的时间戳
				nonceStr: ret.nonceStr, // 必填，生成签名的随机串
				signature: ret.signature, // 必填，签名
				jsApiList: ["chooseWXPay", "updateAppMessageShareData","updateTimelineShareData"], // 必填，需要使用的JS接口列表
				openTagList:['wx-open-launch-weapp','wx-open-launch-app']
			});
			resolve(res)
		}).catch(err => {
			reject(err)
		})
	})
}

function shareReady(data) {
	data = data || {
		title: '',
		imgUrl: '',
		desc: '',
		link: ''
	}
	let shareInfo = {
		title: data.title,
		imgUrl: data.imgUrl,
		desc: data.desc,
		link: data.link,
		// success: function (res) {
		//   uni.showToast({
		//   	'title':'感谢您的分享'
		//   })
		// }
	}
	
	let isWx = isWeiXinBrowser();
	if (!isWx) {
		return '';
	}
	
	let params = {
		url : shareInfo.link
	}
	getWxJssdkConfig(params).then(res=>{
		wxh5.ready(function() {
			wxh5.updateAppMessageShareData(shareInfo);
			wxh5.updateTimelineShareData(shareInfo);
		});
	})
	
}

function payReady(data) {
	let payInfo = {
		timestamp: data
			.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
		nonceStr: data.nonceStr, // 支付签名随机串，不长于 32 位
		package: data.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
		signType: data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
		paySign: data.paySign, // 支付签名
		success: function(res) {
			uni.showToast({
				'title': '支付成功'
			})
		}
	}
	wxh5.ready(function() {
		wxh5.chooseWXPay(payInfo);
	});
}

export {
	getQueryVariable,
	getWxUrl,
	getWxOpenid,
	getWxJssdkConfig,
	shareReady,
	payReady,
	oneKeyWxLogin
};