/**
 * 全局样式配置文件
 * 基于绿色为核心主色，黑色系为辅助色的配色方案
 * 传递青春、新潮的感觉，营造积极向上的产品氛围
 */

/* ==================== 基本颜色 ==================== */

/* 绿色系（核心主色） */
$primary-plus: #00B07B;      /* 深绿色，用于强化突出显示区域 */
$primary: #00C78B;           /* 主要绿色，品牌核心主色 */
$primary-minus: #D0F3E1;     /* 浅绿色，用于柔和过渡区域 */
$background: #F7F7F9;        /* 极浅绿色背景，营造清新氛围 */

/* 黑色系（基础辅助色） */
$secondary: #242424;         /* 深灰色，基础辅助区域 */
$secondary-plus: #111111;    /* 接近黑色，强调次级辅助区域 */
$secondary-minus: #B9B9B9;   /* 浅灰色，弱化补充性辅助区域 */

/* 基础色彩变量 */
$white: #FFFFFF;
$black: #000000;

/* ==================== 字体颜色 ==================== */

/* 黑底白字系列 */
$font-white-primary: rgba(255, 255, 255, 1.0);    /* 重要文本 */
$font-white-secondary: rgba(255, 255, 255, 0.8);  /* 基本文本 */
$font-white-tertiary: rgba(255, 255, 255, 0.5);   /* 次要文本 */
$font-white-quaternary: rgba(255, 255, 255, 0.3); /* 注释文本 */

/* 白底黑字系列 */
$font-black-primary: rgba(0, 0, 0, 1.0);    /* 重要文本 */
$font-black-secondary: rgba(0, 0, 0, 0.8);  /* 基本文本 */
$font-black-tertiary: rgba(0, 0, 0, 0.5);   /* 次要文本 */
$font-black-quaternary: rgba(0, 0, 0, 0.2); /* 注释文本 */

/* ==================== 功能颜色 ==================== */

/* 状态色彩 */
$state-beauty: #FF5477;      /* 颜值功能 - 亮粉色 */
$state-qa: #11D180;          /* 问答功能 - 亮绿色 */
$state-soul: #FFA800;        /* 灵魂功能 - 亮橙色 */
$state-potential: #3DA2FF;   /* 潜力功能 - 亮蓝色 */
$state-tip: #FF4249;         /* 提示功能 - 亮红色 */
$state-info: #FF586C;        /* 信息功能 - 粉红色 */

/* 快拍功能渐变色 */
$state-snap-start: #3EB9FF;  /* 快拍渐变起始色 - 蓝色 */
$state-snap-end: #FFAAB4;    /* 快拍渐变结束色 - 粉色 */

/* ==================== 扩展颜色 ==================== */

/* 成功、警告、错误色 */
$success: $state-qa;         /* 成功色使用问答绿 */
$warning: $state-soul;       /* 警告色使用灵魂橙 */
$error: $state-tip;          /* 错误色使用提示红 */
$info: $state-potential;     /* 信息色使用潜力蓝 */

/* 边框颜色 */
$border-light: rgba(0, 0, 0, 0.1);
$border-medium: rgba(0, 0, 0, 0.2);
$border-dark: rgba(0, 0, 0, 0.3);

/* 阴影颜色 */
$shadow-light: rgba(0, 0, 0, 0.05);
$shadow-medium: rgba(0, 0, 0, 0.1);
$shadow-dark: rgba(0, 0, 0, 0.15);

/* ==================== 渐变定义 ==================== */

/* 主色渐变 */
$gradient-primary: linear-gradient(135deg, $primary-plus 0%, $primary 100%);
$gradient-primary-soft: linear-gradient(135deg, $primary 0%, $primary-minus 100%);

/* 功能渐变 */
$gradient-snap: linear-gradient(135deg, $state-snap-start 0%, $state-snap-end 100%);
$gradient-beauty: linear-gradient(135deg, $state-beauty 0%, lighten($state-beauty, 10%) 100%);
$gradient-soul: linear-gradient(135deg, $state-soul 0%, lighten($state-soul, 10%) 100%);

/* 背景渐变 */
$gradient-background: linear-gradient(180deg, $background 0%, darken($background, 2%) 100%);

/* ==================== 尺寸变量 ==================== */

/* 圆角 */
$radius-small: 8rpx;
$radius-medium: 16rpx;
$radius-large: 24rpx;
$radius-xlarge: 32rpx;
$radius-round: 50%;

/* 间距 */
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 48rpx;

/* 字体大小 */
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-md: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-xxl: 40rpx;

/* ==================== 全局样式类 ==================== */

/* 主色相关类 */
.primary-color { color: $primary !important; }
.primary-bg { background-color: $primary !important; }
.primary-border { border-color: $primary !important; }

.primary-plus-color { color: $primary-plus !important; }
.primary-plus-bg { background-color: $primary-plus !important; }

.primary-minus-color { color: $primary-minus !important; }
.primary-minus-bg { background-color: $primary-minus !important; }

/* 辅助色相关类 */
.secondary-color { color: $secondary !important; }
.secondary-bg { background-color: $secondary !important; }

.secondary-plus-color { color: $secondary-plus !important; }
.secondary-plus-bg { background-color: $secondary-plus !important; }

.secondary-minus-color { color: $secondary-minus !important; }
.secondary-minus-bg { background-color: $secondary-minus !important; }

/* 字体颜色类 */
.font-primary { color: $font-black-primary !important; }
.font-secondary { color: $font-black-secondary !important; }
.font-tertiary { color: $font-black-tertiary !important; }
.font-quaternary { color: $font-black-quaternary !important; }

.font-white-primary { color: $font-white-primary !important; }
.font-white-secondary { color: $font-white-secondary !important; }
.font-white-tertiary { color: $font-white-tertiary !important; }
.font-white-quaternary { color: $font-white-quaternary !important; }

/* 功能色类 */
.beauty-color { color: $state-beauty !important; }
.beauty-bg { background-color: $state-beauty !important; }

.qa-color { color: $state-qa !important; }
.qa-bg { background-color: $state-qa !important; }

.soul-color { color: $state-soul !important; }
.soul-bg { background-color: $state-soul !important; }

.potential-color { color: $state-potential !important; }
.potential-bg { background-color: $state-potential !important; }

.tip-color { color: $state-tip !important; }
.tip-bg { background-color: $state-tip !important; }

.info-color { color: $state-info !important; }
.info-bg { background-color: $state-info !important; }

/* 渐变背景类 */
.gradient-primary { background: $gradient-primary !important; }
.gradient-primary-soft { background: $gradient-primary-soft !important; }
.gradient-snap { background: $gradient-snap !important; }
.gradient-beauty { background: $gradient-beauty !important; }
.gradient-soul { background: $gradient-soul !important; }
.gradient-background { background: $gradient-background !important; }

/* 圆角类 */
.radius-small { border-radius: $radius-small !important; }
.radius-medium { border-radius: $radius-medium !important; }
.radius-large { border-radius: $radius-large !important; }
.radius-xlarge { border-radius: $radius-xlarge !important; }
.radius-round { border-radius: $radius-round !important; }

/* 间距类 */
.margin-xs { margin: $spacing-xs !important; }
.margin-sm { margin: $spacing-sm !important; }
.margin-md { margin: $spacing-md !important; }
.margin-lg { margin: $spacing-lg !important; }
.margin-xl { margin: $spacing-xl !important; }

.padding-xs { padding: $spacing-xs !important; }
.padding-sm { padding: $spacing-sm !important; }
.padding-md { padding: $spacing-md !important; }
.padding-lg { padding: $spacing-lg !important; }
.padding-xl { padding: $spacing-xl !important; }

/* 字体大小类 */
.font-xs { font-size: $font-size-xs !important; }
.font-sm { font-size: $font-size-sm !important; }
.font-md { font-size: $font-size-md !important; }
.font-lg { font-size: $font-size-lg !important; }
.font-xl { font-size: $font-size-xl !important; }
.font-xxl { font-size: $font-size-xxl !important; }

/* 阴影类 */
.shadow-light { box-shadow: 0 2rpx 8rpx $shadow-light !important; }
.shadow-medium { box-shadow: 0 4rpx 16rpx $shadow-medium !important; }
.shadow-dark { box-shadow: 0 8rpx 24rpx $shadow-dark !important; }

/* ==================== 布局工具类 ==================== */

/* Flex布局 */
.flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

.flex-1 { flex: 1 !important; }
.flex-auto { flex: auto !important; }
.flex-none { flex: none !important; }

/* 文本对齐 */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

/* 文本样式 */
.text-bold { font-weight: bold !important; }
.text-normal { font-weight: normal !important; }
.text-light { font-weight: 300 !important; }

/* 溢出处理 */
.text-ellipsis {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.text-break {
  word-wrap: break-word !important;
  word-break: break-all !important;
}

/* 显示隐藏 */
.hidden { display: none !important; }
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* 定位 */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

/* 宽高 */
.w-full { width: 100% !important; }
.h-full { height: 100% !important; }
.w-auto { width: auto !important; }
.h-auto { height: auto !important; }

/* ==================== 动画效果 ==================== */

/* 过渡动画 */
.transition-all { transition: all 0.3s ease !important; }
.transition-color { transition: color 0.3s ease !important; }
.transition-bg { transition: background-color 0.3s ease !important; }
.transition-transform { transition: transform 0.3s ease !important; }

/* 悬停效果 */
.hover-scale:active { transform: scale(0.95) !important; }
.hover-opacity:active { opacity: 0.8 !important; }

/* ==================== 响应式断点 ==================== */

/* 小屏幕 */
@media screen and (max-width: 750rpx) {
  .sm-hidden { display: none !important; }
  .sm-block { display: block !important; }
  .sm-flex { display: flex !important; }
}

/* 大屏幕 */
@media screen and (min-width: 751rpx) {
  .lg-hidden { display: none !important; }
  .lg-block { display: block !important; }
  .lg-flex { display: flex !important; }
}