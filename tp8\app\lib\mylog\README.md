# 增强日志系统使用说明

基于 ThinkPHP 8 的增强日志系统，支持结构化数据记录和更丰富的上下文信息。

## 特性

- 支持结构化数据记录
- 自动添加时间戳和请求ID
- 自动处理数组和对象的序列化
- 支持复杂的嵌套数据结构
- 与 ThinkPHP Log 接口保持一致
- JSON 格式输出上下文数据
- 支持多通道日志记录

## 安装

该日志系统已集成在项目中，位于 `app/lib/mylog` 目录下。

## 基本用法

### 1. 引入命名空间

```php
use app\lib\mylog\MyLog;
```

### 2. 记录简单日志

```php
MyLog::info('简单的日志消息');
```

### 3. 记录带上下文的日志

```php
MyLog::info('用户注册成功', [
    'user_id' => 123,
    'username' => 'john_doe',
    'register_time' => date('Y-m-d H:i:s')
]);
```

### 4. 记录对象数据

```php
$user = User::find(1);
MyLog::info('用户信息', [
    'user' => $user,  // 会自动调用 toArray() 或转换对象属性
    'changes' => $changes
]);
```

### 5. 使用不同的日志通道

```php
// 使用email通道记录日志
MyLog::channel('email')->info('重要通知');

// 使用mongodb通道记录日志
MyLog::channel('mongo')->error('数据库错误', [
    'collection' => 'users',
    'operation' => 'insert',
    'error_code' => 1001
]);

// 使用file通道记录日志（默认通道）
MyLog::info('普通日志');
```

## 支持的日志级别

```php
MyLog::debug('调试信息', $context);
MyLog::info('一般信息', $context);
MyLog::notice('注意信息', $context);
MyLog::warning('警告信息', $context);
MyLog::error('错误信息', $context);
MyLog::critical('严重错误', $context);
MyLog::alert('必须立即处理', $context);
MyLog::emergency('系统不可用', $context);
```

## 日志输出格式

```
[INFO] 消息内容 {"timestamp":"2024-01-20 10:30:00","request_id":"req_65abcd123","context_data":{...}}
```

## 高级用法

### 1. 记录异常信息

```php
try {
    // 可能抛出异常的代码
} catch (\Exception $e) {
    MyLog::error('操作失败', [
        'exception' => [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
```

### 2. 记录数据库操作

```php
MyLog::info('数据库更新', [
    'table' => 'users',
    'operation' => 'update',
    'conditions' => ['id' => 1],
    'changes' => [
        'status' => 'active',
        'last_login' => 'now()'
    ]
]);
```

### 3. 记录API调用

```php
MyLog::info('API请求', [
    'endpoint' => 'https://api.example.com/users',
    'method' => 'POST',
    'request_data' => $requestData,
    'response_code' => $responseCode,
    'response_data' => $responseData
]);
```

### 4. 配置和使用多通道日志

#### 配置日志通道

在 ThinkPHP 的日志配置文件 `config/log.php` 中配置多个通道：

```php
return [
    'default' => 'file',
    'channels' => [
        'file' => [
            'type' => 'file',
            'path' => runtime_path() . 'log',
        ],
        'email' => [
            'type' => 'email',
            'to' => '<EMAIL>',
            'subject' => '系统日志',
        ],
        'mongo' => [
            'type' => 'mongodb',
            'host' => '127.0.0.1',
            'port' => 27017,
            'database' => 'logs',
            'collection' => 'system_logs',
        ],
        'redis' => [
            'type' => 'redis',
            'host' => '127.0.0.1',
            'port' => 6379,
            'key' => 'system_logs',
        ]
    ],
];
```

#### 根据不同场景使用不同通道

```php
// 普通日志使用默认通道
MyLog::info('普通操作日志');

// 重要错误使用邮件通道
MyLog::channel('email')->critical('系统崩溃', [
    'time' => date('Y-m-d H:i:s'),
    'memory_usage' => memory_get_usage(true),
    'server' => $_SERVER['SERVER_NAME']
]);

// 用户行为分析使用MongoDB通道
MyLog::channel('mongo')->info('用户行为', [
    'user_id' => $userId,
    'action' => 'click',
    'target' => 'buy_button',
    'page' => 'product_detail',
    'product_id' => $productId
]);

// 高频操作日志使用Redis通道
MyLog::channel('redis')->debug('API调用', [
    'api' => '/api/products',
    'method' => 'GET',
    'params' => $request->param(),
    'ip' => $request->ip()
]);
```

## 最佳实践

1. **选择适当的日志级别**
   - debug: 调试信息，仅在开发环境使用
   - info: 一般信息，正常的系统操作
   - notice: 需要注意的普通事件
   - warning: 警告但不是错误
   - error: 运行时错误
   - critical: 严重问题
   - alert: 必须立即处理的问题
   - emergency: 系统不可用

2. **结构化数据记录**
   ```php
   // 推荐
   MyLog::info('用户操作', [
       'action' => 'login',
       'user_id' => $userId,
       'ip' => $ip,
       'timestamp' => time()
   ]);

   // 不推荐
   MyLog::info("用户 {$userId} 从 {$ip} 登录");
   ```

3. **敏感数据处理**
   ```php
   MyLog::info('用户注册', [
       'email' => maskEmail($email),  // 对敏感信息进行脱敏
       'phone' => maskPhone($phone)
   ]);
   ```

4. **避免过多日志**
   - 不要记录无用信息
   - 避免在循环中频繁记录日志
   - 合理使用日志级别

5. **上下文信息完整性**
   ```php
   MyLog::error('支付失败', [
       'order_id' => $orderId,
       'amount' => $amount,
       'error_code' => $errorCode,
       'error_message' => $errorMessage,
       'payment_provider' => $provider
   ]);
   ```

## 注意事项

1. 不要记录敏感信息（如密码、令牌等）
2. 大数据量时注意日志文件大小
3. 定期清理或归档日志文件
4. 在生产环境中合理配置日志级别
5. 确保日志目录有适当的写入权限

## 常见问题

1. **日志文件权限问题**
   - 确保日志目录有写入权限
   - 检查用户组权限设置

2. **日志文件大小**
   - 配置日志文件轮转
   - 定期清理旧日志

3. **性能问题**
   - 避免在性能敏感的循环中记录日志
   - 使用批量日志记录
   - 考虑异步日志记录

## 更多帮助

如需更多帮助，请参考：
- ThinkPHP 8.0 日志文档
- 项目技术文档
- 或联系技术支持团队
