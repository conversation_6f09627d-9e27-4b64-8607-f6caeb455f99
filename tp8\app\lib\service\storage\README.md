# 云存储服务模块使用说明

## 1. 概述

云存储服务模块提供了统一的文件上传、删除和获取接口，支持多种云存储服务，包括七牛云、腾讯云COS和阿里云OSS。通过工厂模式实现了对不同云存储服务的统一管理，使开发者可以轻松切换不同的云存储服务而无需修改业务代码。

## 2. 目录结构

```
app/lib/service/storage/
├── CloudStorageService.php       # 云存储服务抽象基类
├── QiniuStorageService.php       # 七牛云存储服务实现
├── TencentCosStorageService.php  # 腾讯云COS存储服务实现
├── AliyunOssStorageService.php   # 阿里云OSS存储服务实现
└── CloudStorageFactory.php       # 云存储工厂类
```

## 3. 安装配置

### 3.1 安装依赖

根据需要使用的云存储服务，安装对应的SDK：

```bash
# 七牛云
composer require qiniu/php-sdk

# 腾讯云COS
composer require qcloud/cos-sdk-v5

# 阿里云OSS
composer require aliyuncs/oss-sdk-php
```

### 3.2 配置文件

在 `config/cloud_storage.php` 中配置云存储服务参数：

```php
return [
    // 默认存储类型
    'default' => 'qiniu',
    
    // 七牛云配置
    'qiniu' => [
        'accessKey' => env('QINIU_ACCESS_KEY', ''),
        'secretKey' => env('QINIU_SECRET_KEY', ''),
        'bucket' => env('QINIU_BUCKET', ''),
        'domain' => env('QINIU_DOMAIN', ''),
        'rules' => [
            'maxSize' => 10 * 1024 * 1024, // 10MB
            'allowedTypes' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'doc', 'docx']
        ]
    ],
    
    // 腾讯云COS配置
    'tencent_cos' => [
        'secretId' => env('TENCENT_COS_SECRET_ID', ''),
        'secretKey' => env('TENCENT_COS_SECRET_KEY', ''),
        'bucket' => env('TENCENT_COS_BUCKET', ''),
        'region' => env('TENCENT_COS_REGION', 'ap-beijing'),
        'domain' => env('TENCENT_COS_DOMAIN', ''),
        'rules' => [
            'maxSize' => 10 * 1024 * 1024, // 10MB
            'allowedTypes' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'doc', 'docx']
        ]
    ],
    
    // 阿里云OSS配置
    'aliyun_oss' => [
        'accessKeyId' => env('ALIYUN_OSS_ACCESS_KEY_ID', ''),
        'accessKeySecret' => env('ALIYUN_OSS_ACCESS_KEY_SECRET', ''),
        'bucket' => env('ALIYUN_OSS_BUCKET', ''),
        'endpoint' => env('ALIYUN_OSS_ENDPOINT', 'oss-cn-hangzhou.aliyuncs.com'),
        'domain' => env('ALIYUN_OSS_DOMAIN', ''),
        'rules' => [
            'maxSize' => 10 * 1024 * 1024, // 10MB
            'allowedTypes' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'doc', 'docx']
        ]
    ],
    
    // 通用上传规则
    'upload_rules' => [
        'image' => [
            'maxSize' => 5 * 1024 * 1024, // 5MB
            'allowedTypes' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
        ],
        'document' => [
            'maxSize' => 20 * 1024 * 1024, // 20MB
            'allowedTypes' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
        ],
        'media' => [
            'maxSize' => 100 * 1024 * 1024, // 100MB
            'allowedTypes' => ['mp3', 'wav', 'flac', 'aac', 'ogg', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
        ],
        'archive' => [
            'maxSize' => 50 * 1024 * 1024, // 50MB
            'allowedTypes' => ['zip', 'rar', '7z', 'tar', 'gz']
        ]
    ],
    
    // 上传路径配置
    'upload_paths' => [
        'image' => 'images/',
        'document' => 'documents/',
        'media' => 'media/',
        'archive' => 'archives/',
        'avatar' => 'avatars/',
        'temp' => 'temp/'
    ]
];
```

## 4. 使用方法

### 4.1 基本使用

#### 4.1.1 通过工厂类创建云存储服务

```php
use app\lib\service\storage\CloudStorageFactory;

// 创建默认的云存储服务
$storageService = CloudStorageFactory::getDefault();

// 创建指定类型的云存储服务
$qiniuService = CloudStorageFactory::create('qiniu');
$tencentService = CloudStorageFactory::create('tencent_cos');
$aliyunService = CloudStorageFactory::create('aliyun_oss');

// 使用自定义配置创建云存储服务
$customConfig = [
    'accessKey' => 'your_access_key',
    'secretKey' => 'your_secret_key',
    'bucket' => 'your_bucket',
    'domain' => 'your_domain.com'
];

// 完全使用自定义配置（不合并配置文件中的配置）
$customService = CloudStorageFactory::create('qiniu', $customConfig, false);

// 合并自定义配置和配置文件中的配置（自定义配置优先）
$mergedService = CloudStorageFactory::create('qiniu', $customConfig, true);
```

#### 4.1.2 上传文件

```php
use think\facade\Request;

// 获取上传的文件
$file = Request::file('file');

// 上传文件到云存储
$result = $storageService->upload($file, 'images/', 'custom_filename.jpg');

// 上传结果
print_r($result);
// 输出示例：
// [
//     'success' => true,
//     'key' => 'images/custom_filename.jpg',
//     'hash' => 'FhHmUYe49o4zP3q-tFKNxKUZCTjS',
//     'url' => 'https://your-domain.com/images/custom_filename.jpg',
//     'size' => 102400,
//     'originalName' => 'original.jpg',
//     'extension' => 'jpg',
//     'mimeType' => 'image/jpeg',
//     'storageType' => 'qiniu'
// ]
```

#### 4.1.3 删除文件

```php
// 删除文件
$key = 'images/custom_filename.jpg';
$result = $storageService->delete($key);

// 删除成功返回 true，失败抛出异常
var_dump($result); // bool(true)
```

#### 4.1.4 获取文件URL

```php
// 获取文件URL
$key = 'images/custom_filename.jpg';
$url = $storageService->getUrl($key);

echo $url; // https://your-domain.com/images/custom_filename.jpg
```

#### 4.1.5 检查文件是否存在

```php
// 检查文件是否存在
$key = 'images/custom_filename.jpg';
$exists = $storageService->exists($key);

var_dump($exists); // bool(true) 或 bool(false)
```

### 4.2 高级使用

#### 4.2.1 使用SysFileService进行文件管理

```php
use app\dogadmin\service\SysFileService;
use think\facade\Request;

$fileService = new SysFileService();

// 上传文件到云存储并保存记录到数据库
$file = Request::file('file');
$storageType = 'qiniu'; // 可选，默认使用配置的默认类型
$category = 'image';    // 可选，文件分类
$options = [];          // 可选，额外选项

$result = $fileService->uploadToCloud($file, $storageType, $category, $options);

// 使用自定义配置上传文件
$customConfig = [
    'accessKey' => 'your_access_key',
    'secretKey' => 'your_secret_key',
    'bucket' => 'your_bucket',
    'domain' => 'your_domain.com'
];
$options = [
    'config' => $customConfig,       // 自定义配置
    'merge_config' => true,          // 是否合并配置文件中的配置（默认为false）
    'upload_path' => 'custom/path/'  // 自定义上传路径
];
$result = $fileService->uploadToCloud($file, $storageType, $category, $options);

// 删除云存储文件及数据库记录
$fileId = 123; // 文件ID
$fileService->deleteCloudFile($fileId);

// 获取文件下载URL
$fileId = 123;     // 文件ID
$expires = 3600;   // 链接有效期（秒）
$url = $fileService->getDownloadUrl($fileId, $expires);
```

#### 4.2.2 使用控制器接口

系统提供了完整的RESTful API接口，可以通过HTTP请求进行文件操作：

```php
// 路由定义在 route/cloud_upload.php
```

**单文件上传：**
```http
POST /dogadmin/cloud-upload/upload
Content-Type: multipart/form-data

file: (二进制文件)
storage_type: qiniu
category: image
file_name: custom_name.jpg (可选)
config: {"accessKey":"your_key","secretKey":"your_secret"} (可选，JSON格式的自定义配置)
merge_config: true (可选，是否合并配置)
upload_path: custom/path/ (可选，自定义上传路径)
```

**批量文件上传：**
```http
POST /dogadmin/cloud-upload/batch-upload
Content-Type: multipart/form-data

files[]: (二进制文件1)
files[]: (二进制文件2)
storage_type: qiniu
category: image
config: {"accessKey":"your_key","secretKey":"your_secret"} (可选，JSON格式的自定义配置)
merge_config: true (可选，是否合并配置)
upload_path: custom/path/ (可选，自定义上传路径)
```

**删除文件：**
```http
DELETE /dogadmin/cloud-upload/delete
Content-Type: application/x-www-form-urlencoded

file_id=123
```

**获取下载链接：**
```http
GET /dogadmin/cloud-upload/download-url?file_id=123&expires=3600
```

**获取支持的存储类型：**
```http
GET /dogadmin/cloud-upload/supported-types
```

### 4.3 前端使用示例

#### 4.3.1 使用Axios上传文件

```javascript
import axios from 'axios';

// 单文件上传
async function uploadFile(file, storageType = 'qiniu', category = 'image', config = null) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('storage_type', storageType);
  formData.append('category', category);
  
  // 添加自定义配置
  if (config) {
    formData.append('config', JSON.stringify(config));
    formData.append('merge_config', 'true');
  }
  
  try {
    const response = await axios.post('/dogadmin/cloud-upload/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.data.code === 200) {
      console.log('上传成功:', response.data.data);
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('上传失败:', error);
    throw error;
  }
}

// 批量上传
async function batchUpload(files, storageType = 'qiniu', category = 'image', config = null) {
  const formData = new FormData();
  
  files.forEach(file => {
    formData.append('files[]', file);
  });
  
  formData.append('storage_type', storageType);
  formData.append('category', category);
  
  // 添加自定义配置
  if (config) {
    formData.append('config', JSON.stringify(config));
    formData.append('merge_config', 'true');
  }
  
  const response = await axios.post('/dogadmin/cloud-upload/batch-upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      'Authorization': `Bearer ${token}`
    }
  });
  
  return response.data;
}

// 删除文件
async function deleteFile(fileId) {
  const response = await axios.delete('/dogadmin/cloud-upload/delete', {
    data: { file_id: fileId },
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return response.data;
}

// 获取下载链接
async function getDownloadUrl(fileId, expires = 3600) {
  const response = await axios.get(`/dogadmin/cloud-upload/download-url?file_id=${fileId}&expires=${expires}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return response.data;
}
```

#### 4.3.2 使用Element UI上传组件

```vue
<template>
  <div>
    <el-upload
      action="/dogadmin/cloud-upload/upload"
      :headers="headers"
      :data="uploadData"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload">
      <el-button size="small" type="primary">点击上传</el-button>
    </el-upload>
    
    <div v-if="fileList.length > 0">
      <h3>已上传文件列表</h3>
      <ul>
        <li v-for="(file, index) in fileList" :key="index">
          {{ file.file_info.originalName }}
          <a :href="file.file_info.url" target="_blank">查看</a>
          <button @click="handleDelete(file.file_id)">删除</button>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      headers: {
        Authorization: `Bearer ${this.getToken()}`
      },
      uploadData: {
        storage_type: 'qiniu',
        category: 'image',
        // 可以添加自定义配置
        config: JSON.stringify({
          accessKey: 'your_custom_key',
          secretKey: 'your_custom_secret'
        }),
        merge_config: true
      },
      fileList: []
    };
  },
  methods: {
    getToken() {
      // 获取认证token的方法
      return localStorage.getItem('token') || '';
    },
    beforeUpload(file) {
      // 上传前检查文件类型和大小
      const isImage = file.type.startsWith('image/');
      const isLt5M = file.size / 1024 / 1024 < 5;
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!');
        return false;
      }
      return true;
    },
    handleSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.$message.success('上传成功');
        this.fileList.push(response.data);
      } else {
        this.$message.error(response.message || '上传失败');
      }
    },
    handleError(err) {
      console.error(err);
      this.$message.error('上传失败');
    },
    async handleDelete(fileId) {
      try {
        const res = await this.axios.delete('/dogadmin/cloud-upload/delete', {
          data: { file_id: fileId },
          headers: this.headers
        });
        
        if (res.data.code === 200) {
          this.$message.success('删除成功');
          this.fileList = this.fileList.filter(item => item.file_id !== fileId);
        } else {
          this.$message.error(res.data.message || '删除失败');
        }
      } catch (error) {
        console.error(error);
        this.$message.error('删除失败');
      }
    }
  }
};
</script>
```

## 5. 注意事项

1. **安全性**：请确保云存储的访问密钥等敏感信息安全存储，建议使用环境变量或其他安全的配置管理方式。

2. **文件大小限制**：根据不同的云存储服务和配置，可能存在文件大小限制，请在上传前进行验证。

3. **文件类型限制**：为了安全起见，建议限制允许上传的文件类型，防止恶意文件上传。

4. **错误处理**：云存储服务可能因网络、配置等原因导致上传失败，请做好错误处理和重试机制。

5. **成本控制**：云存储服务通常按使用量计费，请注意控制成本，避免不必要的文件上传和存储。

6. **配置管理**：系统支持多种配置方式（环境变量、配置文件、外部传入），请根据实际需求选择合适的配置方式。

## 6. 故障排除

### 6.1 常见问题

1. **配置错误**：检查云存储配置是否正确，包括密钥、bucket名称、域名等。

2. **网络问题**：确保服务器能够访问云存储服务的API。

3. **权限问题**：检查云存储账户是否有相应的读写权限。

4. **依赖问题**：确保已安装相应的SDK依赖包。

5. **配置优先级**：当使用多种配置方式时，请注意配置的优先级：外部传入配置 > 配置文件 > 环境变量默认值。

### 6.2 调试方法

1. 开启调试模式，查看详细的错误信息。

2. 检查日志文件，了解错误发生的具体原因。

3. 使用云存储服务提供的调试工具进行问题排查。

4. 尝试使用云存储服务的官方SDK示例进行测试，确认是否为配置问题。

5. 使用 `var_dump()` 或 `print_r()` 输出配置信息，确认配置是否正确合并或覆盖。

## 7. 扩展开发

如果需要支持其他云存储服务，可以按照以下步骤进行扩展：

1. 创建新的存储服务类，继承 `CloudStorageService` 抽象类。

2. 实现抽象方法：`init()`、`upload()`、`delete()`、`getUrl()`、`exists()`。

3. 在 `CloudStorageFactory` 中注册新的存储服务类。

```php
// 示例：添加新的云存储服务
class NewStorageService extends CloudStorageService
{
    protected function init(): void
    {
        // 初始化配置
    }
    
    public function upload(File $file, string $path = '', string $fileName = ''): array
    {
        // 实现上传逻辑
    }
    
    public function delete(string $key): bool
    {
        // 实现删除逻辑
    }
    
    public function getUrl(string $key): string
    {
        // 实现获取URL逻辑
    }
    
    public function exists(string $key): bool
    {
        // 实现检查文件是否存在的逻辑
    }
}

// 在CloudStorageFactory中注册
class CloudStorageFactory
{
    private static array $supportedTypes = [
        'qiniu' => QiniuStorageService::class,
        'tencent_cos' => TencentCosStorageService::class,
        'aliyun_oss' => AliyunOssStorageService::class,
        'new_storage' => NewStorageService::class, // 添加新的存储服务
    ];
    
    // ...
}
```