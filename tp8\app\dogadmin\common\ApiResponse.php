<?php

namespace app\dogadmin\common;

class ApiResponse
{
    /**
     * 成功响应
     * @param mixed $data 响应数据
     * @param string $message 响应信息
     */
    public static function success($data = null, string $message = '')
    {
        return json([
            'code' => ResponseCode::SUCCESS,
            'msg' => $message ?: ResponseCode::getMessage(ResponseCode::SUCCESS),
            'data' => $data
        ]);
    }

    /**
     * 错误响应
     * @param int $code 错误码
     * @param string $message 错误信息
     * @param mixed $data 额外数据
     */
    public static function error(int $code = ResponseCode::ERROR, string $message = '', $data = null)
    {
        return json([
            'code' => $code,
            'msg' => $message ?: ResponseCode::getMessage($code),
            'data' => $data
        ]);
    }

    /**
     * 未登录响应
     * @param string $message 自定义提示信息
     */
    public static function notLogin(string $message = '')
    {
        return self::error(ResponseCode::NOT_LOGIN, $message);
    }

    /**
     * 无权限响应
     * @param string $message 自定义提示信息
     */
    public static function noPermission(string $message = '')
    {
        return self::error(ResponseCode::NO_PERMISSION, $message);
    }

    /**
     * 参数错误响应
     * @param string $message 自定义提示信息
     */
    public static function paramError(string $message = '')
    {
        return self::error(ResponseCode::PARAM_ERROR, $message);
    }

    /**
     * 业务错误响应
     * @param string $message 自定义提示信息
     */
    public static function businessError(string $message = '')
    {
        return self::error(ResponseCode::BUSINESS_ERROR, $message);
    }

    /**
     * 系统错误响应
     * @param string $message 自定义提示信息
     */
    public static function systemError(string $message = '')
    {
        return self::error(ResponseCode::SYSTEM_ERROR, $message);
    }
}