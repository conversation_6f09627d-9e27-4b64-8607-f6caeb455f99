import store from '@/store/index.js';
const mixins = {
	methods: {
		check(isPhone = true) {
			// console.log('check');
			let isLogin = store.getters.isLogin;
			if (isLogin) {
				if (isPhone) {
					let userInfo = store.getters.userInfo;
					if (userInfo.phone) {
						return true;
					} else {
						uni.showModal({
							title: '提示',
							content: '暂未绑定手机号，请绑定手机号后发布',
							confirmText: '立即绑定',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/user/phone'
									})
								} else if (res.cancel) {
									uni.showToast({
										title: '功能无法使用',
										icon: 'none'
									})
								}
							}
						})
					}

				} else {
					return true;
				}
			} else {
				// this.$refs.logintips.show = true;
				uni.showModal({
					title: '提示',
					content: '暂未登录，是否立即登录后操作',
					confirmText: '立即登录',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login/login'
							})
						} else if (res.cancel) {
							uni.showToast({
								title: '未登录部分功能无法使用',
								icon: 'none'
							})
						}
					}
				})
			}
			return false;
		}
	}
}
export default mixins;
