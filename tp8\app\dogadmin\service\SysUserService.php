<?php

namespace app\dogadmin\service;

use app\dogadmin\model\SysUserModel;
use app\dogadmin\service\SysRoleService;
use app\dogadmin\service\SysDeptService;
use app\dogadmin\service\SysUserDeptService;

class SysUserService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysUserModel();
    }

    /**
     * 根据条件获取用户信息
     * @param array $where 查询条件
     * @return array|null 用户信息
     */
    public function getUserByWhere($where)
    {
        $user = $this->model->where($where)->find();
        return $user ? $user->toArray() : null;
    }

    /**
     * 用户登录
     * @param array $params 登录参数
     * @return array|false 成功返回用户信息，失败返回false
     */
    public function login($params)
    {
        $where = [
            ['status','=',1],
            ['login_name','=',$params['login_name']],
            ['password','=',md5_salt($params['password'])]
        ];
        $res = $this->model->where($where)->find();
        return $res;
    }


    public function originPassword($params){
        $where = [
            ['id','=',$params['admin_id']],
        ];
        $data = [
            'status' => 1,
            'password' => md5_salt($params['password'])
        ];
        $res = $this->model->where($where)->update($data);
        return $res;
    }

    public function resetPwd($params)
    {
        $where = [
            ['id','=',$params['admin_id']]
        ];
        $data = [
            'password' => md5_salt($params['password'])
        ];
        $res = $this->model->where($where)->update($data);
        return $res;
    }
    
    /**
     * 获取用户个人资料信息
     * @param int $userId 用户ID
     * @param array $roleIds 用户角色ID
     * @return array 用户个人资料
     */
    public function getPersonalData($userId,$roleIds)
    {
        // 获取基本用户信息
        $userInfo = $this->getUserByWhere([
            ['id', '=', $userId],
            ['status', '=', 1],
        ]);
        
        if (empty($userInfo)) {
            return null;
        }
        
        // 获取用户角色信息
        // $userRoleService = new SysUserRoleService();
        // $roleIds = $userRoleService->getRoleIdsByUserId($userId);
        
        // 获取角色名称
        $roleService = new SysRoleService();
        $userInfo['role_name'] = $roleService->getRoleNamesByIds($roleIds);
        
        // 获取部门信息
        $userDeptService = new SysUserDeptService();
        $deptId = $userDeptService->getDeptIdByUserId($userId);
        
        $deptService = new SysDeptService();
        $userInfo['dept_name'] = $deptService->getDeptNameById($deptId ?? 0);
        
        // 格式化返回数据
        $result = [
            'avatar' => $userInfo['avatar'] ?? '',
            'login_name' => $userInfo['login_name'] ?? '',
            'user_name' => $userInfo['user_name'] ?? '',
            'phone' => $userInfo['phone'] ?? '',
            'email' => $userInfo['email'] ?? '',
            'dept_name' => $userInfo['dept_name'] ?? '',
            'role_name' => $userInfo['role_name'] ?? '',
            'create_time' => $userInfo['create_time'] ?? '',
            'sex' => $userInfo['sex'] ?? '',
        ];
        
        return $result;
    }

    public function updateBasicData($params){
        $where = [
            ['id','=',$params['admin_id']]
        ];
        $res = $this->model->where($where)->strict(false)->update($params);
        return $res;
    }

    public function getAdminById($id)
    {
        $where = [
            ['id', '=', $id],
            ['status', '=', 1]
        ];
        $res = $this->model->where($where)->find();
        return $res;
    }
}