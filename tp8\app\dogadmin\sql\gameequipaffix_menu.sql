-- 添加装备词条菜单


-- 添加装备词条主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `name`, `component`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('装备词条', 122, '2', '/game/gameEquipAffix/index', 'gameEquipAffixPage', 'game/gameEquipAffix/index', 'Menu', 'game:gameEquipAffix:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的菜单ID
SET @menuId = LAST_INSERT_ID();

-- 添加装备词条按钮权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `auth`, `status`, `is_hide`,`create_time`, `update_time`) VALUES
('查询', @menuId, '3', 'game:gameEquipAffix:listPage', '1','0', NOW(), NOW()),
('获取详情', @menuId, '3', 'game:gameEquipAffix:getById', '1','0', NOW(), NOW()),
('获取排序', @menuId, '3', 'game:gameEquipAffix:getSorted', '1','0', NOW(), NOW()),
('新增', @menuId, '3', 'game:gameEquipAffix:add', '1','0', NOW(), NOW()),
('修改', @menuId, '3', 'game:gameEquipAffix:update', '1','0', NOW(), NOW()),
('删除', @menuId, '3', 'game:gameEquipAffix:deleteById', '1','0', NOW(), NOW()),
('批量删除', @menuId, '3', 'game:gameEquipAffix:batchDelete', '1','0', NOW(), NOW()),
('更新状态', @menuId, '3', 'game:gameEquipAffix:updateStatus', '1','0', NOW(), NOW());