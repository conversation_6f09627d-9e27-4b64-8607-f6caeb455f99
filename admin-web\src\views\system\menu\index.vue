<template>
  <div class="koi-flex">
    <KoiCard>
      <!-- 搜索条件 -->
      <el-form v-show="showSearch" :inline="true">
        <el-form-item label="菜单名称" prop="menu_name">
          <el-input
            placeholder="请输入菜单名称"
            v-model="searchParams.menu_name"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleTreeList"
          ></el-input>
        </el-form-item>
        <el-form-item label="菜单状态" prop="status">
          <el-select
            placeholder="请选择菜单状态"
            v-model="searchParams.status"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleTreeList"
          >
            <el-option label="启用" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="权限标识" prop="auth">
          <el-input
            placeholder="请输入权限标识"
            v-model="searchParams.auth"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleTreeList"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch()" v-auth="['system:menu:search']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch()">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格头部按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5" v-auth="['system:menu:add']">
          <el-button type="primary" icon="plus" plain @click="handleAdd()">新增</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['system:menu:update']">
          <el-button type="success" icon="edit" plain @click="handleUpdate()" :disabled="single">修改</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['system:menu:delete']">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete()" :disabled="multiple">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" icon="Sort" plain @click="toggleExpandAll()">展开/折叠</el-button>
        </el-col>
        <KoiToolbar v-model:showSearch="showSearch" @refreshTable="handleTreeList"></KoiToolbar>
      </el-row>

      <div class="h-20px"></div>
      <!-- 数据表格 -->
      <el-table
        v-if="refreshTreeTable"
        v-loading="loading"
        border
        :data="tableList"
        @selection-change="handleSelectionChange"
        :default-expand-all="isExpandAll"
        :expand-row-keys="expandKey"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        empty-text="暂时没有数据哟🌻"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" prop="id" width="80px" align="center"></el-table-column>
        <el-table-column
          label="菜单名称"
          prop="menu_name"
          width="160px"
          align="left"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="英文名称"
          prop="en_name"
          width="160px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column label="菜单类型" prop="menu_type" width="100px" align="center">
          <template #default="scope">
            <KoiTag :tagOptions="koiDicts.sys_menu_type" :value="scope.row.menu_type"></KoiTag>
          </template>
        </el-table-column>
        <el-table-column label="展开/折叠" prop="is_spread" width="100px" align="center">
          <template #default="scope">
            <el-switch
              v-show="scope.row.menu_type == '1' || scope.row.menu_type == '2'"
              v-model="scope.row.is_spread"
              active-text="展开"
              inactive-text="折叠"
              active-value="0"
              inactive-value="1"
              :inline-prompt="true"
              @click="handleIsSpread(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="图标" prop="icon" width="80px" align="center">
          <template #default="scope">
            <!-- 使用 is 属性绑定组件名称 -->
            <div class="flex flex-justify-center">
              <el-icon v-if="scope.row.icon && scope.row.icon.indexOf('koi-') == '-1'" :size="20">
                <component :is="scope.row.icon"></component>
              </el-icon>
              <el-icon v-if="scope.row.icon && scope.row.icon.indexOf('koi-') == '0'" :size="20">
                <component is="SvgIcon" :name="scope.row.icon"></component>
              </el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="权限标识"
          prop="auth"
          width="220px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="页面路径"
          prop="component"
          width="220px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <!-- 注意：如果后端数据返回的是字符串"0" OR "1"，这里的active-value AND inactive-value不需要加冒号，会认为是字符串，否则：后端返回是0 AND 1数字，则需要添加冒号 -->
        <el-table-column label="菜单状态" prop="status" width="100px" align="center">
          <template #default="scope">
            <!-- {{ scope.row.status }} -->
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              active-value="1"
              inactive-value="0"
              :inline-prompt="true"
              @click="handleSwitch(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="是否隐藏" prop="is_hide" width="100px" align="center" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-tag :type="scope.row.is_hide == '0' ? 'danger' : scope.row.is_hide == '1' ? 'primary' : 'warning'">
              {{ scope.row.is_hide == "0" ? "隐藏" : scope.row.is_hide == "1" ? "显示" : "未知状态" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="路由path" prop="path" width="180px" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="显示顺序" prop="sorted" width="90px" align="center"></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="200"
          fixed="right"
          v-auth="['system:menu:add', 'system:menu:update', 'system:menu:delete', 'system:menu:addRoute']"
        >
          <template #default="{ row }">
            <el-tooltip content="新增🌻" placement="top">
              <el-button
                type="primary"
                icon="CirclePlus"
                circle
                plain
                @click="handleAdd(row)"
                v-auth="['system:menu:add']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="修改🌻" placement="top">
              <el-button
                type="success"
                icon="Edit"
                circle
                plain
                @click="handleUpdate(row)"
                v-auth="['system:menu:update']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button
                type="danger"
                icon="Delete"
                circle
                plain
                @click="handleDelete(row)"
                v-auth="['system:menu:delete']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="生成路由🌻" placement="top" v-if="row.menu_type == 2">
              <el-button
                type="success"
                icon="Check"
                circle
                plain
                @click="handleButtonAuth(row)"
                v-auth="['system:menu:addRoute']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!-- 添加 OR 修改 -->
      <KoiDialog
        ref="koiDialogRef"
        :title="title"
        @koiConfirm="handleConfirm"
        @koiCancel="handleCancel"
        :loading="confirmLoading"
        :height="500"
      >
        <template #content>
          <el-form ref="formRef" :rules="rules" :model="form" label-width="auto" status-icon>
            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <!-- 菜单级联选择框 -->
                <el-form-item label="菜单上级" prop="parent_id">
                  <el-cascader
                    placeholder="请选择菜单上级"
                    v-model="form.parent_id"
                    :options="cascaderOptions"
                    :props="{
                      expandTrigger: 'hover',
                      emitPath: false,
                      checkStrictly: true
                    }"
                    filterable
                    clearable
                    style="width: 540px"
                  >
                    <template #default="{ node, data }">
                      <span>{{ data.label }}</span>
                      <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
                    </template>
                  </el-cascader>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="菜单类型" prop="menu_type">
                  <el-radio-group v-model="form.menu_type">
                    <el-radio
                      v-for="(item, index) in koiDicts.sys_menu_type"
                      :key="item.dict_value + index"
                      :value="item.dict_value"
                      border
                      @change="handleMenuType"
                      >{{ item.dict_label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 16 }" v-if="form.menu_type < 3">
                <div class="flex items-center m-b-15px m-l-10px">
                  <el-form-item prop="icon"></el-form-item>
                  <div class="w-70px m-r-10px">菜单图标</div>
                  <KoiSelectIcon v-model="form.icon"></KoiSelectIcon>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                <el-form-item label="菜单名称" prop="menu_name">
                  <el-input v-model="form.menu_name" placeholder="请输入菜单名称" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                <el-form-item label="显示排序" prop="sorted" class="p-l-10px">
                  <el-input-number v-model="form.sorted" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-if="form.menu_type < 3">
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                <el-form-item label="英文菜单" prop="en_name">
                  <el-input v-model="form.en_name" placeholder="请输入英文菜单" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" class="p-l-10px">
                <el-form-item label="选中路由" prop="active_menu">
                  <el-input v-model="form.active_menu" placeholder="例如：/system/dict/type" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                <el-form-item label="是否隐藏" prop="is_hide">
                  <el-radio-group v-model="form.is_hide">
                    <el-radio value="0">是</el-radio>
                    <el-radio value="1">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" class="p-l-10px">
                <el-form-item label="权限字符" prop="auth">
                  <el-input v-model="form.auth" placeholder="权限字符[system:user:list]" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-if="form.menu_type == '2'">
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                <el-form-item label="页面路径" prop="component">
                  <el-input v-model="form.component" placeholder="请输入页面路径[system/user/index]" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" class="p-l-10px">
                <el-form-item label="是否缓存" prop="is_keep_alive">
                  <el-radio-group v-model="form.is_keep_alive">
                    <el-radio value="0">是</el-radio>
                    <el-radio value="1">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-if="form.menu_type < 3">
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                <el-form-item label="路由名称" prop="name">
                  <el-input v-model="form.name" placeholder="例如：userPage[唯一]" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" class="p-l-10px">
                <el-form-item label="路由Path" prop="path">
                  <el-input v-model="form.path" placeholder="例如：/system/user[唯一]" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" v-if="form.menu_type < 3">
                <el-form-item label="是否展开" prop="is_spread">
                  <el-radio-group v-model="form.is_spread">
                    <el-radio value="0">是</el-radio>
                    <el-radio value="1">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" class="p-l-10px" v-if="form.menu_type < 3">
                <el-form-item label="是否固钉" prop="is_affix">
                  <el-radio-group v-model="form.is_affix">
                    <el-radio value="0">是</el-radio>
                    <el-radio value="1">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="外链地址" prop="is_link">
                  <el-input v-model="form.is_link" placeholder="请输入外链地址[输入值则判断为外链地址]" clearable />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <!-- {{ form }} -->
        </template>
      </KoiDialog>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="menuPage">
import { nextTick, ref, reactive, onMounted } from "vue";
import { koiNoticeSuccess, koiNoticeError, koiMsgError, koiMsgWarning, koiMsgBox, koiMsgInfo } from "@/utils/koi.ts";
import { handleTree } from "@/utils/index.ts";
import {
  listPage,
  cascaderList,
  getById,
  add,
  update,
  deleteById,
  batchDelete,
  updateStatus,
  updateSpread,
  addButtonAuth
} from "@/api/system/menu/index.ts";
import { useKoiDict } from "@/hooks/dicts/index.ts";
import useTabsStore from "@/stores/modules/tabs.ts";

const tabsStore = useTabsStore();
const { koiDicts } = useKoiDict(["sys_menu_type"]);
// 数据表格加载页面动画
const loading = ref(false);
/** 是否显示搜索表单 */
const showSearch = ref<boolean>(true); // 默认显示搜索条件
// 数据表格数据
const tableList = ref([]);

// 查询参数
const searchParams = ref({
  menu_name: "",
  auth: "",
  status: "",
  page_no: 1,
  page_size: 999999
});

// 重置搜索参数
const resetSearchParams = () => {
  searchParams.value = {
    menu_name: "",
    auth: "",
    status: "",
    page_no: 1,
    page_size: 999999
  };
};

/** 搜索 */
const handleSearch = () => {
  console.log("搜索");
  handleTreeData();
};

/** 重置 */
const resetSearch = () => {
  console.log("重置搜索");
  resetSearchParams();
  handleTreeList();
};

/** 树形表格查询 */
const handleTreeList = async () => {
  try {
    loading.value = true;
    tableList.value = []; // 重置表格数据
    const res: any = await listPage(searchParams.value);
    // console.log("菜单数据表格数据->", res.data.records);
    handleExpandKey(res.data.records);
    tableList.value = handleTree(res.data.records, "id");
    loading.value = false;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

/** 树形表格[删除、批量删除等刷新使用] */
const handleTreeData = async () => {
  try {
    const res: any = await listPage(searchParams.value);
    // console.log("菜单数据表格数据->", res.data.records);
    handleExpandKey(res.data.records);
    tableList.value = handleTree(res.data.records, "id");
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

// 展开数据
const expandKey = ref();
/** 展开节点 */
const handleExpandKey = (data: any) => {
  /* 展开节点开始 */
  if (data != null && data.length != 0) {
    expandKey.value = [];
    const resultList: string[] = [];
    data.forEach((obj: any) => {
      if (obj.parent_id == "0" && obj.is_spread == "0") {
        resultList.push(obj.id);
      }
      if (obj.parent_id != "0" && obj.is_spread == "0") {
        resultList.push(obj.id);
        resultList.push(obj.parent_id);
      }
    });
    // 过滤数据
    const uniqueArray = [...new Set(resultList)];
    // console.log("展开节点", uniqueArray);
    // 数组必须转为String类型的才生效
    expandKey.value = uniqueArray.map(String);
  } else {
    expandKey.value = [];
  }
};

onMounted(() => {
  // 获取数据表格数据
  handleTreeList();
});

const ids = ref([]); // 选中数组
const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
/** 是否多选 */
const handleSelectionChange = (selection: any) => {
  ids.value = selection.map((item: any) => item.id);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
};

// 级联下拉框
let cascaderOptions = ref<any>([]);
/** 菜单级联数据 */
const handleCascader = async () => {
  try {
    cascaderOptions.value = [];
    const res: any = await cascaderList();
    if (res.data != null && res.data != undefined && res.data.length > 0) {
      cascaderOptions.value = handleTree(res.data, "value");
    }
    cascaderOptions.value.unshift({
      label: "最顶级菜单",
      value: 0,
      parent_id: -1
    });
  } catch (error) {
    console.log(error);
    koiMsgError("菜单级联数据查询失败，请重试🌻");
  }
};

// 重新渲染表格状态
const refreshTreeTable = ref(true);
// 是否展开，默认折叠
const isExpandAll = ref(false);
/** 展开/折叠 */
const toggleExpandAll = () => {
  refreshTreeTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTreeTable.value = true;
  });
};

/** 菜单、目录、按钮类型默认是否隐藏 */
const handleMenuType = () => {
  if (form.value.menu_type == "3") {
    form.value.is_hide = "0";
  } else {
    form.value.is_hide = "1";
  }
};

/** 添加 */
const handleAdd = (row?: any) => {
  // 打开弹出框
  koiDialogRef.value.koiOpen();
  koiNoticeSuccess("添加🌻");
  // 重置表单
  resetForm();
  // 重置图标
  form.value.icon = "";
  // 标题
  title.value = "菜单添加";
  handleCascader();
  form.value.status = "1";
  form.value.parent_id = 0;
  form.value.menu_type = "1";
  if (row) {
    nextTick(() => {
      form.value.parent_id = row?.id;
      form.value.menu_type = row?.menu_type === "1" ? "2" : "3";
      form.value.is_hide = form.value.menu_type === "3" ? "0" : "1";
    });
  }
};

const is_affix = ref();
const path = ref();
/** 回显数据 */
const handleEcho = async (id: any) => {
  if (id == null || id == "") {
    koiMsgWarning("请选择需要修改的数据🌻");
    return;
  }
  try {
    const res: any = await getById(id);
    console.log("菜单回显数据", res.data);
    is_affix.value = res.data?.is_affix;
    path.value = res.data?.path;
    form.value = res.data;
    // 后端返回是数字类型，这里需要转换为字符串进行回显
    form.value.parent_id = res.data.parent_id;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据获取失败，请刷新重试🌻");
  }
};

/** 修改 */
const handleUpdate = async (row?: any) => {
  // 打开弹出框
  koiDialogRef.value.koiOpen();
  koiNoticeSuccess("修改🌻");
  // 重置表单
  resetForm();
  // 标题
  title.value = "菜单修改";
  const id = row ? row.id : ids.value[0];
  if (id == null || id == "") {
    koiMsgError("请选择需要修改的数据🌻");
  }
  handleCascader();
  // 回显数据
  handleEcho(id);
};

/** 添加 AND 修改弹出框 */
const koiDialogRef = ref();
// 标题
const title = ref("菜单管理");
// form表单Ref
const formRef = ref();
// form表单
let form = ref<any>();

/** 清空表单数据 */
const resetForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (formRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      formRef.value.resetFields();
    }
  });
  form.value = {
    parent_id: 0,
    menu_type: "",
    icon: "",
    menu_name: "",
    en_name: "",
    name: "",
    path: "",
    component: "",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_spread: "1",
    auth: "",
    status: "1",
    is_full: "1",
    is_affix: "1",
    active_menu: "",
    sorted: 1
  };
};

/** 表单规则 */
const rules = reactive({
  parent_id: [{ required: true, message: "请选择上级菜单", trigger: "blur" }],
  menu_type: [{ required: true, message: "请选择菜单类型", trigger: "blur" }],
  menu_name: [{ required: true, message: "请输入菜单名称", trigger: "blur" }],
  is_hide: [{ required: true, message: "请选择是否隐藏", trigger: "blur" }],
  auth: [{ required: true, message: "请输入权限字符", trigger: "blur" }],
  sorted: [{ required: true, message: "请输入排序号", trigger: "blur" }]
});

// 确定按钮是否显示loading
const confirmLoading = ref(false);
/** 确定  */
const handleConfirm = () => {
  if (!formRef.value) return;
  confirmLoading.value = true;
  (formRef.value as any).validate(async (valid: any) => {
    if (valid) {
      if (form.value.id != null && form.value.id != "") {
        try {
          if (is_affix.value != form.value.is_affix) {
            const closeIcon = form.value.is_affix == 0 ? false : true;
            tabsStore.replaceIsAffix(path.value, closeIcon);
          }
          await update(form.value);
          koiNoticeSuccess("修改成功🌻");
          confirmLoading.value = false;
          koiDialogRef.value.koiQuickClose();
          handleTreeList();
        } catch (error) {
          console.log(error);
          confirmLoading.value = false;
          koiNoticeError("修改失败，请刷新重试🌻");
        }
      } else {
        try {
          await add(form.value);
          koiNoticeSuccess("添加成功🌻");
          confirmLoading.value = false;
          koiDialogRef.value.koiQuickClose();
          handleTreeList();
        } catch (error) {
          console.log(error);
          confirmLoading.value = false;
          koiNoticeError("添加失败，请刷新重试🌻");
        }
      }
    } else {
      koiMsgError("验证失败，请检查填写内容🌻");
      confirmLoading.value = false;
    }
  });
};

/** 取消 */
const handleCancel = () => {
  koiDialogRef.value.koiClose();
};

/** 状态switch */
const handleSwitch = (row: any) => {
  let text = row.status === "1" ? "启用" : "停用";
  koiMsgBox("确认要[" + text + "]-[" + row.menu_name + "]菜单吗？")
    .then(async () => {
      if (!row.id || !row.status) {
        row.status = row.status == "1" ? "0" : "1";
        koiMsgWarning("请选择需要修改的数据🌻");
        return;
      }
      try {
        await updateStatus(row.id, row.status);
        koiNoticeSuccess("修改成功🌻");
      } catch (error) {
        console.log(error);
        koiNoticeError("修改失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      row.status = row.status == "1" ? "0" : "1";
      koiMsgError("已取消🌻");
    });
};

/** 是否展开 */
const handleIsSpread = async (row: any) => {
  if (!row.id || !row.is_spread) {
    koiMsgWarning("请选择需要展开的数据🌻");
    return;
  }
  try {
    await updateSpread(row.id, row.is_spread);
    handleTreeData();
    koiNoticeSuccess("操作成功🌻");
  } catch (error) {
    console.log(error);
    koiNoticeError("操作失败，请刷新重试🌻");
  }
};

/** 删除 */
const handleDelete = (row: any) => {
  const id = row.id;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认需要删除菜单名称[" + row.menu_name + "]么？")
    .then(async () => {
      try {
        await deleteById(id);
        koiNoticeSuccess("删除成功🌻");
        handleTreeData();
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 批量删除 */
const handleBatchDelete = () => {
  if (ids.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认需要进行批量删除么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await batchDelete(ids.value);
        koiNoticeSuccess("批量删除成功🌻");
        handleTreeData();
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 一键生成通用路由
const handleButtonAuth = (row: any) => {
  console.log("handleButtonAuth", row);
  koiMsgBox("一键生成权限按钮，是否确定")
    .then(async () => {
      try {
        let data = {
          parent_id: row.id,
          auth: row.auth
        };
        await addButtonAuth(data);
        koiNoticeSuccess("操作成功🌻");
        handleTreeData();
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};
</script>

<style lang="scss" scoped></style>
