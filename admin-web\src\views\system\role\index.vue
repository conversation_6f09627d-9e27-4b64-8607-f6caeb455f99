<template>
  <div class="koi-flex">
    <KoiCard>
      <!-- 搜索条件 -->
      <el-form v-show="showSearch" :inline="true">
        <el-form-item label="角色名称" prop="role_name">
          <el-input
            placeholder="请输入角色名称"
            v-model="searchParams.role_name"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item label="角色编号" prop="role_code">
          <el-input
            placeholder="请输入角色编号"
            v-model="searchParams.role_code"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item label="角色状态" prop="status">
          <el-select
            placeholder="请选择角色状态"
            v-model="searchParams.status"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleListPage"
          >
            <el-option label="启用" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据范围" prop="data_scope">
          <el-select
            placeholder="请选择数据范围"
            v-model="searchParams.data_scope"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleListPage"
          >
            <el-option label="全部数据权限" value="1" />
            <el-option label="自定义数据权限" value="2" />
            <el-option label="本部门数据权限" value="3" />
            <el-option label="本部门及以下数据权限" value="4" />
            <el-option label="仅本人数据权限" value="5" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch()" v-auth="['system:role:search']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch()">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 表格头部按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5" v-auth="['system:role:add']">
          <el-button type="primary" icon="plus" plain @click="handleAdd()">新增</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['system:role:update']">
          <el-button type="success" icon="edit" plain @click="handleUpdate()" :disabled="single">修改</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['system:role:delete']">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete()" :disabled="multiple">删除</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['system:role:menu']">
          <el-button type="success" icon="Postcard" plain :disabled="single" @click="handleAssignMenu()">分配菜单</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['system:role:dept']">
          <el-button type="primary" icon="FolderOpened" plain :disabled="single" @click="handleAssignDept()">分配部门</el-button>
        </el-col>
        <KoiToolbar v-model:showSearch="showSearch" @refreshTable="handleListPage"></KoiToolbar>
      </el-row>

      <div class="h-20px"></div>
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        border
        :data="tableList"
        empty-text="暂时没有数据哟🌻"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" prop="id" width="120px" align="center" type="index"></el-table-column>
        <el-table-column
          label="角色名称"
          prop="role_name"
          width="150px"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column label="角色编号" prop="role_code" width="150px" align="center"></el-table-column>
        <!-- 注意：如果后端数据返回的是字符串"0" OR "1"，这里的active-value AND inactive-value不需要加冒号，会认为是字符串，否则：后端返回是0 AND 1数字，则需要添加冒号 -->
        <el-table-column label="角色状态" prop="status" width="100px" align="center">
          <template #default="scope">
            <!-- {{ scope.row.status }} -->
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              active-value="1"
              inactive-value="0"
              :inline-prompt="true"
              @change="handleSwitch(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="data_scope" label="数据范围" width="150" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.data_scope == 1" type="primary">全部数据权限</el-tag>
            <el-tag v-else-if="scope.row.data_scope == 2" type="warning">自定义数据权限</el-tag>
            <el-tag v-else-if="scope.row.data_scope == 3" type="success">本部门数据权限</el-tag>
            <el-tag v-else-if="scope.row.data_scope == 4" type="info">本部门及以下数据权限</el-tag>
            <el-tag v-else-if="scope.row.data_scope == 5" type="danger">仅本人数据权限</el-tag>
            <el-tag v-else type="">未设置</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="角色排序" prop="sorted" width="100px" align="center"></el-table-column>
        <el-table-column
          label="角色备注"
          prop="remark"
          width="260px"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column label="创建时间" prop="create_time" width="180px" align="center"></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="200"
          fixed="right"
          v-auth="['system:role:update', 'system:role:delete', 'system:role:menu', 'system:role:dept']"
        >
          <template #default="{ row }">
            <el-tooltip content="修改🌻" placement="top">
              <el-button
                type="primary"
                icon="Edit"
                circle
                plain
                @click="handleUpdate(row)"
                v-auth="['system:role:update']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button
                type="danger"
                icon="Delete"
                circle
                plain
                @click="handleDelete(row)"
                v-auth="['system:role:delete']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="分配菜单数据权限🌻" placement="top" v-if="row.id != 1">
              <el-button
                type="info"
                icon="Postcard"
                circle
                plain
                @click="handleAssignMenu(row)"
                v-auth="['system:role:menu']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="分配部门数据权限🌻" placement="top" v-if="row.id != 1">
              <el-button
                type="success"
                icon="FolderOpened"
                circle
                plain
                @click="handleAssignDept(row)"
                v-auth="['system:role:dept']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="h-20px"></div>
      <!-- {{ searchParams.page_no }} --- {{ searchParams.page_size }} -->
      <!-- 分页 -->
      <el-pagination
        background
        v-model:current-page="searchParams.page_no"
        v-model:page-size="searchParams.page_size"
        v-show="total > 0"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleListPage"
        @current-change="handleListPage"
      />

      <!-- 添加 OR 修改 -->
      <KoiDialog
        ref="koiDialogRef"
        :title="title"
        @koiConfirm="handleConfirm"
        @koiCancel="handleCancel"
        :loading="confirmLoading"
      >
        <template #content>
          <el-form ref="formRef" :rules="rules" :model="form" label-width="80px" status-icon>
            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                <el-form-item label="角色名称" prop="role_name">
                  <el-input v-model="form.role_name" placeholder="请输入角色名称" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" class="p-l-10px">
                <el-form-item label="角色编号" prop="role_code">
                  <el-input v-model="form.role_code" placeholder="请输入角色编号" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }">
                <el-form-item label="角色状态" prop="status">
                  <el-select v-model="form.status" placeholder="请选择角色状态" style="width: 260px" clearable>
                    <el-option label="启用" value="1" />
                    <el-option label="停用" value="0" />
                  </el-select>
                </el-form-item>
                <el-form-item label="数据范围" prop="data_scope">
                  <el-select v-model="form.data_scope" placeholder="请选择数据范围" style="width: 260px" clearable>
                    <el-option label="全部数据权限" :value="1" />
                    <el-option label="自定义数据权限" :value="2" />
                    <el-option label="本部门数据权限" :value="3" />
                    <el-option label="本部门及以下数据权限" :value="4" />
                    <el-option label="仅本人数据权限" :value="5" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" class="p-l-10px">
                <el-form-item label="角色排序" prop="sorted">
                  <el-input-number v-model="form.sorted" style="width: 260px" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="角色备注" prop="remark">
                  <el-input v-model="form.remark" :rows="5" type="textarea" placeholder="请输入角色备注" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <!-- {{ form }} -->
        </template>
      </KoiDialog>

      <!-- 分配菜单 -->
      <KoiDrawer
        ref="koiMenuDrawerRef"
        :title="title"
        size="360"
        @koiConfirm="handleMenuConfirm"
        @koiCancel="handleMenuCancel"
        :loading="confirmLoading"
        cancelText="关闭"
      >
        <template #content>
          <div>
            <el-tree
              ref="menuTreeRef"
              :data="treeData"
              show-checkbox
              :default-expand-all="false"
              :default-expanded-keys="expandedKey"
              node-key="id"
              highlight-current
              :props="defaultMenuProps"
            />
          </div>
        </template>
      </KoiDrawer>

      <!-- 分配部门权限数据 -->
      <KoiDrawer
        ref="koiDeptDrawerRef"
        :title="title"
        size="360"
        @koiConfirm="handleDeptConfirm"
        @koiCancel="handleDeptCancel"
        :loading="confirmLoading"
        cancelText="关闭"
      >
        <template #content>
          <div>
            <el-tree
              ref="deptTreeRef"
              :data="treeData"
              show-checkbox
              :default-expand-all="false"
              :default-expanded-keys="expandedKey"
              node-key="id"
              highlight-current
              :props="defaultDeptProps"
            />
          </div>
        </template>
      </KoiDrawer>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="rolePage">
import { nextTick, ref, reactive, onMounted } from "vue";
import { koiNoticeSuccess, koiNoticeError, koiMsgError, koiMsgWarning, koiMsgBox, koiMsgInfo } from "@/utils/koi.ts";
import { listPage, getById, add, update, deleteById, batchDelete, updateStatus, getSorted } from "@/api/system/role/index.ts";
import { listMenuNormal, listMenuIdsByRoleId, saveRoleMenu } from "@/api/system/menu/index.ts";
import { listDeptNormal, listDeptIdsByRoleId, saveRoleDept } from "@/api/system/dept/index.ts";
import { handleTree } from "@/utils/index.ts";

// 数据表格加载页面动画
const loading = ref(false);
/** 是否显示搜索表单 */
const showSearch = ref<boolean>(true); // 默认显示搜索条件
// 数据表格数据
const tableList = ref<any>([]);

// 查询参数
const searchParams = ref({
  page_no: 1, // 第几页
  page_size: 10, // 每页显示多少条
  role_name: "",
  role_code: "",
  status: "",
  data_scope: ""
});
const total = ref<number>(0);
// 重置搜索参数
const resetSearchParams = () => {
  searchParams.value = {
    page_no: 1, // 第几页
    page_size: 10, // 每页显示多少条
    role_name: "",
    role_code: "",
    status: "",
    data_scope: ""
  };
};
/** 搜索 */
const handleSearch = () => {
  console.log("搜索");
  searchParams.value.page_no = 1;
  handleListPage();
};
/** 重置 */
const resetSearch = () => {
  console.log("重置搜索");
  resetSearchParams();
  handleListPage();
};

/** @current-change：点击分页组件页码发生变化：例如：切换第2、3页 OR 上一页 AND 下一页 OR 跳转某一页 */
/** @size-change：点击分页组件下拉选中条数发生变化：例如：选择10条/页、20条/页等 */
// 分页查询，@current-change AND @size-change都会触发分页，调用后端分页接口
/** 数据表格 */
const handleListPage = async () => {
  try {
    loading.value = true;
    tableList.value = []; // 重置表格数据
    const res: any = await listPage(searchParams.value);
    console.log("角色数据表格数据->", res.data);
    tableList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

/** 数据表格[删除、批量删除等刷新使用] */
const handleTableData = async () => {
  try {
    const res: any = await listPage(searchParams.value);
    console.log("角色数据表格数据->", res.data);
    tableList.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

onMounted(() => {
  // 获取数据表格数据
  handleListPage();
});
const ids = ref([]); // 选中数组
const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
/** 是否多选 */
const handleSelectionChange = (selection: any) => {
  console.log(selection);
  ids.value = selection.map((item: any) => item.id);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
};

// 获取最新排序数字
const handleSorted = async () => {
  try {
    const res: any = await getSorted();
    form.value.sorted = res.data;
  } catch (error) {
    console.log(error);
    koiMsgError("数据查询失败，请重试🌻");
  }
};

/** 添加 */
const handleAdd = () => {
  // 打开弹出框
  koiDialogRef.value.koiOpen();
  koiNoticeSuccess("添加🌻");
  // 重置表单
  resetForm();
  // 标题
  title.value = "角色添加";
  form.value.status = "1";
  handleSorted();
};

/** 回显数据 */
const handleEcho = async (id: any) => {
  if (id == null || id == "") {
    koiMsgWarning("请选择需要修改的数据🌻");
    return;
  }
  try {
    const res: any = await getById(id);
    console.log(res.data);
    form.value = res.data;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据获取失败，请刷新重试🌻");
  }
};

/** 修改 */
const handleUpdate = async (row?: any) => {
  // 打开弹出框
  koiDialogRef.value.koiOpen();
  koiNoticeSuccess("修改🌻");
  // 重置表单
  resetForm();
  // 标题
  title.value = "角色修改";
  const id = row ? row.id : ids.value[0];
  if (id == null || id == "") {
    koiMsgError("请选择需要修改的数据🌻");
  }
  console.log(id);
  // 回显数据
  handleEcho(id);
};

/** 添加 AND 修改弹出框 */
const koiDialogRef = ref();
// 标题
const title = ref("角色管理");
// form表单Ref
const formRef = ref<any>();
// form表单
let form = ref<any>({
  id: "",
  role_name: "",
  role_code: "",
  status: "",
  sorted: 1,
  data_scope: "",
  remark: ""
});
/** 清空表单数据 */
const resetForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (formRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      formRef.value.resetFields();
    }
  });
  form.value = {
    id: null,
    role_name: null,
    role_code: null,
    status: null,
    sorted: 1,
    data_scope: null,
    remark: null
  };
};
/** 表单规则 */
const rules = reactive({
  role_name: [{ required: true, message: "请输入角色名字", trigger: "blur" }],
  role_code: [{ required: true, message: "请输入角色编号", trigger: "blur" }],
  status: [{ required: true, message: "请输入选择角色状态", trigger: "blur" }],
  sorted: [{ required: true, message: "请输入排序号", trigger: "blur" }],
  data_scope: [{ required: true, message: "请选择数据范围", trigger: "change" }]
});

// 确定按钮是否显示loading
const confirmLoading = ref(false);
/** 确定  */
const handleConfirm = () => {
  if (!formRef.value) return;
  confirmLoading.value = true;
  (formRef.value as any).validate(async (valid: any) => {
    if (valid) {
      console.log("表单ID", form.value.id);
      if (form.value.id != null && form.value.id != "") {
        try {
          await update(form.value);
          koiNoticeSuccess("修改成功🌻");
          confirmLoading.value = false;
          koiDialogRef.value.koiQuickClose();
          resetForm();
          handleListPage();
        } catch (error) {
          console.log(error);
          confirmLoading.value = false;
          koiNoticeError("修改失败，请刷新重试🌻");
        }
      } else {
        try {
          await add(form.value);
          koiNoticeSuccess("添加成功🌻");
          confirmLoading.value = false;
          koiDialogRef.value.koiQuickClose();
          resetForm();
          handleListPage();
        } catch (error) {
          console.log(error);
          confirmLoading.value = false;
          koiNoticeError("添加失败，请刷新重试🌻");
        }
      }
    } else {
      koiMsgError("验证失败，请检查填写内容🌻");
      confirmLoading.value = false;
    }
  });
};

/** 取消 */
const handleCancel = () => {
  koiDialogRef.value.koiClose();
};

/** 状态switch */
const handleSwitch = (row: any) => {
  let text = row.status === "1" ? "启用" : "停用";
  koiMsgBox("确认要[" + text + "]-[" + row.role_name + "]角色吗？")
    .then(async () => {
      if (!row.id || !row.status) {
        row.status = row.status == "1" ? "0" : "1";
        koiMsgWarning("请选择需要修改的数据🌻");
        return;
      }
      try {
        await updateStatus(row.id, row.status);
        koiNoticeSuccess("修改成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("修改失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      row.status = row.status == "1" ? "0" : "1";
      koiMsgError("已取消🌻");
    });
};

/** 删除 */
const handleDelete = (row: any) => {
  const id = row.id;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认需要删除角色名称[" + row.role_name + "]么？")
    .then(async () => {
      try {
        await deleteById(id);
        handleTableData();
        koiNoticeSuccess("删除成功🌻");
      } catch (error) {
        console.log(error);
        koiNoticeError("删除失败，请刷新重试🌻");
        handleTableData();
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 批量删除 */
const handleBatchDelete = () => {
  if (ids.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认需要进行批量删除么？")
    .then(async () => {
      try {
        // console.log("ids",ids.value)
        await batchDelete(ids.value);
        handleTableData();
        koiNoticeSuccess("批量删除成功🌻");
      } catch (error) {
        console.log(error);
        koiNoticeError("批量删除失败，请刷新重试🌻");
        handleTableData();
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 分配菜单开始 */
const koiMenuDrawerRef = ref();
const menuTreeRef = ref();
const treeData = ref<any>([]);

// 配置属性
const defaultMenuProps = {
  id: "id",
  label: "menu_name",
  children: "children"
};
// 默认展开配置
const expandedKey = ref();
const id = ref();
/** 分配菜单 */
const handleAssignMenu = async (row?: any) => {
  console.log("handleAssignMenu", row);
  title.value = "分配菜单🌻";
  // 置空
  menuTreeRef.value?.setCheckedKeys([], false);
  expandedKey.value = [];
  treeData.value = [];
  id.value = row?.id || ids.value[0];
  if (id.value == null || id.value == "") {
    koiMsgWarning("请选中需要分配菜单的数据🌻");
    return;
  }
  // 查询所有的菜单权限
  koiMenuDrawerRef.value.koiOpen();
  // console.log("角色ID",ids.value[0]);
  // 查询所有的菜单权限
  try {
    const res: any = await listMenuNormal();
    console.log("listMenuNormal", res.data);
    treeData.value = handleTree(res.data.menu_list, "id");
    expandedKey.value = res.data.spread_list;
  } catch (error) {
    console.log(error);
    koiMsgError("菜单资源加载失败🌻");
  }

  // 通过key设置反选角色拥有的菜单权限(只能查询子节点，查询父节点将直接选中全部下的子节点)
  try {
    const res: any = await listMenuIdsByRoleId(id.value);
    console.log("listMenuIdsByRoleId", res.data);
    // menuTreeRef.value?.setCheckedKeys([1,2,3], false);
    if (res.data) {
      // menuTreeRef.value?.setCheckedKeys(res.data, false);
      const leafNodes = filterLeafNodes(treeData.value, res.data);
      menuTreeRef.value?.setCheckedKeys(leafNodes, false);
    }
  } catch (error) {
    console.log(error);
    koiMsgError("角色菜单资源加载失败🌻");
  }
};

// 保存
const handleMenuConfirm = async () => {
  confirmLoading.value = true;
  // 获取选中的keys
  const checkedKeys = menuTreeRef.value?.getCheckedKeys(false);
  // console.log("选中",checkedKeys)
  // 获取半选的keys(即保存选中子节点的父节点[父节点下的子节点并没有选中完])
  const halfCheckKeys = menuTreeRef.value?.getHalfCheckedKeys();
  // console.log("半选",halfCheckKeys)
  // 组合成最后的keys
  const finalKey = halfCheckKeys.concat(checkedKeys);
  // console.log("最终",finalKey)

  try {
    await saveRoleMenu(id.value, finalKey);
    confirmLoading.value = false;
    koiMenuDrawerRef.value.koiQuickClose();
    koiNoticeSuccess("角色菜单保存成功🌻");
  } catch (error) {
    console.log(error);
    koiMsgError("角色菜单保存失败🌻");
  }
};

// 取消
const handleMenuCancel = () => {
  koiMenuDrawerRef.value.koiClose();
};
/** 分配菜单结束 */

/** 分配部门权限数据开始 */
const koiDeptDrawerRef = ref();
const deptTreeRef = ref();

// 配置属性
const defaultDeptProps = {
  id: "id",
  label: "dept_name",
  children: "children"
};

/**
 * 过滤出叶子节点（子节点）
 * @param treeData 树形数据
 * @param selectedNodeIds 选中的节点ID数组
 * @returns 过滤后的叶子节点ID数组
 */
const filterLeafNodes = (treeData: any[], selectedNodeIds: any[]) => {
  // 在树形数据中查找对应节点
  const findNode = (nodes: any[], id: any): any => {
    for (const node of nodes) {
      if (node.id === id) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = findNode(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };
  
  return selectedNodeIds.filter((nodeId: any) => {
    const node = findNode(treeData, nodeId);
    // 如果节点不存在或者没有子节点，则认为是叶子节点
    return !node || !node.children || node.children.length === 0;
  });
};
/** 分配部门 */
const handleAssignDept = async (row?: any) => {
  // console.log(row.id);
  title.value = "分配部门🌻";
  // 置空
  deptTreeRef.value?.setCheckedKeys([], false);
  expandedKey.value = [];
  treeData.value = [];
  id.value = row?.id || ids.value[0];
  if (id.value == null || id.value == "") {
    koiMsgWarning("请选中需要分配部门的数据🌻");
    return;
  }
  koiDeptDrawerRef.value.koiOpen();
  // console.log("角色ID",ids.value[0]);
  // 查询所有的部门权限
  try {
    const res: any = await listDeptNormal();
    treeData.value = handleTree(res.data.dept_list, "id");
    expandedKey.value = res.data.spread_list;
  } catch (error) {
    console.log(error);
    koiMsgError("部门资源加载失败🌻");
  }

  // deptTreeRef.value?.setCheckedKeys(['3'], false);

  // 通过key设置反选角色拥有的菜单权限(只能查询子节点，查询父节点将直接选中全部下的子节点)
  try {
    const res: any = await listDeptIdsByRoleId(id.value);
    
    console.log("listDeptIdsByRoleId", res.data);
    if (res.data) {
      // 使用通用方法过滤出叶子节点
      const leafNodes = filterLeafNodes(treeData.value, res.data);
      deptTreeRef.value?.setCheckedKeys(leafNodes, false);
    }
  } catch (error) {
    console.log(error);
    koiMsgError("角色部门资源加载失败🌻");
  }
};
// 确认
const handleDeptConfirm = async () => {
  confirmLoading.value = true;
  // 获取选中的keys
  const checkedKeys = deptTreeRef.value?.getCheckedKeys(false);
  // console.log("选中",checkedKeys)
  // 获取半选的keys(即保存选中子节点的父节点[父节点下的子节点并没有选中完])
  const halfCheckKeys = deptTreeRef.value?.getHalfCheckedKeys();
  // console.log("半选",halfCheckKeys)
  // 组合成最后的keys
  const finalKey = halfCheckKeys.concat(checkedKeys);
  // console.log("最终",finalKey)

  try {
    await saveRoleDept(id.value, finalKey);
    confirmLoading.value = false;
    koiDeptDrawerRef.value.koiQuickClose();
    koiNoticeSuccess("角色部门保存成功🌻");
    // 刷新页面菜单信息
    window.location.reload;
  } catch (error) {
    console.log(error);
    koiMsgError("角色部门保存失败🌻");
  }
};
// 取消
const handleDeptCancel = () => {
  koiDeptDrawerRef.value.koiClose();
};
/** 分配部门权限数据结束 */
</script>

<style lang="scss" scoped></style>
