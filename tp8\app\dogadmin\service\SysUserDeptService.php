<?php
namespace app\dogadmin\service;

use app\dogadmin\model\SysUserDeptModel;

class SysUserDeptService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysUserDeptModel();
    }
    
    /**
     * 根据用户ID获取部门ID
     * @param int $userId 用户ID
     * @return int|null 部门ID
     */
    public function getDeptIdByUserId($userId)
    {
        if (empty($userId)) {
            return null;
        }
        $result = $this->model->where('user_id', $userId)->find();
        return $result ? $result['dept_id'] : null;
    }
}