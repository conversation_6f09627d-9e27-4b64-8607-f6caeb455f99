<?php
declare(strict_types=1);

namespace app\lib\service\storage;

use app\lib\exception\dogadmin\FileException;
use think\File;
use app\lib\service\storage\CloudStorageService;

/**
 * 腾讯云COS存储服务
 */
class TencentCosStorageService extends CloudStorageService
{
    /**
     * COS客户端
     * @var mixed
     */
    private $client;
    
    /**
     * 存储桶名
     * @var string
     */
    private string $bucket = '';
    
    /**
     * 地域
     * @var string
     */
    private string $region = '';
    
    /**
     * 域名
     * @var string
     */
    private string $domain = '';
    
    /**
     * 初始化腾讯云COS配置
     */
    protected function init(): void
    {
        $this->storageType = 'tencent_cos';
        
        if (empty($this->config['secretId']) || empty($this->config['secretKey']) || 
            empty($this->config['bucket']) || empty($this->config['region'])) {
            throw new FileException(['message' => '腾讯云COS配置信息不完整'], 4); // 4 = 配置错误
        }
        
        $this->bucket = $this->config['bucket'];
        $this->region = $this->config['region'];
        $this->domain = $this->config['domain'] ?? '';
        
        // 检查是否安装了腾讯云SDK
        if (!class_exists('\\Qcloud\\Cos\\Client')) {
            throw new FileException(['message' => '请先安装腾讯云COS SDK: composer require qcloud/cos-sdk-v5'], 4); // 4 = 配置错误
        }
        
        try {
            $this->client = new \Qcloud\Cos\Client([
                'region' => $this->region,
                'schema' => 'https',
                'credentials' => [
                    'secretId' => $this->config['secretId'],
                    'secretKey' => $this->config['secretKey']
                ]
            ]);
            
        } catch (\Exception $e) {
            throw new FileException(['message' => '腾讯云COS初始化失败: ' . $e->getMessage()], 4); // 4 = 配置错误
        }
    }
    
    /**
     * 上传文件到腾讯云COS
     * 
     * @param File|string $file 文件对象或本地路径
     * @param string $path 上传路径
     * @param string $fileName 文件名
     * @param string|bool $nameMode 文件名模式：
     *                             - 字符串: 使用自定义文件名（保留扩展名）
     *                             - true: 使用原始文件名
     *                             - false: 生成唯一文件名（默认）
     * @return array
     */
    public function upload($file, string $path = '', string $fileName = '', $nameMode = false): array
    {
        try {
            $info = $this->parseFileInfo($file);
            $this->validateFile($file, $this->config['rules'] ?? []);
            if (empty($fileName)) {
                $fileName = $this->generateFileName($info['originalName'], $path, $nameMode);
            } else {
                $fileName = $path . $fileName;
            }
            $result = $this->client->putObject([
                'Bucket' => $this->bucket,
                'Key' => $fileName,
                'Body' => fopen($info['realPath'], 'rb'),
                'ContentType' => $info['mime']
            ]);
            return [
                'success' => true,
                'key' => $fileName,
                'etag' => trim($result['ETag'], '"'),
                'url' => $this->getUrl($fileName),
                'size' => $info['size'],
                'originalName' => $info['originalName'],
                'extension' => $info['extension'],
                'mimeType' => $info['mime'],
                'storageType' => $this->storageType
            ];
        } catch (FileException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '腾讯云COS上传失败: ' . $e->getMessage(),
                'file' => $info['originalName'] ?? ''
            ], 5);
        }
    }
    
    /**
     * 删除文件
     * 
     * @param string $key 文件key
     * @return bool
     */
    public function delete(string $key): bool
    {
        try {
            $this->client->deleteObject([
                'Bucket' => $this->bucket,
                'Key' => $key
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '腾讯云COS删除失败: ' . $e->getMessage(),
                'key' => $key
            ], 8); // 8 = 文件删除失败
        }
    }
    
    /**
     * 获取文件URL
     * 
     * @param string $key 文件key
     * @return string
     */
    public function getUrl(string $key): string
    {
        if (!empty($this->domain)) {
            return rtrim($this->domain, '/') . '/' . ltrim($key, '/');
        }
        
        // 使用默认域名
        return sprintf('https://%s.cos.%s.myqcloud.com/%s', 
            $this->bucket, 
            $this->region, 
            ltrim($key, '/')
        );
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param string $key 文件key
     * @return bool
     */
    public function exists(string $key): bool
    {
        try {
            $this->client->headObject([
                'Bucket' => $this->bucket,
                'Key' => $key
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取预签名URL
     * 
     * @param string $key 文件key
     * @param int $expires 过期时间（秒）
     * @return string
     */
    public function getPresignedUrl(string $key, int $expires = 3600): string
    {
        try {
            $signedUrl = $this->client->getPresignedUrl(
                'GetObject',
                [
                    'Bucket' => $this->bucket,
                    'Key' => $key
                ],
                '+' . $expires . ' seconds'
            );
            
            return $signedUrl;
            
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '获取预签名URL失败: ' . $e->getMessage(),
                'key' => $key
            ], 4); // 4 = 配置错误
        }
    }
    
    // validateFile方法兼容本地路径
    protected function validateFile($file, array $rules = []): void
    {
        $info = $this->parseFileInfo($file);
        if (isset($rules['maxSize']) && $info['size'] > $rules['maxSize']) {
            throw new FileException([
                'file' => $info['originalName'],
                'size' => $info['size'],
                'maxSize' => $rules['maxSize']
            ], 1);
        }
        if (isset($rules['allowedTypes']) && !in_array($info['extension'], $rules['allowedTypes'])) {
            throw new FileException([
                'file' => $info['originalName'],
                'type' => $info['extension'],
                'allowedTypes' => $rules['allowedTypes']
            ], 3);
        }
    }
}