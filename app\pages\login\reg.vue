<template>
	<view class="container">
		<view class="tui-page-title">注册</view>
		<tui-form ref="form" class="tui-form">
			<view class="tui-view-input">
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="mobile" color="#6d7a87" :size="20"></tui-icon>
						<input :value="phone" placeholder="请输入手机号" placeholder-class="tui-phcolor" type="number"
							maxlength="11" @input="inputphone" />
						<view class="tui-icon-close" v-show="phone" @tap="clearInput(1)">
							<tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon>
						</view>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="shield" color="#6d7a87" :size="20"></tui-icon>
						<input placeholder="请输入验证码" placeholder-class="tui-phcolor" type="text" maxlength="6"
							@input="inputCode" />
						<tui-countdown-verify :successVal="successSend" :resetVal="resetSend" @send="getCode" :params="1"></tui-countdown-verify>
						<!-- <view class="tui-btn-send" v-else :class="{ 'tui-gray': isSend }"
							:hover-class="isSend ? '' : 'tui-opcity'" :hover-stay-time="150" @click="sendSms">
							{{ btnSendText }}</view> -->
						
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="pwd" color="#6d7a87" :size="20"></tui-icon>
						<input :value="password" placeholder="请输入密码" :password="true" placeholder-class="tui-phcolor"
							type="text" maxlength="40" @input="inputPwd" />
						<view class="tui-icon-close" v-show="password" @tap="clearInput(2)">
							<tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon>
						</view>
					</view>
				</tui-list-cell>
			</view>
			<view class="tui-btn-box">
				<tui-button type="blue" :disabledGray="true" :disabled="disabled" :shadow="true" shape="circle" @click="register">注册</tui-button>
			</view>
			<view class="tui-cell-text">
				注册代表同意
				<view class="tui-color-primary" hover-class="tui-opcity" :hover-stay-time="150">
					用户服务协议、隐私政策</view>
			</view>
		</tui-form>
	</view>
</template>

<script>
	import { sendSms, register } from '@/api/login.js';
	import { rule_register } from '@/config/rules.js';
	import { baseURL } from '@/api/config.js';
	import { mapActions } from 'vuex';
	import { checkPhone } from '@/utils/common.js'
	export default {
		computed: {
			disabled() {
				let bool = true;
				if (this.phone && this.code && this.password) {
					bool = false;
				}
				return bool;
			}
		},
		data() {
			return {
				phone: '',
				password: '',
				code: '',
				isSend: false,
				successSend: 0,
				resetSend:0
			};
		},
		methods: {
			...mapActions({
				getUser: 'getUserInfo'
			}),
			back() {
				uni.navigateBack();
			},
			inputCode(e) {
				this.code = e.detail.value;
			},
			inputphone(e) {
				this.phone = e.detail.value;
			},
			inputPwd(e) {
				this.password = e.detail.value;
			},
			clearInput(type) {
				if (type == 1) {
					this.phone = '';
				} else {
					this.password = '';
				}
			},
			getCode() {
				let check = checkPhone(this.phone);
				if (check) {
					let data = {
						phone: this.phone,
						type:'register'
					}
					sendSms(data).then(res=>{
						if (res.code == 0) {
							uni.showToast({
								title: '验证码已发送',
								icon: 'none'
							})
							this.successSend += 1;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							this.resetSend += 1;
						}
					})
				} else {
					uni.showToast({
						title: '手机格式不正确',
						icon: 'none'
					})
				}
			},
			register(){
				let data = {
					phone: this.phone,
					password: this.password,
					code: this.code,
				}
				register(data).then(res=>{
					if (res.code == 0) {
						uni.setStorageSync('SMLY_TOKEN', res.data);
						uni.showToast({
							title: '注册成功！',
							icon: "none"
						});
						this.getUser().then(res => {
							uni.reLaunch({
								url: uni.getStorageSync('beforePageFullPath') ||
									'/pages/index/index'
							})
						}).catch(err => {
							uni.showToast({
								title: err.msg,
								icon: "none"
							});
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		.tui-page-title {
			width: 100%;
			font-size: 48rpx;
			font-weight: bold;
			color: $uni-text-color;
			line-height: 42rpx;
			padding: 110rpx 40rpx 40rpx 40rpx;
			box-sizing: border-box;
		}

		.tui-form {
			padding-top: 50rpx;

			.tui-view-input {
				width: 100%;
				box-sizing: border-box;
				padding: 0 40rpx;

				.tui-cell-input {
					width: 100%;
					display: flex;
					align-items: center;
					padding-top: 48rpx;
					padding-bottom: $uni-spacing-col-base;
					border-bottom: 1px solid $uni-text-color-disable;

					input {
						flex: 1;
						padding-left: $uni-spacing-row-base;
					}

					.tui-icon-close {
						margin-left: auto;
					}

					.tui-btn-send {
						width: 156rpx;
						text-align: right;
						flex-shrink: 0;
						font-size: $uni-font-size-base;
						color: $uni-color-primary;
					}

					.tui-gray {
						color: $uni-text-color-placeholder;
					}
				}
			}

			.tui-cell-text {
				width: 100%;
				padding: 40rpx $uni-spacing-row-lg;
				box-sizing: border-box;
				font-size: $uni-font-size-sm;
				color: $uni-text-color-grey;
				display: flex;
				align-items: center;

				.tui-color-primary {
					color: $uni-color-primary;
					padding-left: $uni-spacing-row-sm;
				}
			}

			.tui-btn-box {
				width: 100%;
				padding: 0 $uni-spacing-row-lg;
				box-sizing: border-box;
				margin-top: 80rpx;
			}
		}
	}
</style>
