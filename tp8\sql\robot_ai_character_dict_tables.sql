/*
 AI角色通用字典表设计
 基于 robot_ai_character 表的字段设计通用字典表
 Date: 16/08/2025
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- AI角色通用字典表
-- ----------------------------
DROP TABLE IF EXISTS `robot_ai_character_dict`;
CREATE TABLE `robot_ai_character_dict` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dict_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型',
  `dict_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
  `dict_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典值（可存储JSON等复杂数据）',
  `sorted` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态标识',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI角色通用字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 字典数据插入
-- ----------------------------

-- 性别字典 (enum类型)
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `dict_value`, `sorted`) VALUES
('gender', '男性', 'male', 1),
('gender', '女性', 'female', 2),
('gender', '其他', 'other', 3);

-- 职业字典 (varchar类型，dict_name和dict_value一致)
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `dict_value`, `sorted`) VALUES
('occupation', '教师', '教师', 1),
('occupation', '医生', '医生', 2),
('occupation', '工程师', '工程师', 3),
('occupation', '设计师', '设计师', 4),
('occupation', '学生', '学生', 5),
('occupation', '企业家', '企业家', 6),
('occupation', '艺术家', '艺术家', 7),
('occupation', '作家', '作家', 8),
('occupation', '律师', '律师', 9),
('occupation', '护士', '护士', 10),
('occupation', '警察', '警察', 11),
('occupation', '消防员', '消防员', 12),
('occupation', '厨师', '厨师', 13),
('occupation', '司机', '司机', 14),
('occupation', '销售员', '销售员', 15),
('occupation', '会计师', '会计师', 16),
('occupation', '程序员', '程序员', 17),
('occupation', '记者', '记者', 18),
('occupation', '摄影师', '摄影师', 19),
('occupation', '音乐家', '音乐家', 20),
('occupation', '演员', '演员', 21),
('occupation', '主播', '主播', 22),
('occupation', '网红', '网红', 23),
('occupation', '自由职业者', '自由职业者', 24),
('occupation', '退休人员', '退休人员', 25),
('occupation', '家庭主妇/主夫', '家庭主妇/主夫', 26),
('occupation', '无业游民', '无业游民', 27),
('occupation', '公务员', '公务员', 28),
('occupation', '军人', '军人', 29),
('occupation', '农民', '农民', 30),
('occupation', '工人', '工人', 31),
('occupation', '服务员', '服务员', 32),
('occupation', '快递员', '快递员', 33),
('occupation', '保安', '保安', 34),
('occupation', '清洁工', '清洁工', 35);

-- 性格字典 (text类型，dict_name和dict_value一致)
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `dict_value`, `sorted`) VALUES
('personality', '开朗活泼', '开朗活泼', 1),
('personality', '沉稳内敛', '沉稳内敛', 2),
('personality', '幽默风趣', '幽默风趣', 3),
('personality', '知识渊博', '知识渊博', 4),
('personality', '浪漫感性', '浪漫感性', 5),
('personality', '温柔体贴', '温柔体贴', 6),
('personality', '热情奔放', '热情奔放', 7),
('personality', '冷静理性', '冷静理性', 8),
('personality', '乐观积极', '乐观积极', 9),
('personality', '悲观消极', '悲观消极', 10),
('personality', '自信张扬', '自信张扬', 11),
('personality', '谦逊低调', '谦逊低调', 12),
('personality', '急躁易怒', '急躁易怒', 13),
('personality', '温和平静', '温和平静', 14),
('personality', '固执己见', '固执己见', 15),
('personality', '随和包容', '随和包容', 16),
('personality', '完美主义', '完美主义', 17),
('personality', '随性自由', '随性自由', 18),
('personality', '严肃认真', '严肃认真', 19),
('personality', '轻松随意', '轻松随意', 20),
('personality', '敏感细腻', '敏感细腻', 21),
('personality', '粗心大意', '粗心大意', 22),
('personality', '勤奋努力', '勤奋努力', 23),
('personality', '懒散拖延', '懒散拖延', 24),
('personality', '社交达人', '社交达人', 25),
('personality', '社恐宅男/女', '社恐宅男/女', 26),
('personality', '爱冒险', '爱冒险', 27),
('personality', '求稳保守', '求稳保守', 28),
('personality', '毒舌刻薄', '毒舌刻薄', 29),
('personality', '善良纯真', '善良纯真', 30);

-- 说话风格字典 (text类型，dict_name和dict_value一致)
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `dict_value`, `sorted`) VALUES
('speaking_style', '正式严谨', '正式严谨', 1),
('speaking_style', '轻松随意', '轻松随意', 2),
('speaking_style', '可爱萌系', '可爱萌系', 3),
('speaking_style', '专业权威', '专业权威', 4),
('speaking_style', '文艺诗意', '文艺诗意', 5),
('speaking_style', '幽默搞笑', '幽默搞笑', 6),
('speaking_style', '温柔甜美', '温柔甜美', 7),
('speaking_style', '霸道总裁', '霸道总裁', 8),
('speaking_style', '毒舌犀利', '毒舌犀利', 9),
('speaking_style', '呆萌天然', '呆萌天然', 10),
('speaking_style', '高冷傲娇', '高冷傲娇', 11),
('speaking_style', '热情洋溢', '热情洋溢', 12),
('speaking_style', '佛系淡然', '佛系淡然', 13),
('speaking_style', '网络用语', '网络用语', 14),
('speaking_style', '方言土话', '方言土话', 15),
('speaking_style', '古风雅致', '古风雅致', 16),
('speaking_style', '二次元宅', '二次元宅', 17),
('speaking_style', '商务精英', '商务精英', 18),
('speaking_style', '学者教授', '学者教授', 19),
('speaking_style', '街头痞子', '街头痞子', 20),
('speaking_style', '小清新', '小清新', 21),
('speaking_style', '暴躁老哥', '暴躁老哥', 22),
('speaking_style', '温婉淑女', '温婉淑女', 23),
('speaking_style', '直男钢铁', '直男钢铁', 24),
('speaking_style', '撒娇卖萌', '撒娇卖萌', 25);

-- 口头禅字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `dict_value`, `sorted`) VALUES
('catchphrases', '太棒了！', '太棒了！', 1),
('catchphrases', '有意思~', '有意思~', 2),
('catchphrases', '说得对！', '说得对！', 3),
('catchphrases', '让我想想...', '让我想想...', 4),
('catchphrases', '哇哦！', '哇哦！', 5),
('catchphrases', '真的吗？', '真的吗？', 6),
('catchphrases', '不可能吧！', '不可能吧！', 7),
('catchphrases', '我去！', '我去！', 8),
('catchphrases', '牛逼！', '牛逼！', 9),
('catchphrases', '绝了！', '绝了！', 10),
('catchphrases', '哈哈哈', '哈哈哈', 11),
('catchphrases', 'emmm...', 'emmm...', 12),
('catchphrases', '好吧', '好吧', 13),
('catchphrases', '算了', '算了', 14),
('catchphrases', '无语了', '无语了', 15),
('catchphrases', '6666', '6666', 16),
('catchphrases', 'OMG', 'OMG', 17),
('catchphrases', '我的天', '我的天', 18),
('catchphrases', '真香！', '真香！', 19),
('catchphrases', '社死了', '社死了', 20),
('catchphrases', '破防了', '破防了', 21),
('catchphrases', '绷不住了', '绷不住了', 22),
('catchphrases', '爷青回', '爷青回', 23),
('catchphrases', 'YYDS', 'YYDS', 24),
('catchphrases', '芜湖~', '芜湖~', 25),
('catchphrases', '淦！', '淦！', 26),
('catchphrases', '卧槽', '卧槽', 27),
('catchphrases', '我佛了', '我佛了', 28),
('catchphrases', '麻了', '麻了', 29),
('catchphrases', '服了', '服了', 30),
('catchphrases', '确实', '确实', 31),
('catchphrases', '就这？', '就这？', 32),
('catchphrases', '不愧是你', '不愧是你', 33),
('catchphrases', '你懂的', '你懂的', 34),
('catchphrases', '没毛病', '没毛病', 35),
('catchphrases', '可以的', '可以的', 36),
('catchphrases', '不错不错', '不错不错', 37),
('catchphrases', '厉害了', '厉害了', 38),
('catchphrases', '佩服佩服', '佩服佩服', 39),
('catchphrases', '学到了', '学到了', 40);

-- 禁用词汇字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('forbidden_words', '政治敏感词', 1),
('forbidden_words', '暴力词汇', 2),
('forbidden_words', '脏话粗口', 3);

-- Emoji偏好字典 (enum类型)
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `dict_value`, `sorted`) VALUES
('emoji_preference', '高频使用', 'high', 1),
('emoji_preference', '适度使用', 'medium', 2),
('emoji_preference', '偶尔使用', 'low', 3),
('emoji_preference', '不使用', 'none', 4);

-- 响应速度字典 (enum类型)
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `dict_value`, `sorted`) VALUES
('response_speed', '快速响应', 'fast', 1),
('response_speed', '正常响应', 'normal', 2),
('response_speed', '慢速响应', 'slow', 3);

-- 活跃时间字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('active_time', '全天', 1),
('active_time', '工作时间', 2),
('active_time', '晚间时段', 3),
('active_time', '早间时段', 4),
('active_time', '下午时段', 5),
('active_time', '周末', 6);

-- 语言偏好字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('language_preference', '简体中文', 1),
('language_preference', '繁体中文', 2),
('language_preference', '英语', 3),
('language_preference', '日语', 4),
('language_preference', '韩语', 5);

-- 启用状态字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('is_active', '启用', 1),
('is_active', '禁用', 2);

SET FOREIGN_KEY_CHECKS = 1;
