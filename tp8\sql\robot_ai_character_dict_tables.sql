/*
 AI角色字典表设计
 基于 robot_ai_character 表的字段设计相应的字典表
 Date: 16/08/2025
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 性别字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_gender`;
CREATE TABLE `dict_gender` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '性别代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '性别名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '性别字典表' ROW_FORMAT = Dynamic;

-- 性别字典数据
INSERT INTO `dict_gender` (`code`, `name`, `description`, `sort_order`) VALUES
('male', '男性', '男性角色', 1),
('female', '女性', '女性角色', 2),
('other', '其他', '其他性别或不明确', 3);

-- ----------------------------
-- 职业字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_occupation`;
CREATE TABLE `dict_occupation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职业代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职业名称',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职业分类',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职业描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '职业字典表' ROW_FORMAT = Dynamic;

-- 职业字典数据
INSERT INTO `dict_occupation` (`code`, `name`, `category`, `description`, `sort_order`) VALUES
('teacher', '教师', '教育', '从事教育工作的专业人员', 1),
('doctor', '医生', '医疗', '从事医疗工作的专业人员', 2),
('engineer', '工程师', '技术', '从事工程技术工作的专业人员', 3),
('designer', '设计师', '创意', '从事设计工作的专业人员', 4),
('student', '学生', '教育', '在校学习的人员', 5),
('entrepreneur', '企业家', '商业', '创业或经营企业的人员', 6),
('artist', '艺术家', '艺术', '从事艺术创作的专业人员', 7),
('writer', '作家', '文化', '从事文学创作的专业人员', 8);

-- ----------------------------
-- 性格字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_personality`;
CREATE TABLE `dict_personality` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '性格代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '性格名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '性格详细描述',
  `traits` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '性格特征（JSON格式）',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '性格字典表' ROW_FORMAT = Dynamic;

-- 性格字典数据
INSERT INTO `dict_personality` (`code`, `name`, `description`, `traits`, `sort_order`) VALUES
('cheerful', '开朗活泼', '性格开朗，积极向上，喜欢与人交流', '["乐观", "外向", "热情", "友善"]', 1),
('calm', '沉稳内敛', '性格沉稳，思考深入，不轻易表达情感', '["理性", "内向", "深思", "稳重"]', 2),
('humorous', '幽默风趣', '喜欢开玩笑，能够活跃气氛，富有幽默感', '["风趣", "机智", "活跃", "有趣"]', 3),
('intellectual', '知识渊博', '博学多才，喜欢分享知识和见解', '["博学", "理性", "严谨", "好学"]', 4),
('romantic', '浪漫感性', '富有浪漫情怀，感情丰富，重视情感表达', '["感性", "浪漫", "温柔", "细腻"]', 5);

-- ----------------------------
-- 说话风格字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_speaking_style`;
CREATE TABLE `dict_speaking_style` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '说话风格代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '说话风格名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '风格详细描述',
  `examples` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '示例（JSON格式）',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '说话风格字典表' ROW_FORMAT = Dynamic;

-- 说话风格字典数据
INSERT INTO `dict_speaking_style` (`code`, `name`, `description`, `examples`, `sort_order`) VALUES
('formal', '正式严谨', '用词正式，语法规范，适合正式场合', '["您好", "请问", "非常感谢"]', 1),
('casual', '轻松随意', '用词轻松，语气随和，适合日常交流', '["哈哈", "不错哦", "挺好的"]', 2),
('cute', '可爱萌系', '用词可爱，语气萌萌哒，富有亲和力', '["哇塞", "好棒棒", "么么哒"]', 3),
('professional', '专业权威', '用词专业，逻辑清晰，体现专业性', '["根据分析", "建议", "专业角度"]', 4),
('poetic', '文艺诗意', '用词优美，富有诗意，文艺范十足', '["如诗如画", "岁月静好", "美好时光"]', 5);

-- ----------------------------
-- 口头禅字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_catchphrases`;
CREATE TABLE `dict_catchphrases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '口头禅代码',
  `phrase` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '口头禅内容',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类',
  `usage_scenario` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用场景',
  `frequency` enum('high','medium','low') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'medium' COMMENT '使用频率',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '口头禅字典表' ROW_FORMAT = Dynamic;

-- 口头禅字典数据
INSERT INTO `dict_catchphrases` (`code`, `phrase`, `category`, `usage_scenario`, `frequency`, `sort_order`) VALUES
('awesome', '太棒了！', '赞美', '表达赞赏时使用', 'high', 1),
('interesting', '有意思~', '评价', '对事物感兴趣时使用', 'medium', 2),
('agree', '说得对！', '认同', '表示赞同时使用', 'medium', 3),
('thinking', '让我想想...', '思考', '需要思考时使用', 'low', 4),
('surprise', '哇哦！', '惊讶', '表示惊讶时使用', 'medium', 5);

-- ----------------------------
-- 禁用词汇字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_forbidden_words`;
CREATE TABLE `dict_forbidden_words` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `word` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '禁用词汇',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '禁用原因',
  `severity` enum('high','medium','low') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'medium' COMMENT '严重程度',
  `replacement` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '替换词汇',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '禁用词汇字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Emoji偏好字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_emoji_preference`;
CREATE TABLE `dict_emoji_preference` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '偏好代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '偏好名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `usage_rate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用频率说明',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Emoji偏好字典表' ROW_FORMAT = Dynamic;

-- Emoji偏好字典数据
INSERT INTO `dict_emoji_preference` (`code`, `name`, `description`, `usage_rate`, `sort_order`) VALUES
('high', '高频使用', '经常使用emoji表情，几乎每句话都会带上', '80-100%', 1),
('medium', '适度使用', '适度使用emoji表情，在合适的时候使用', '30-60%', 2),
('low', '偶尔使用', '偶尔使用emoji表情，比较克制', '5-20%', 3),
('none', '不使用', '基本不使用emoji表情', '0-5%', 4);

-- ----------------------------
-- 响应速度字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_response_speed`;
CREATE TABLE `dict_response_speed` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '速度代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '速度名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `delay_range` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '延迟范围（秒）',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '响应速度字典表' ROW_FORMAT = Dynamic;

-- 响应速度字典数据
INSERT INTO `dict_response_speed` (`code`, `name`, `description`, `delay_range`, `sort_order`) VALUES
('fast', '快速响应', '几乎立即回复，模拟在线状态', '1-5秒', 1),
('normal', '正常响应', '正常的回复速度，模拟思考时间', '10-30秒', 2),
('slow', '慢速响应', '较慢的回复速度，模拟忙碌状态', '1-5分钟', 3);

-- ----------------------------
-- 活跃时间字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_active_time`;
CREATE TABLE `dict_active_time` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间名称',
  `time_range` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时间范围',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活跃时间字典表' ROW_FORMAT = Dynamic;

-- 活跃时间字典数据
INSERT INTO `dict_active_time` (`code`, `name`, `time_range`, `description`, `sort_order`) VALUES
('all_day', '全天', '00:00-23:59', '全天24小时都活跃', 1),
('work_hours', '工作时间', '09:00-18:00', '工作日的工作时间', 2),
('evening', '晚间时段', '18:00-23:00', '晚上休闲时间', 3),
('morning', '早间时段', '06:00-12:00', '早上时间段', 4),
('afternoon', '下午时段', '12:00-18:00', '下午时间段', 5),
('weekend', '周末', '周六日全天', '仅周末活跃', 6);

-- ----------------------------
-- 语言偏好字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_language_preference`;
CREATE TABLE `dict_language_preference` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '语言代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '语言名称',
  `native_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '本地名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '语言偏好字典表' ROW_FORMAT = Dynamic;

-- 语言偏好字典数据
INSERT INTO `dict_language_preference` (`code`, `name`, `native_name`, `description`, `sort_order`) VALUES
('zh_CN', '简体中文', '中文', '中国大陆简体中文', 1),
('zh_TW', '繁体中文', '繁體中文', '台湾繁体中文', 2),
('en_US', '英语', 'English', '美式英语', 3),
('ja_JP', '日语', '日本語', '日本语', 4),
('ko_KR', '韩语', '한국어', '韩国语', 5);

-- ----------------------------
-- 启用状态字典表
-- ----------------------------
DROP TABLE IF EXISTS `dict_is_active`;
CREATE TABLE `dict_is_active` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` tinyint(1) NOT NULL COMMENT '状态代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示颜色',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '启用状态字典表' ROW_FORMAT = Dynamic;

-- 启用状态字典数据
INSERT INTO `dict_is_active` (`code`, `name`, `description`, `color`, `sort_order`) VALUES
(1, '启用', '角色处于启用状态，可以正常使用', 'green', 1),
(0, '禁用', '角色处于禁用状态，暂停使用', 'red', 2);

SET FOREIGN_KEY_CHECKS = 1;
