/*
 AI角色通用字典表设计
 基于 robot_ai_character 表的字段设计通用字典表
 Date: 16/08/2025
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- AI角色通用字典表
-- ----------------------------
DROP TABLE IF EXISTS `robot_ai_character_dict`;
CREATE TABLE `robot_ai_character_dict` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dict_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型',
  `dict_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
  `dict_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典值（可存储JSON等复杂数据）',
  `sorted` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态标识',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI角色通用字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 字典数据插入
-- ----------------------------

-- 性别字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('gender', '男性', 1),
('gender', '女性', 2),
('gender', '其他', 3);

-- 职业字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('occupation', '教师', 1),
('occupation', '医生', 2),
('occupation', '工程师', 3),
('occupation', '设计师', 4),
('occupation', '学生', 5),
('occupation', '企业家', 6),
('occupation', '艺术家', 7),
('occupation', '作家', 8);

-- 性格字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('personality', '开朗活泼', 1),
('personality', '沉稳内敛', 2),
('personality', '幽默风趣', 3),
('personality', '知识渊博', 4),
('personality', '浪漫感性', 5);

-- 说话风格字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('speaking_style', '正式严谨', 1),
('speaking_style', '轻松随意', 2),
('speaking_style', '可爱萌系', 3),
('speaking_style', '专业权威', 4),
('speaking_style', '文艺诗意', 5);

-- 口头禅字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `dict_value`, `sorted`) VALUES
('catchphrases', '太棒了！', '太棒了！', 1),
('catchphrases', '有意思~', '有意思~', 2),
('catchphrases', '说得对！', '说得对！', 3),
('catchphrases', '让我想想...', '让我想想...', 4),
('catchphrases', '哇哦！', '哇哦！', 5);

-- 禁用词汇字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('forbidden_words', '政治敏感词', 1),
('forbidden_words', '暴力词汇', 2),
('forbidden_words', '脏话粗口', 3);

-- Emoji偏好字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('emoji_preference', '高频使用', 1),
('emoji_preference', '适度使用', 2),
('emoji_preference', '偶尔使用', 3),
('emoji_preference', '不使用', 4);

-- 响应速度字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('response_speed', '快速响应', 1),
('response_speed', '正常响应', 2),
('response_speed', '慢速响应', 3);

-- 活跃时间字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('active_time', '全天', 1),
('active_time', '工作时间', 2),
('active_time', '晚间时段', 3),
('active_time', '早间时段', 4),
('active_time', '下午时段', 5),
('active_time', '周末', 6);

-- 语言偏好字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('language_preference', '简体中文', 1),
('language_preference', '繁体中文', 2),
('language_preference', '英语', 3),
('language_preference', '日语', 4),
('language_preference', '韩语', 5);

-- 启用状态字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_name`, `sorted`) VALUES
('is_active', '启用', 1),
('is_active', '禁用', 2);

SET FOREIGN_KEY_CHECKS = 1;
