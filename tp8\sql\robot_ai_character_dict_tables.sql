/*
 AI角色通用字典表设计
 基于 robot_ai_character 表的字段设计通用字典表
 Date: 16/08/2025
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- AI角色通用字典表
-- ----------------------------
DROP TABLE IF EXISTS `robot_ai_character_dict`;
CREATE TABLE `robot_ai_character_dict` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dict_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型',
  `dict_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典代码',
  `dict_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
  `dict_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字典值（可存储JSON等复杂数据）',
  `parent_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级代码（用于层级关系）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `extra_data` json NULL COMMENT '扩展数据（JSON格式）',
  `sorted` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用（1=是，0=否）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_type_code`(`dict_type`, `dict_code`) USING BTREE,
  INDEX `idx_dict_type`(`dict_type`) USING BTREE,
  INDEX `idx_parent_code`(`parent_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI角色通用字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 字典数据插入
-- ----------------------------

-- 性别字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `sorted`) VALUES
('gender', 'male', '男性', '男性角色', 1),
('gender', 'female', '女性', '女性角色', 2),
('gender', 'other', '其他', '其他性别或不明确', 3);

-- 职业字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('occupation', 'teacher', '教师', '从事教育工作的专业人员', '{"category": "教育"}', 1),
('occupation', 'doctor', '医生', '从事医疗工作的专业人员', '{"category": "医疗"}', 2),
('occupation', 'engineer', '工程师', '从事工程技术工作的专业人员', '{"category": "技术"}', 3),
('occupation', 'designer', '设计师', '从事设计工作的专业人员', '{"category": "创意"}', 4),
('occupation', 'student', '学生', '在校学习的人员', '{"category": "教育"}', 5),
('occupation', 'entrepreneur', '企业家', '创业或经营企业的人员', '{"category": "商业"}', 6),
('occupation', 'artist', '艺术家', '从事艺术创作的专业人员', '{"category": "艺术"}', 7),
('occupation', 'writer', '作家', '从事文学创作的专业人员', '{"category": "文化"}', 8);

-- 性格字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('personality', 'cheerful', '开朗活泼', '性格开朗，积极向上，喜欢与人交流', '{"traits": ["乐观", "外向", "热情", "友善"]}', 1),
('personality', 'calm', '沉稳内敛', '性格沉稳，思考深入，不轻易表达情感', '{"traits": ["理性", "内向", "深思", "稳重"]}', 2),
('personality', 'humorous', '幽默风趣', '喜欢开玩笑，能够活跃气氛，富有幽默感', '{"traits": ["风趣", "机智", "活跃", "有趣"]}', 3),
('personality', 'intellectual', '知识渊博', '博学多才，喜欢分享知识和见解', '{"traits": ["博学", "理性", "严谨", "好学"]}', 4),
('personality', 'romantic', '浪漫感性', '富有浪漫情怀，感情丰富，重视情感表达', '{"traits": ["感性", "浪漫", "温柔", "细腻"]}', 5);

-- 说话风格字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('speaking_style', 'formal', '正式严谨', '用词正式，语法规范，适合正式场合', '{"examples": ["您好", "请问", "非常感谢"]}', 1),
('speaking_style', 'casual', '轻松随意', '用词轻松，语气随和，适合日常交流', '{"examples": ["哈哈", "不错哦", "挺好的"]}', 2),
('speaking_style', 'cute', '可爱萌系', '用词可爱，语气萌萌哒，富有亲和力', '{"examples": ["哇塞", "好棒棒", "么么哒"]}', 3),
('speaking_style', 'professional', '专业权威', '用词专业，逻辑清晰，体现专业性', '{"examples": ["根据分析", "建议", "专业角度"]}', 4),
('speaking_style', 'poetic', '文艺诗意', '用词优美，富有诗意，文艺范十足', '{"examples": ["如诗如画", "岁月静好", "美好时光"]}', 5);

-- 口头禅字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `dict_value`, `description`, `extra_data`, `sorted`) VALUES
('catchphrases', 'awesome', '太棒了！', '太棒了！', '表达赞赏时使用', '{"category": "赞美", "frequency": "high"}', 1),
('catchphrases', 'interesting', '有意思~', '有意思~', '对事物感兴趣时使用', '{"category": "评价", "frequency": "medium"}', 2),
('catchphrases', 'agree', '说得对！', '说得对！', '表示赞同时使用', '{"category": "认同", "frequency": "medium"}', 3),
('catchphrases', 'thinking', '让我想想...', '让我想想...', '需要思考时使用', '{"category": "思考", "frequency": "low"}', 4),
('catchphrases', 'surprise', '哇哦！', '哇哦！', '表示惊讶时使用', '{"category": "惊讶", "frequency": "medium"}', 5);

-- 禁用词汇字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('forbidden_words', 'politics', '政治敏感词', '涉及政治敏感的词汇', '{"severity": "high", "category": "政治"}', 1),
('forbidden_words', 'violence', '暴力词汇', '涉及暴力的词汇', '{"severity": "high", "category": "暴力"}', 2),
('forbidden_words', 'profanity', '脏话粗口', '不文明用语', '{"severity": "medium", "category": "不文明"}', 3);

-- Emoji偏好字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('emoji_preference', 'high', '高频使用', '经常使用emoji表情，几乎每句话都会带上', '{"usage_rate": "80-100%"}', 1),
('emoji_preference', 'medium', '适度使用', '适度使用emoji表情，在合适的时候使用', '{"usage_rate": "30-60%"}', 2),
('emoji_preference', 'low', '偶尔使用', '偶尔使用emoji表情，比较克制', '{"usage_rate": "5-20%"}', 3),
('emoji_preference', 'none', '不使用', '基本不使用emoji表情', '{"usage_rate": "0-5%"}', 4);

-- 响应速度字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('response_speed', 'fast', '快速响应', '几乎立即回复，模拟在线状态', '{"delay_range": "1-5秒"}', 1),
('response_speed', 'normal', '正常响应', '正常的回复速度，模拟思考时间', '{"delay_range": "10-30秒"}', 2),
('response_speed', 'slow', '慢速响应', '较慢的回复速度，模拟忙碌状态', '{"delay_range": "1-5分钟"}', 3);

-- 活跃时间字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('active_time', 'all_day', '全天', '全天24小时都活跃', '{"time_range": "00:00-23:59"}', 1),
('active_time', 'work_hours', '工作时间', '工作日的工作时间', '{"time_range": "09:00-18:00"}', 2),
('active_time', 'evening', '晚间时段', '晚上休闲时间', '{"time_range": "18:00-23:00"}', 3),
('active_time', 'morning', '早间时段', '早上时间段', '{"time_range": "06:00-12:00"}', 4),
('active_time', 'afternoon', '下午时段', '下午时间段', '{"time_range": "12:00-18:00"}', 5),
('active_time', 'weekend', '周末', '仅周末活跃', '{"time_range": "周六日全天"}', 6);

-- 语言偏好字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('language_preference', 'zh_CN', '简体中文', '中国大陆简体中文', '{"native_name": "中文"}', 1),
('language_preference', 'zh_TW', '繁体中文', '台湾繁体中文', '{"native_name": "繁體中文"}', 2),
('language_preference', 'en_US', '英语', '美式英语', '{"native_name": "English"}', 3),
('language_preference', 'ja_JP', '日语', '日本语', '{"native_name": "日本語"}', 4),
('language_preference', 'ko_KR', '韩语', '韩国语', '{"native_name": "한국어"}', 5);

-- 启用状态字典
INSERT INTO `robot_ai_character_dict` (`dict_type`, `dict_code`, `dict_name`, `description`, `extra_data`, `sorted`) VALUES
('is_active', '1', '启用', '角色处于启用状态，可以正常使用', '{"color": "green"}', 1),
('is_active', '0', '禁用', '角色处于禁用状态，暂停使用', '{"color": "red"}', 2);

SET FOREIGN_KEY_CHECKS = 1;
