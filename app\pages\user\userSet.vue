<template>
	<view>
		<tui-list-view>
			<tui-list-cell :arrow="true" @click="showTips">
				<view class="tui-item-box">
					<tui-icon name="applets" :size="24" color="#5677fc"></tui-icon>
					<view class="tui-list-cell_name">用户ID</view>
					<view class="tui-right">{{userInfo.user_id}}</view>
				</view>
			</tui-list-cell>
			<tui-list-cell :arrow="true" @click="showTips">
				<view class="tui-item-box">
					<tui-icon name="people-fill" :size="24" color="#5677fc"></tui-icon>
					<view class="tui-list-cell_name">用户头像</view>
					<view class="tui-right">
						<image class="tui-logo" :src="userInfo.avatar_url"></image>
					</view>
				</view>
			</tui-list-cell>
			<tui-list-cell :arrow="true" @click="showTips">
				<view class="tui-item-box">
					<tui-icon name="service-fill" :size="24" color="#5677fc"></tui-icon>
					<view class="tui-list-cell_name">用户昵称</view>
					<view class="tui-right">{{userInfo.nickname}}</view>
				</view>
			</tui-list-cell>
			<tui-list-cell :arrow="true" @click="toPageAuth('/pages/user/gameArea')">
				<view class="tui-item-box">
					<tui-icon name="satisfied" :size="24" color="#5677fc"></tui-icon>
					<view class="tui-list-cell_name">游戏平台<text class="tips">可修改</text></view>
					<view class="tui-right">{{userInfo.platform}}</view>
				</view>
			</tui-list-cell>
			<tui-list-cell :arrow="true" @click="toPageAuth('/pages/user/gameArea')">
				<view class="tui-item-box">
					<tui-icon name="satisfied" :size="24" color="#5677fc"></tui-icon>
					<view class="tui-list-cell_name">游戏大区<text class="tips">可修改</text></view>
					<view class="tui-right">{{userInfo.region}}区</view>
				</view>
			</tui-list-cell>
			<tui-list-cell :arrow="true" @click="toPageAuth('/pages/user/gameArea')">
				<view class="tui-item-box">
					<tui-icon name="satisfied" :size="24" color="#5677fc"></tui-icon>
					<view class="tui-list-cell_name">游戏ID<text class="tips">可修改</text></view>
					<view class="tui-right">{{userInfo.game_id}}</view>
				</view>
			</tui-list-cell>
			<!-- <tui-list-cell :arrow="true" v-if="userInfo.phone">
				<view class="tui-item-box">
					<tui-icon name="voipphone" :size="24" color="#5677fc"></tui-icon>
					<view class="tui-list-cell_name">绑定手机</view>
					<view class="tui-right">{{userInfo.phone}}</view>
				</view>
			</tui-list-cell>
			<tui-list-cell :arrow="true" v-else @click="toPageAuth('/pages/user/userPhone')">
				<view class="tui-item-box">
					<tui-icon name="voipphone" :size="24" color="#5677fc"></tui-icon>
					<view class="tui-list-cell_name">绑定手机</view>
					<view class="tui-right">未绑定</view>
				</view>
			</tui-list-cell>

			<tui-list-cell :arrow="true">
				<view class="tui-item-box" @click="toPageAuth('/pages/user/realName')">
					<tui-icon name="card-fill" :size="24" color="#5677fc"></tui-icon>
					<text class="tui-list-cell_name">我的实名</text>
					<view class="tui-right">{{userInfo.real_name}}</view>
				</view>
			</tui-list-cell>
			<tui-list-cell :arrow="true">
				<view class="tui-item-box" @click="clearLogin">
					<tui-icon name="setup" :size="24" color="#5677fc"></tui-icon>
					<text class="tui-list-cell_name">清除登录</text>
				</view>
			</tui-list-cell> -->
		</tui-list-view>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';
	export default {
		computed: {
			...mapState({
				userInfo: state => state.app.userInfo,
				isLogin: state => state.app.isLogin
			})
		},
		methods: {
			clearLogin() {
				uni.setStorageSync('token', '');
				uni.showToast({
					title: '清除登录成功，请关闭后再打开',
					icon: 'none'
				})
			},
			showTips() {
				uni.showToast({
					title: '无法修改，等待后续开放',
					icon: 'none'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding-bottom: env(safe-area-inset-bottom);
	}

	.tui-item-box {
		width: 100%;
		display: flex;
		align-items: center;
	}

	.tui-ml-auto {
		margin-left: auto;
	}

	.tui-right {
		margin-left: auto;
		margin-right: 34rpx;
		font-size: 26rpx;
		color: #999;
	}

	.tui-logo {
		height: 52rpx;
		width: 52rpx;
	}

	.tui-list-cell_name {
		padding-left: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.tips{
		margin-left: 10rpx;
		font-size: 24rpx;
		color: #fff;
		padding: 4rpx 10rpx;
		background-color: #EB0909;
		border-radius: 12rpx;
	}
</style>