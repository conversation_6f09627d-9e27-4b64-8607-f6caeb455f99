<?php
use think\facade\Route;
use app\dogadmin\middleware\AdminCheck;
use app\dogadmin\middleware\MenuAuth;
use app\dogadmin\controller\Base;

// 机器人AI角色管理路由
Base::registerCrudRoutes('robotAiCharacter', 'app\robot\controller\RobotAiCharacter', [AdminCheck::class, MenuAuth::class]);

// 机器人AI角色字典管理路由
Base::registerCrudRoutes('robotAiCharacterDict', 'app\robot\controller\RobotAiCharacterDict', [AdminCheck::class, MenuAuth::class]);

// 字典相关的额外路由
Route::group('robotAiCharacterDict', function () {
    Route::get('getAllActiveDict', 'app\robot\controller\RobotAiCharacterDict@getAllActiveDict');
    Route::get('getActiveDictByType', 'app\robot\controller\RobotAiCharacterDict@getActiveDictByType');
})->middleware([AdminCheck::class]);
