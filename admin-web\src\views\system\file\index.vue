<template>
  <div class="koi-flex flex flex-row">
    <koi-card class="max-w-160px m-r-6px">
      <template #header>
        <div class="flex justify-between select-none">
          <div class="flex flex-items-center gap-6px">
            <el-icon :size="18">
              <Folder />
            </el-icon>
            <div>文件分类</div>
          </div>
        </div>
      </template>
      <div
        class="h-26px text-14px text-#555 m-t-2px rounded-6px dark:text-#CFD3DC flex flex-items-center p-y-4px p-x-16px hover:bg-[rgba(0,0,0,0.06)] hover:dark:bg-[rgba(255,255,255,0.06)]"
        v-for="item in koiDicts.sys_file_type"
        :key="item.dict_id"
        :class="selectedIndex === item?.dict_value ? 'bg-[--el-color-primary-light-8]! text-[--el-color-primary]!' : ''"
        @click="handleSelectedIndex(item?.dict_value)"
      >
        <div class="line-clamp-1">{{ item.dict_label }}</div>
      </div>
    </koi-card>
    <KoiCard>
      <!-- 搜索条件 -->
      <el-form v-show="showSearch" :inline="true">
        <el-form-item label="文件原始名称" prop="file_name">
          <el-input
            placeholder="请输入文件原始名称"
            v-model="searchParams.file_name"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item label="文件后缀" prop="file_suffix">
          <el-input
            placeholder="请输入文件后缀"
            v-model="searchParams.file_suffix"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item label="文件服务类型" prop="file_service">
          <el-select
            placeholder="请选择文件服务类型"
            v-model="searchParams.file_service"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          >
            <el-option
              v-for="koi in koiDicts.sys_file_service"
              :key="koi.dict_value"
              :label="koi.dict_label"
              :value="koi.dict_value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch" v-auth="['dogadmin:sysFile:search']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格头部按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5" v-auth="['dogadmin:sysFile:upload']">
          <el-button type="primary" icon="Upload" plain @click="handleUpload()">文件上传</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['dogadmin:sysFile:delete']">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete()" :disabled="multiple">删除</el-button>
        </el-col>
        <KoiToolbar v-model:showSearch="showSearch" @refreshTable="handleListPage"></KoiToolbar>
      </el-row>

      <div class="h-20px"></div>
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        border
        :data="tableList"
        empty-text="暂时没有数据哟🌻"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" prop="fileId" width="80px" align="center" type="index"></el-table-column>
        <el-table-column
          label="文件原始名称"
          prop="file_name"
          width="180px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="文件新名称"
          prop="new_name"
          width="180px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="文件大小"
          prop="file_size"
          width="120px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="文件后缀"
          prop="file_suffix"
          width="100px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="文件上传路径"
          prop="file_upload"
          width="180px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="文件回显路径"
          prop="file_path"
          width="120px"
          align="center"
          :show-overflow-tooltip="true"
          v-if="selectedIndex === '0'"
        >
          <template #default="scope">
            <el-tooltip :content="scope.row.file_path" placement="top" v-if="scope.row.fileType === '1'">
              <div class="flex justify-center">
                <el-image
                  class="rounded-8px w-60px h-60px"
                  fit="contain"
                  :preview-teleported="true"
                  :preview-src-list="[scope.row.file_path]"
                  :src="scope.row.file_path"
                >
                  <template #error>
                    <el-image
                      src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"
                      fit="cover"
                      :preview-src-list="['https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png']"
                      :preview-teleported="true"
                      class="w-60px h-60px rounded-8px"
                    ></el-image>
                  </template>
                </el-image>
              </div>
            </el-tooltip>
            <div v-else>{{ scope.row.file_path }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="文件回显路径"
          prop="file_path"
          width="120px"
          align="center"
          :show-overflow-tooltip="true"
          v-if="selectedIndex === '1'"
        >
          <template #default="scope">
            <el-tooltip :content="scope.row.file_path" placement="top">
              <div class="flex justify-center">
                <el-image
                  class="rounded-8px w-60px h-60px"
                  fit="contain"
                  :preview-teleported="true"
                  :preview-src-list="[scope.row.file_path]"
                  :src="scope.row.file_path"
                >
                  <template #error>
                    <el-image
                      src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"
                      fit="cover"
                      :preview-src-list="['https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png']"
                      :preview-teleported="true"
                      class="w-60px h-60px rounded-8px"
                    ></el-image>
                  </template>
                </el-image>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="文件回显路径"
          prop="file_path"
          width="200px"
          align="center"
          :show-overflow-tooltip="true"
          v-if="selectedIndex != '0' && selectedIndex != '1'"
        >
        </el-table-column>
        <el-table-column label="文件服务类型" prop="file_service" width="120px" align="center">
          <template #default="scope">
            <KoiTag :tagOptions="koiDicts.sys_file_service" :value="scope.row.file_service"></KoiTag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="create_time" width="180px" align="center"></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="110"
          fixed="right"
          v-auth="['dogadmin:sysFile:download', 'dogadmin:sysFile:delete']"
        >
          <template #default="{ row }">
            <el-tooltip content="文件下载🌻" placement="top">
              <el-button
                type="info"
                icon="Download"
                circle
                plain
                @click="handleDownload(row)"
                v-auth="['dogadmin:sysFile:download']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button
                type="danger"
                icon="Delete"
                circle
                plain
                @click="handleDelete(row)"
                v-auth="['dogadmin:sysFile:delete']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="h-20px"></div>
      <!-- 分页 -->
      <el-pagination
        background
        v-model:current-page="searchParams.page_no"
        v-model:page-size="searchParams.page_size"
        v-show="total > 0"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleListPage"
        @current-change="handleListPage"
      />
      <!-- 多文件/单文件上传 -->
      <KoiDialog
        ref="koiDialogRef"
        :title="title"
        :footerHidden="true"
      >
        <template #content>
          <el-form ref="formRef" :rules="rules" :model="form" label-width="auto" status-icon>
            <el-row>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="文件服务" prop="file_service">
                  <el-select
                    placeholder="请选择文件服务类型"
                    v-model="form.file_service"
                    clearable
                    style="width: 220px"
                    @change="handlefile_serviceChange"
                  >
                    <el-option
                      v-for="koi in koiDicts.sys_file_service"
                      :key="koi.dict_value"
                      :label="koi.dict_label"
                      :value="koi.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="{ span: 24 }" :sm="{ span: 24 }">
                <el-form-item label="文件回显路径" prop="file_path">
                  <KoiUploadFiles v-model:fileList="form.file_path"></KoiUploadFiles>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </KoiDialog>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="sysFilePage">
import { nextTick, ref, reactive, onMounted } from "vue";
import { koiNoticeSuccess, koiNoticeError, koiMsgError, koiMsgWarning, koiMsgBox, koiMsgInfo } from "@/utils/koi.ts";
import { listPage, deleteById, batchDelete } from "@/api/system/file/index.ts";
import { useKoiDict } from "@/hooks/dicts/index.ts";

const { koiDicts } = useKoiDict(["sys_file_type", "sys_file_service"]);
// 数据表格加载页面动画
const loading = ref(false);
/** 是否显示搜索表单 */
const showSearch = ref<boolean>(true); // 默认显示搜索条件
// 数据表格数据
const tableList = ref<any>([]);

// 查询参数
const searchParams = ref<any>({
  page_no: 1, // 第几页
  page_size: 10, // 每页显示多少条
  file_name: "",
  file_suffix: "",
  file_service: "",
  fileType: "0"
});

const total = ref<number>(0);

// 重置搜索参数
const resetSearchParams = () => {
  searchParams.value = {
    page_no: 1,
    page_size: 10,
    file_name: "",
    file_suffix: "",
    file_service: "",
    fileType: "0"
  };
};

/** 搜索 */
const handleSearch = () => {
  searchParams.value.page_no = 1;
  handleTableData();
};

/** 重置 */
const resetSearch = () => {
  resetSearchParams();
  handleListPage();
};

/** 数据表格 */
const handleListPage = async () => {
  try {
    tableList.value = []; // 重置表格数据
    loading.value = true;
    const res: any = await listPage(searchParams.value);
    tableList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

/** 数据表格[不带Loading，删除、批量删除等使用] */
const handleTableData = async () => {
  try {
    const res: any = await listPage(searchParams.value);
    tableList.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

// 获取数据表格数据
onMounted(() => {
  handleListPage();
});

const ids = ref([]); // 选中数组
const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
/** 是否多选 */
const handleSelectionChange = (selection: any) => {
  ids.value = selection.map((item: any) => item.fileId);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
};

/** 删除 */
const handleDelete = (row: any) => {
  const id = row.fileId;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认删除该数据么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await deleteById(id);
        handleTableData();
        koiNoticeSuccess("删除成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 批量删除 */
const handleBatchDelete = () => {
  if (ids.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认进行批量删除么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await batchDelete(ids.value);
        handleTableData();
        koiNoticeSuccess("批量删除成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("批量删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 选中颜色
const selectedIndex = ref("0");
/** 查询分类结束 */
// 点击选中分类
const handleSelectedIndex = (value?: string) => {
  if (value) {
    selectedIndex.value = value;
    searchParams.value.fileType = selectedIndex.value;
    handleListPage();
  }
};

// 文件上传
const handleUpload = () => {
  // 打开弹出框
  koiDialogRef.value.koiOpen();
  koiMsgInfo("上传文件🌻");
  // 标题
  title.value = "上传操作🌻";
  // 重置表单
  resetForm();
  form.value.file_path = [];
};

/** 回显数据 */
// const handleEcho = async (id: any) => {
//   if (id == null || id == "") {
//     koiMsgWarning("请选择需要上传的数据🌻");
//     return;
//   }
//   try {
//     const res: any = await getById(id);
//     form.value.file_path = [
//       {
//         name: res.data?.file_name,
//         url: res.data?.file_path
//       }
//     ];
//   } catch (error) {
//     koiNoticeError("数据获取失败，请刷新重试🌻");
//     console.log(error);
//   }
// };

/** 多文件/单文件上传弹框 */
const koiDialogRef = ref();
// 标题
const title = ref("文件资源表");
// form表单Ref
const formRef = ref<any>();

// form表单
let form = ref<any>({
  file_path: [],
  file_service: "1"
});

/** 清空表单数据 */
const resetForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (formRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      formRef.value.resetFields();
    }
  });
  form.value = {
    file_path: [],
    file_service: "1"
  };
};

/** 表单规则 */
const rules = reactive({
  file_path: [{ required: true, message: "请上传文件", trigger: "blur" }],
  file_service: [{ required: true, message: "请选择文件服务类型", trigger: "blur" }]
});

/** 选择类型改变 */
const handlefile_serviceChange = () => {
  // 其他服务类型，定义变量，改变组件上传路径接口
  if (form.value.file_service !== "1") {
    koiMsgWarning("亲，暂时仅支持本地上传哟🌻");
  }
};

/** 文件下载🌻 */
const handleDownload = (row: any) => {
  const fileOriginalName = row?.file_name;
  const url = row?.file_path;
  if (url == null || url == "") {
    koiMsgWarning("请选中需要下载的数据🌻");
    return;
  }
  koiMsgBox("您确认下载该数据么？")
    .then(async () => {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Network response was not ok: ${response.status}`);
        }
        // 创建 Blob 对象
        const blob = await response.blob();
        // 创建对象 URL
        const downloadUrl = window.URL.createObjectURL(blob);
        // 创建一个隐藏的下载链接
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = fileOriginalName; // 设置下载文件名
        link.style.display = "none";
        // 添加到 DOM 中
        document.body.appendChild(link);
        // 触发点击事件
        link.click();
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        koiNoticeSuccess("下载成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("下载失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};
</script>

<style lang="scss" scoped></style>
