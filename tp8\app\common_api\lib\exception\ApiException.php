<?php
declare(strict_types=1);

namespace app\common_api\lib\exception;

use app\lib\exception\BaseException;
use Throwable;

/**
 * 认证异常类
 * 处理认证相关的异常，如登录失败、权限不足等
 */
class ApiException extends BaseException
{
    /**
     * 错误码映射
     * @var array
     */
    protected static array $errorCodes = [
        2 => 'Token已过期',
        3 => 'Token验证失败',
        4 => '权限不足',
        5 => '账号已被禁用',
        6 => '账号不存在'
    ];

    /**
     * 构造函数
     * 
     * @param $data 附加数据
     * @param int $code 错误码
     * @param string $message 自定义错误消息，为空时使用错误码映射的消息
     * @param Throwable|null $previous 上一个异常
     */
    public function __construct($data = [], int $code = 2, string $message = '', ?Throwable $previous = null)
    {
        parent::__construct($data, $code, $message, $previous);
    }
}