<template>
  <view class="moments-container">
    <!-- 朋友圈头部 -->
    <MomentHeader 
      :userInfo="currentUser"
      @changeBackground="changeBackground"
      @viewProfile="viewProfile"
      @showPublishOptions="showPublishOptions"
    />
    
    <!-- 动态列表 -->
    <scroll-view 
      class="moments-list" 
      scroll-y="true"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <MomentItem
        v-for="moment in momentsList"
        :key="moment.id"
        :moment="moment"
        @like="handleLike"
        @comment="handleComment"
        @viewProfile="viewProfile"
        @viewLocation="viewLocation"
        @showMore="showMoreOptions"
      />
      
      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <text class="load-text">{{ isLoading ? '加载中...' : '上拉加载更多' }}</text>
      </view>
      
      <!-- 没有更多 -->
      <view v-else-if="momentsList.length > 0" class="no-more">
        <text class="no-more-text">没有更多动态了</text>
      </view>
      
      <!-- 空状态 -->
      <view v-if="momentsList.length === 0 && !isLoading" class="empty-state">
        <text class="empty-text">还没有动态，快来发布第一条吧！</text>
      </view>
    </scroll-view>
    
    <!-- 发布选项弹窗 -->
    <view v-if="showPublishModal" class="publish-modal" @tap="hidePublishModal">
      <view class="publish-options" @tap.stop>
        <view class="option-item" @tap="publishText">
          <text class="option-icon">📝</text>
          <text class="option-text">发表文字</text>
        </view>
        <view class="option-item" @tap="publishImage">
          <text class="option-icon">📷</text>
          <text class="option-text">拍照或选择图片</text>
        </view>
        <view class="cancel-option" @tap="hidePublishModal">
          <text class="cancel-text">取消</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import MomentHeader from '@/components/moments/MomentHeader.vue'
import MomentItem from '@/components/moments/MomentItem.vue'

export default {
  components: {
    MomentHeader,
    MomentItem
  },
  data() {
    return {
      currentUser: {
        id: 'user_001',
        name: '张三',
        avatar: '/static/images/user-avatar.png',
        backgroundImage: '/static/images/moment-bg.jpg'
      },
      momentsList: [],
      isLoading: false,
      isRefreshing: false,
      hasMore: true,
      page: 1,
      showPublishModal: false
    }
  },
  onLoad() {
    this.loadMoments()
  },
  methods: {
    async loadMoments(refresh = false) {
      if (this.isLoading) return
      
      this.isLoading = true
      
      try {
        // 模拟API调用
        const mockData = this.generateMockData()
        
        if (refresh) {
          this.momentsList = mockData
          this.page = 1
        } else {
          this.momentsList = [...this.momentsList, ...mockData]
        }
        
        this.hasMore = mockData.length === 10
        this.page++
      } catch (error) {
        console.error('加载动态失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.isLoading = false
        this.isRefreshing = false
      }
    },
    
    generateMockData() {
      const users = [
        { id: '1', name: '小明', avatar: '/static/images/avatar1.png' },
        { id: '2', name: '小红', avatar: '/static/images/avatar2.png' },
        { id: '3', name: '小华', avatar: '/static/images/avatar3.png' },
        { id: '4', name: '小李', avatar: '/static/images/avatar4.png' }
      ]
      
      const contents = [
        '今天天气真不错！',
        '和朋友们一起吃饭，很开心～',
        '工作忙碌的一天结束了',
        '分享一些生活中的小美好',
        '周末愉快！',
        '新的一周开始了，加油！'
      ]
      
      const mockMoments = []
      
      for (let i = 0; i < 5; i++) {
        const user = users[Math.floor(Math.random() * users.length)]
        const content = contents[Math.floor(Math.random() * contents.length)]
        const imageCount = Math.floor(Math.random() * 4)
        const images = []
        
        for (let j = 0; j < imageCount; j++) {
          images.push(`/static/images/moment${j + 1}.jpg`)
        }
        
        mockMoments.push({
          id: `moment_${Date.now()}_${i}`,
          user,
          content,
          images,
          location: Math.random() > 0.7 ? '北京市朝阳区' : null,
          createTime: new Date().getTime() - Math.random() * 86400000 * 7,
          isLiked: Math.random() > 0.5,
          likes: Math.random() > 0.3 ? [
            { id: 'like1', user: users[0] },
            { id: 'like2', user: users[1] }
          ] : [],
          comments: Math.random() > 0.4 ? [
            {
              id: 'comment1',
              user: users[0],
              content: '不错哦！',
              createTime: new Date().getTime() - 3600000
            }
          ] : []
        })
      }
      
      return mockMoments
    },
    
    onRefresh() {
      this.isRefreshing = true
      this.loadMoments(true)
    },
    
    loadMore() {
      if (this.hasMore && !this.isLoading) {
        this.loadMoments()
      }
    },
    
    handleLike(momentId) {
      const moment = this.momentsList.find(m => m.id === momentId)
      if (moment) {
        moment.isLiked = !moment.isLiked
        
        if (moment.isLiked) {
          if (!moment.likes) moment.likes = []
          moment.likes.unshift({
            id: `like_${Date.now()}`,
            user: this.currentUser
          })
        } else {
          moment.likes = moment.likes.filter(like => like.user.id !== this.currentUser.id)
        }
      }
    },
    
    handleComment(data) {
      const moment = this.momentsList.find(m => m.id === data.momentId)
      if (moment) {
        if (!moment.comments) moment.comments = []
        moment.comments.push({
          id: `comment_${Date.now()}`,
          user: this.currentUser,
          content: data.content,
          createTime: new Date().getTime()
        })
      }
    },
    
    showPublishOptions() {
      this.showPublishModal = true
    },
    
    hidePublishModal() {
      this.showPublishModal = false
    },
    
    publishText() {
      this.hidePublishModal()
      uni.navigateTo({
        url: '/pages/moments/publish?type=text'
      })
    },
    
    publishImage() {
      this.hidePublishModal()
      uni.navigateTo({
        url: '/pages/moments/publish?type=image'
      })
    },
    
    changeBackground() {
      uni.showToast({
        title: '更换背景功能开发中',
        icon: 'none'
      })
    },
    
    viewProfile(user) {
      uni.showToast({
        title: `查看${user.name}的资料`,
        icon: 'none'
      })
    },
    
    viewLocation(location) {
      uni.showToast({
        title: `查看位置：${location}`,
        icon: 'none'
      })
    },
    
    showMoreOptions(moment) {
      const items = ['删除', '设置权限']
      
      uni.showActionSheet({
        itemList: items,
        success: (res) => {
          if (res.tapIndex === 0) {
            this.deleteMoment(moment.id)
          }
        }
      })
    },
    
    deleteMoment(momentId) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条动态吗？',
        success: (res) => {
          if (res.confirm) {
            this.momentsList = this.momentsList.filter(m => m.id !== momentId)
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.moments-container {
  background-color: #F7F7F9;
  min-height: 100vh;
}

.moments-list {
  height: calc(100vh - 400rpx);
}

.load-more, .no-more, .empty-state {
  text-align: center;
  padding: 40rpx;
}

.load-text, .no-more-text, .empty-text {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.5);
}

.publish-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.publish-options {
  background-color: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx;
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.option-item:last-of-type {
  border-bottom: none;
}

.option-icon {
  font-size: 40rpx;
  margin-right: 32rpx;
}

.option-text {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.8);
}

.cancel-option {
  text-align: center;
  padding: 32rpx 0;
  margin-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-text {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.5);
}
</style>
