<?php

namespace app\common_api\controller;

use app\common_api\service\CommonUserService;
use app\dogadmin\common\Captcha;
use thans\jwt\facade\JWTAuth;

class CommonUser extends Base
{
    /**
     * @var CommonUserService
     */
    protected $service;

    public function initialize(): void
    {
        $this->service = new CommonUserService();
    }

    public function getTokenByPwd(){
        $params = $this->request->param();
        // 验证验证码
//        $captcha = new Captcha();
//        if (!$captcha->verify($params['code_key'], $params['security_code'])) {
//            return fRet('验证码错误');
//        }
        // 验证登录
        $user = $this->service->getTokenByPwd($params);
        if (!$user) {
            return fRet('用户名或密码不正确');
        }
        // 生成token
        $token = JWTAuth::builder(['user_id' => $user['id']]);
        return sRet($token);
    }

    public function getUserInfo(){
        $params = $this->request->param();
        $params['user_id'] = request()->user_id;
        $user = $this->service->getUserInfo($params);
        return sRet($user);
    }

    //注册用户
    public function register(){
        $params = $this->request->param();
        $userId = $this->service->register($params);
        if(!$userId) {
            return fRet('已经有相同的用户名');
        }
        $token = JWTAuth::builder(['user_id' => $userId]);
        return sRet($token);
    }

    //邮箱注册
    public function emailRegister(){
        $params = $this->request->param();
        
        // 验证邮箱格式
        if (empty($params['email']) || !filter_var($params['email'], FILTER_VALIDATE_EMAIL)) {
            return fRet('邮箱格式不正确');
        }
        
        // 验证邮箱验证码
        if (empty($params['email_code'])) {
            return fRet('邮箱验证码不能为空');
        }
        
        // 校验验证码是否正确
        $cachedCode = cache('email_code_' . $params['email']);
        if ($cachedCode != $params['email_code']) {
            return fRet('邮箱验证码错误或已过期');
        }
        
        // 调用service处理邮箱注册
        $userId = $this->service->emailRegister($params);
        if(!$userId) {
            return fRet('邮箱已被注册或验证码错误');
        }
        
        $token = JWTAuth::builder(['user_id' => $userId]);
        return sRet($token);
    }

    // 发送邮箱验证码
    public function sendEmailCode() {
        $params = $this->request->param();
        
        // 验证邮箱格式
        if (empty($params['email']) || !filter_var($params['email'], FILTER_VALIDATE_EMAIL)) {
            return fRet('邮箱格式不正确');
        }
        
        // 生成6位随机验证码
        $code = mt_rand(100000, 999999);
        
        // 存储验证码到缓存（有效期5分钟）
        cache('email_code_' . $params['email'], $code, 300);
        
        // 检查发送频率（60秒内只能发送一次）
        $lastSendTime = cache('email_code_time_' . $params['email']);
        if ($lastSendTime && time() - $lastSendTime < 60) {
            return fRet('操作过于频繁，请稍后再试');
        }
        
        // 发送邮件
        $subject = '您的注册验证码';
        $content = "<p>您的验证码是：<strong>{$code}</strong></p><p>验证码5分钟内有效，请勿泄露给他人。</p>";
        
        $mailResult = $this->service->sendEmail($params['email'], $subject, $content);
        
        if (!$mailResult) {
            return fRet('邮件发送失败，请稍后重试');
        }
        
        // 记录发送时间
        cache('email_code_time_' . $params['email'], time(), 300);
        
        return sRet([
            'email' => $params['email'], 
            'expire' => 300,
            'message' => '验证码已发送'
        ]);
    }

    // 忘记密码-重置密码
    public function resetPassword() {
        $params = $this->request->param();
        if (empty($params['email']) || empty($params['email_code']) || empty($params['password'])) {
            return fRet('请填写完整信息');
        }
        if (!filter_var($params['email'], FILTER_VALIDATE_EMAIL)) {
            return fRet('邮箱格式不正确');
        }
        if (mb_strlen($params['password']) < 6) {
            return fRet('新密码至少6位');
        }
        // 校验验证码
        $cachedCode = cache('email_code_' . $params['email']);
        if ($cachedCode != $params['email_code']) {
            return fRet('邮箱验证码错误或已过期');
        }
        $result = $this->service->resetPassword($params);
        if ($result === true) {
            // 验证码用完即删
            cache('email_code_' . $params['email'], null);
            return sRet([], '密码重置成功');
        } else {
            return fRet($result ?: '密码重置失败');
        }
    }

    // 修改密码
    public function changePassword() {
        $params = $this->request->param();
        $params['user_id'] = request()->user_id;
        if (empty($params['old_password']) || empty($params['new_password'])) {
            return fRet('请填写完整信息');
        }
        if (mb_strlen($params['new_password']) < 6) {
            return fRet('新密码至少6位');
        }
        $result = $this->service->changePassword($params);
        if ($result === true) {
            return sRet([], '密码修改成功');
        } else {
            return fRet($result ?: '密码修改失败');
        }
    }

    // 修改用户昵称
    public function updateNickname() {
        $params = $this->request->param();
        $params['user_id'] = request()->user_id;
        
        // 验证昵称
        if (empty($params['nickname'])) {
            return fRet('昵称不能为空');
        }
        
        if (mb_strlen($params['nickname']) > 20) {
            return fRet('昵称长度不能超过20个字符');
        }
        
        $result = $this->service->updateNickname($params);
        if (!$result) {
            return fRet('昵称修改失败');
        }
        
        return sRet(['nickname' => $params['nickname']], '昵称修改成功');
    }

    // 上传用户头像
    public function uploadAvatar() {
        try {
            $file = $this->request->file('avatar');
            if (!$file) {
                return fRet('请选择要上传的头像文件');
            }
            
            $userId = request()->user_id;
            
            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $extension = strtolower($file->getOriginalExtension());
            if (!in_array($extension, $allowedTypes)) {
                return fRet('只支持上传 jpg、jpeg、png、gif、webp 格式的图片');
            }
            
            // 验证文件大小（限制5MB）
            if ($file->getSize() > 5 * 1024 * 1024) {
                return fRet('头像文件大小不能超过5MB');
            }
            
            $result = $this->service->uploadAvatar($file, $userId,$extension);
            if (!$result) {
                return fRet('头像上传失败');
            }
            
            return sRet($result, '头像上传成功');
            
        } catch (\Exception $e) {
            return fRet('头像上传失败: ' . $e->getMessage());
        }
    }
}