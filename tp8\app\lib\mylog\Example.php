<?php
declare(strict_types=1);

namespace app\lib\mylog;

use app\lib\mylog\MyLog;

/**
 * 增强日志系统使用示例
 */
class Example
{
    /**
     * 基本日志示例
     */
    public function basicLogging(): void
    {
        // 简单日志
        MyLog::info('这是一条简单的信息日志');
        MyLog::error('这是一条错误日志');
        
        // 不同级别的日志
        MyLog::debug('调试信息');
        MyLog::notice('注意信息');
        MyLog::warning('警告信息');
        MyLog::critical('严重错误');
        MyLog::alert('警报信息');
        MyLog::emergency('紧急情况');
    }
    
    /**
     * 带上下文的日志示例
     */
    public function contextLogging(): void
    {
        // 带简单上下文的日志
        MyLog::info('用户登录成功', [
            'user_id' => 123,
            'username' => 'john_doe',
            'login_time' => date('Y-m-d H:i:s'),
            'ip' => '***********'
        ]);
        
        // 带嵌套数据的日志
        MyLog::info('订单创建', [
            'order_id' => 'ORD-2024-0001',
            'customer' => [
                'id' => 456,
                'name' => 'Jane <PERSON>',
                'vip_level' => 2
            ],
            'items' => [
                [
                    'product_id' => 101,
                    'name' => '商品A',
                    'price' => 99.99,
                    'quantity' => 2
                ],
                [
                    'product_id' => 102,
                    'name' => '商品B',
                    'price' => 199.99,
                    'quantity' => 1
                ]
            ],
            'total_amount' => 399.97,
            'payment_method' => 'credit_card'
        ]);
    }
    
    /**
     * 记录异常的示例
     */
    public function exceptionLogging(): void
    {
        try {
            // 模拟异常
            throw new \Exception('数据库连接失败', 1001);
        } catch (\Exception $e) {
            MyLog::error('操作失败', [
                'exception' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ],
                'context' => [
                    'operation' => 'database_connect',
                    'database' => 'main_db',
                    'host' => 'localhost'
                ]
            ]);
        }
    }
    
    /**
     * 记录对象的示例
     */
    public function objectLogging(): void
    {
        // 模拟一个对象
        $user = new \stdClass();
        $user->id = 789;
        $user->name = 'Alice Johnson';
        $user->email = '<EMAIL>';
        $user->roles = ['admin', 'editor'];
        
        // 记录对象
        MyLog::info('用户信息', [
            'user' => $user,
            'session_id' => session_id(),
            'is_admin' => true
        ]);
    }
    
    /**
     * 记录API调用的示例
     */
    public function apiLogging(): void
    {
        // 模拟API请求和响应
        $requestData = [
            'method' => 'GET',
            'endpoint' => '/api/users',
            'params' => ['page' => 1, 'limit' => 10],
            'headers' => ['Authorization' => 'Bearer ***', 'Content-Type' => 'application/json']
        ];
        
        $responseData = [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'total' => 100,
                'page' => 1,
                'limit' => 10,
                'users' => [/* 用户数据 */]
            ]
        ];
        
        // 记录API调用
        MyLog::info('API调用', [
            'request' => $requestData,
            'response' => $responseData,
            'duration_ms' => 120,
            'cache_hit' => false
        ]);
    }
    
    /**
     * 运行所有示例
     */
    public function runAll(): void
    {
        $this->basicLogging();
        $this->contextLogging();
        $this->exceptionLogging();
        $this->objectLogging();
        $this->apiLogging();
        
        echo "所有日志示例已执行，请查看日志文件。\n";
    }
}
