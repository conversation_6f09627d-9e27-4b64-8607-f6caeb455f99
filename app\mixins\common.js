import store from '@/store/index.js';
import dayjs from '@/utils/dayjs.js'
// #ifdef H5
import { shareReady } from '@/utils/wx.js'
// #endif
import { formatRelativeTime } from '@/utils/common.js';
const mixins = {
	data() {
		return {}
	},
	methods: {
		showTime(dateString) {
			return formatRelativeTime(dateString);
		},
		dealContentText(content) {
			let dd = content.replace(/<[^>]+>/g, ""); //截取html标签
			let dds = dd.replace(/&nbsp;/ig, ""); //截取空格等特殊标签
			return dds.substring(0, 200) + "...";
		},
		// #ifdef H5
		shareReady(data) {
			shareReady(data);
		},
		// #endif
		toPage(url, type) {
			// console.log('toPage', url);
			uni[type || 'navigateTo']({ url });
		},
		toPageAuth(url, type, isPhone) {
			console.log('toPageAuth', url);
			let isLogin = store.getters.isLogin;
			// console.log('isLogin', isLogin);
			type = type || 'navigateTo';
			if (isLogin) {
				if (isPhone) {
					let userInfo = store.getters.userInfo;
					if (userInfo.phone) {
						uni[type]({ url });
					} else {
						uni.showModal({
							title: '提示',
							content: '暂未绑定手机号，请绑定手机号后发布',
							confirmText: '立即绑定',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/user/phone'
									})
								} else if (res.cancel) {
									uni.showToast({
										title: '功能无法使用',
										icon: 'none'
									})
								}
							}
						})
					}

				} else {
					uni[type]({ url });
				}
			} else {
				// this.$refs.logintips.show = true;
				uni.showModal({
					title: '提示',
					content: '暂未登录，是否立即登录后操作',
					confirmText: '立即登录',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login/login'
							})
						} else if (res.cancel) {
							uni.showToast({
								title: '未登录部分功能无法使用',
								icon: 'none'
							})
						}
					}
				})
			}
		},
		formatTime(time, format) {
			format = format || 'YYYY-MM-DD HH:mm:ss'
			return dayjs(time * 1000).format(format)
		},
		formatTime2(time) {
			if (('' + time).length === 10) {
				time = parseInt(time) * 1000
			} else {
				time = +time
			}
			const d = new Date(time)
			const now = Date.now()

			const diff = (now - d) / 1000

			if (diff < 30) {
				return '刚刚'
			} else if (diff < 3600) {
				// less 1 hour
				return Math.ceil(diff / 60) + '分钟前'
			} else if (diff < 3600 * 24) {
				return Math.ceil(diff / 3600) + '小时前'
			} else if (diff < 3600 * 24 * 2) {
				return '1天前'
			}
			return dayjs(time).format('YYYY-MM-DD HH:mm')
		},
		setNavigationBarTitle(text) {
			uni.setNavigationBarTitle({
				title: text
			});
		}
	}
}
export default mixins