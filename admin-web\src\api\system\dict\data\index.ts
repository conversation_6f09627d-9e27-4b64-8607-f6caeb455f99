// 导入二次封装axios
import koi from "@/utils/axios.ts";

// 统一管理接口
enum API {
  LIST_DICT_TYPE = "/dogadmin/sysDictType/listDictType",
  LIST_PAGE = "/dogadmin/sysDictData/listPage",
  GET_BY_ID = "/dogadmin/sysDictData/getById",
  UPDATE = "/dogadmin/sysDictData/update",
  ADD = "/dogadmin/sysDictData/add",
  DELETE = "/dogadmin/sysDictData/deleteById",
  BATCH_DELETE = "/dogadmin/sysDictData/batchDelete",
  UPDATE_STATUE = "/dogadmin/sysDictData/updateStatus",
  LIST_DATA_BY_TYPE = "/dogadmin/sysDictData/getDictDataByType",
  GET_SORTED = "/dogadmin/sysDictData/getSorted"
}
// 暴露请求函数
// 条件下拉框
export const listDictType = () => {
  return koi.get(API.LIST_DICT_TYPE);
};
// 多条件分页查询数据
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "?id=" + id);
};

// 根据ID进行修改
export const update = (data: any) => {
  return koi.post(API.UPDATE, data);
};

// 添加
export const add = (data: any) => {
  return koi.post(API.ADD, data);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE,{ id: id });
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids});
};

// 修改状态
export const updateStatus = (id: any, status: any) => {
  return koi.post(API.UPDATE_STATUE,{ id: id, status }); 
};

// 根据字典类型查询数据
export const listDataByType = (dict_type: any) => {
  return koi.get(API.LIST_DATA_BY_TYPE + "?dict_type=" + dict_type); 
};

// 获取最新排序
export const getSorted = (dict_type: any) => {
  return koi.get(API.GET_SORTED + "?dict_type=" + dict_type);
};

