// 菜单路由权限[menu_type[1-目录 2-菜单页面 3-按钮权限]当等于2时才是页面]
export const listRouters = [
  {
    menu_id: 66,
    menu_name: "工作台",
    parent_id: 0,
    menu_type: "2",
    path: "/workbench/index",
    name: "workbenchPage",
    component: "workbench/index",
    icon: "Histogram",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "0",
    redirect: ""
  },
  // 系统管理
  {
    menu_id: 1,
    menu_name: "系统管理",
    parent_id: 0,
    menu_type: "1",
    path: "/system",
    name: "systemPage",
    component: "",
    icon: "Tools",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: "/system/user"
  },
  {
    menu_id: 2,
    menu_name: "用户管理",
    parent_id: 1,
    menu_type: "2",
    path: "/system/user",
    name: "userPage",
    component: "system/user/index",
    icon: "UserFilled",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  {
    menu_id: 11,
    menu_name: "角色管理",
    parent_id: 1,
    menu_type: "2",
    path: "/system/role",
    name: "rolePage",
    component: "system/role/index",
    icon: "CameraFilled",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  {
    menu_id: 17,
    menu_name: "菜单管理",
    parent_id: 1,
    menu_type: "2",
    path: "/system/menu",
    name: "menuPage",
    component: "system/menu/index",
    icon: "Menu",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  // 多级菜单
  {
    menu_id: 666,
    menu_name: "多级菜单",
    parent_id: 0,
    menu_type: "1",
    path: "/menus",
    name: "menusPage",
    component: "",
    icon: "KnifeFork",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: "/menus/menu1"
  },
  {
    menu_id: 667,
    menu_name: "菜单一",
    parent_id: 666,
    menu_type: "2",
    path: "/menus/menu1",
    name: "menu1Page",
    component: "menus/menu1",
    icon: "Coffee",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  {
    menu_id: 668,
    menu_name: "菜单二",
    parent_id: 666,
    menu_type: "1",
    path: "/menus/menu2",
    name: "menu2Page",
    component: "",
    icon: "Burger",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: "/menus/menu2"
  },
  {
    menu_id: 669,
    menu_name: "菜单三",
    parent_id: 668,
    menu_type: "2",
    path: "/menus/menu3",
    name: "menu3Page",
    component: "menus/menu2",
    icon: "Chicken",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  // 外部链接
  {
    menu_id: 7,
    menu_name: "外部链接",
    parent_id: 0,
    menu_type: "1",
    path: "/link",
    name: "linkPage",
    component: "",
    icon: "Link",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: "/link/gitee"
  },
  {
    menu_id: 71,
    menu_name: "Gitee仓库",
    parent_id: 7,
    menu_type: "2",
    path: "/link/gitee",
    name: "giteePage",
    component: "link/gitee/index",
    icon: "Soccer",
    is_hide: "1",
    is_link: "https://gitee.com/BigCatHome/koi-ui",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  {
    menu_id: 72,
    menu_name: "Github仓库",
    parent_id: 7,
    menu_type: "2",
    path: "/link/github",
    name: "githubPage",
    component: "link/github/index",
    icon: "Basketball",
    is_hide: "1",
    is_link: "https://gitee.com/BigCatHome/koi-ui",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  {
    menu_id: 73,
    menu_name: "Iframe页面",
    parent_id: 7,
    menu_type: "2",
    path: "/link/iframe",
    name: "iframePage",
    component: "link/iframe/index",
    icon: "Baseball",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  // 组件示例
  {
    menu_id: 8,
    menu_name: "组件示例",
    parent_id: 0,
    menu_type: "1",
    path: "/module",
    name: "modulePage",
    component: "",
    icon: "CoffeeCup",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: "/module/tabs"
  },
  {
    menu_id: 81,
    menu_name: "标签页操作",
    parent_id: 8,
    menu_type: "2",
    path: "/module/tabs",
    name: "tabsPage",
    component: "module/tabs/index",
    icon: "Watermelon",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  },
  {
    menu_id: 82,
    menu_name: "标签页明细",
    parent_id: 8,
    menu_type: "2",
    path: "/module/tabs/detail/:id",
    name: "tabsDetailPage",
    component: "module/tabs/detail",
    icon: "Pear",
    is_hide: "1",
    is_link: "",
    is_keep_alive: "0",
    is_full: "1",
    is_affix: "1",
    redirect: ""
  }
];

export const loginUserInfo = {
  user: {
    user_id: 1,
    login_name: "YU-ADMIN",
    sex: "1",
    avatar:
      "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2Fae90b4c7-98b6-4a47-b1b3-9ee8bc71acf6%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1692146441&t=6fca60f3a0d323869b81d8fb53b5dd1b"
  },
  roles: ["YU-ADMIN", "SUPER-ADMIN"],
  buttons: [
    "system:role:search",
    "system:role:list",
    "system:role:add",
    "system:role:delete",
    "system:role:update",
    "system:role:import",
    "system:role:export"
  ],
  permissions: "*"
};

export default [
  // 获取路由接口
  {
    url: "/mock/auth/listRouters", //请求地址
    method: "get", //请求方式
    response: () => {
      // 获取请求体携带过来用户ID
      return { status: 200, msg: "路由获取成功", data: listRouters };
    }
  },
  // 获取角色数据 AND 按钮数据 AND 用户信息
  {
    url: "/mock/auth/getLoginUserInfo", //请求地址
    method: "get", //请求方式
    response: () => {
      return { status: 200, msg: "用户信息获取成功", data: loginUserInfo };
    }
  }
];
