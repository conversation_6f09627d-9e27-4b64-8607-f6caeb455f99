import Request from '@/utils/luch-request/index.js';
import { options } from './config.js';
import { isWeiXinBrowser } from "@/utils/common.js";
const http = new Request(options);
http.interceptors.request.use((config) => { // 可使用async await 做异步操作
	let token = uni.getStorageSync('token');
	if (token) {
		config.header.Authorization = 'bearer ' + token;
	}
	config.data = config.data || {};
	
	config.data.os_platform = uni.getSystemInfoSync().platform;
	config.data.os_system = uni.getSystemInfoSync().system;
	config.data.os_version = 109;
	// 额外参数
	// #ifdef APP-PLUS
	config.data.os_origin = 'APP';
	config.data.os_appv = plus.runtime.version;
	// #endif
	
	// #ifdef H5
	let iswx = isWeiXinBrowser();
	if (iswx) {
		config.data.os_origin = 'WX';
	} else {
		config.data.os_origin = 'H5';
	}
	// #endif
	
	// #ifdef MP-BAIDU
	config.data.os_origin = 'MP-BAIDU';
	// #endif
	
	// #ifdef MP-TOUTIAO
	config.data.os_origin = 'MP-TOUTIAO';
	// #endif
	
	// #ifdef MP-WEIXIN
	config.data.os_origin = 'MP-WEIXIN';
	// #endif
	
	// #ifdef MP-ALIPAY
	config.data.os_origin = 'MP-ALIPAY';
	// #endif

	// 演示custom 用处
	// if (config.custom.auth) {
	//   config.header.token = 'token'
	// }
	// if (config.custom.loading) {
	//  uni.showLoading()
	// }
	/**
	 /* 演示
	 if (!token) { // 如果token不存在，return Promise.reject(config) 会取消本次请求
	    return Promise.reject(config)
	  }
	 **/
	return config
}, config => { // 可使用async await 做异步操作
	return Promise.reject(config)
})
http.interceptors.response.use((response) => {
	// console.log('response1', response);
	/* 对响应成功做点什么 可使用async await 做异步操作*/
	// if (response.data.code !== 200) {
	// 	return Promise.reject(response) // return Promise.reject 可使promise状态进入catch
	// } // 服务端返回的状态码不等于200，则reject()

	// if (response.config.custom.verification) { // 演示自定义参数的作用
	// 	return response.data
	// }
	// console.log('response',response);
	if(response.config.custom && response.config.custom.notip){
		// console.log('response.config.custom',response);
	}else{
		if (response.data.error_code == 20001) {
			uni.showModal({
				title: '温馨提示',
				content: '登录以后才可以正常使用该程序。是否立即登录',
				confirmText: '立即登录',
				cancelText: '暂不登录',
				success: function(res) {
					if (res.confirm) {
						uni.navigateTo({
							url: '/pages/login/login'
						})
					} else if (res.cancel) {
						uni.showToast({
							title: '大部分功能需要登录才能正常使用',
							icon: 'none',
							duration: 3000
						})
					}
				}
			});
		}else if(response.data.error_code){
			uni.showToast({
				title: response.data.msg,
				icon: 'none',
				duration: 3000
			})
		}
	}
	return response.data;
}, (response) => {
	/*  对响应错误做点什么 （statusCode !== 200）*/
	console.log('response',response);
	// uni.showToast({
	// 	title: response.data.msg,
	// 	icon: 'none',
	// 	duration:3000
	// });

	return Promise.reject(response.data)
})
export default http;
