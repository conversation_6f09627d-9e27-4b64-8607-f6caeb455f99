const SEARCH_HISTORY_KEY ='guide_search_history';
const MAX_HISTORY_LENGTH = 10;

// 从本地存储获取搜索历史记录
function getSearchHistory() {
  const history = uni.getStorageSync(SEARCH_HISTORY_KEY);
  return history? history : [];
}

// 将搜索历史记录存储到本地缓存
function saveSearchHistory(searchHistory) {
  const limitedHistory = searchHistory.slice(0, MAX_HISTORY_LENGTH);
  uni.setStorageSync(SEARCH_HISTORY_KEY, limitedHistory);
}

// 查找搜索词在历史记录中的索引位置（从后往前找，以便处理多个重复时保留最后输入的在最前）
function findSearchTermIndex(searchTerm, searchHistory) {
  for (let i = searchHistory.length - 1; i >= 0; i--) {
    if (searchHistory[i] === searchTerm) {
      return i;
    }
  }
  return -1;
}

// 调整搜索历史记录顺序，将最新输入的相同记录移到最前面
function adjustSearchHistory(searchTerm, searchHistory) {
  const index = findSearchTermIndex(searchTerm, searchHistory);
  if (index > -1) {
    // 将找到的记录移到数组头部
    searchHistory.unshift(searchHistory.splice(index, 1)[0]);
  }
  return searchHistory;
}

// 添加搜索历史记录，去除重复项并调整顺序
function addSearchHistory(searchTerm, searchHistory) {
  if (searchTerm.trim()!== '') {
    // 先查找搜索词是否已存在于历史记录中
    const index = findSearchTermIndex(searchTerm, searchHistory);
    if (index === -1) {
      // 如果不存在，直接添加到头部
      searchHistory.unshift(searchTerm.trim());
    } else {
      // 如果存在，调整顺序，把最新输入的移到最前面
      searchHistory = adjustSearchHistory(searchTerm, searchHistory);
    }
    // 按照最大长度限制进行截取
    searchHistory = searchHistory.slice(0, MAX_HISTORY_LENGTH);
  }
  return searchHistory;
}

export default {
  getSearchHistory,
  saveSearchHistory,
  addSearchHistory
};