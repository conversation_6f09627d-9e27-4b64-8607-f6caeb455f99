<template>
	<view>
		<view class="card-list-wrap">
			<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback">
				<view class="card-item" v-for="(item,index) in dataList" :key="index">
					<view class="new-title" v-if="item.isReplyRead == 1 && (userInfo.user_id == item.replyUserId)">新消息</view>
					<comment-card :info="item"></comment-card>
					<view class="set-btn-wrap" v-if="userInfo.user_id == item.userId">
						<text class="del" @click="showModalDel(item)">删除</text>
					</view>
				</view>
			</mescroll-body>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex';
	import { getExchangeCommentListByUserId, delExchangeCommentByUserId,setExchangeCommentRead } from '@/api/exchange.js';
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	import commentCard from '@/pages/user/components/comment-card.vue';
	export default {
		mixins: [MescrollMixin],
		components: {
			commentCard
		},
		data() {
			return {
				total: 0,
				dataList: []
			}
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		methods: {
			initData(){
				setExchangeCommentRead({}).then(res=>{
					// console.log('setExchangeCommentRead',res);
				})
			},
			upCallback(page) {
				this._getData(page);
			},
			_getData(page) {
				let data = {
					pageNo: page.num,
					pageSize: page.size,
				}

				getExchangeCommentListByUserId(data).then(res => {
					console.log('res',res);
					let curPageData = res.data.list;
					let curPageLen = curPageData.length;
					if (page.num == 1) this.dataList = [];
					this.dataList = this.dataList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen);
					this.initData();
				}).catch(err => {
					this.mescroll.endErr()
				})
			},
			showModalDel(info){
				let that = this;
				uni.showModal({
					title: '提示',
					content: '【删除后无法恢复】是否删除该信息？',
					success: function (res) {
						if (res.confirm) {
							console.log('用户点击确定');
							that._delInfo(info);
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			_delInfo(info){
				let data = {
					exchangeCommentId: info.exchangeCommentId
				}
				delExchangeCommentByUserId(data).then(res=>{
					uni.showToast({
						title: '操作成功',
						icon:'none'
					})
					this.mescroll.resetUpScroll();
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.card-list-wrap {
		padding: 26rpx;

		.card-item {
			margin-bottom: 20rpx;
			position: relative;
			.new-title{
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				// left: 100rpx;
				top: 10rpx;
				z-index: 888;
				font-size: 32rpx;
				color: rgba(235, 9, 9, 0.9);
				font-weight: 600;
			}
		}
	}
	.set-btn-wrap{
		font-size: 28rpx;
		background-color: #fff;
		padding: 20rpx;
		display: flex;
		justify-content: flex-end;
		.del{
			padding: 10rpx 20rpx;
			color: #fff;
			background-color: #EB0909;
			border-radius: 12rpx;
		}
	}
</style>