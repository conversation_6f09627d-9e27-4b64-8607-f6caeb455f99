// 导入二次封装axios
import koi from "@/utils/axios.ts";
// 引入接口类型
// import type { ILoginParams } from "./type.ts";

// 统一管理接口
enum API {
  KOI_DYNAMIC_DATA = "/dogadmin/sysUser/getLoginUserInfo",
  LIST_PAGE = "/dogadmin/sysUser/listPage",
  GET_BY_ID = "/dogadmin/sysUser/getById",
  UPDATE = "/dogadmin/sysUser/update",
  ADD = "/dogadmin/sysUser/add",
  DELETE = "/dogadmin/sysUser/deleteById",
  BATCH_DELETE = "/dogadmin/sysUser/batchDelete",
  UPDATE_STATUE = "/dogadmin/sysUser/updateStatus",
  EXPORT_DATA = "/dogadmin/sysUser/exportExcelData",
  DOWNLOAD_TEMPLATE = "/dogadmin/sysUser/downloadExcelTemplate",
  RESET_PWD = "/dogadmin/sysUser/resetPwd",
  GET_PERSONAL_DATA = "/dogadmin/sysUser/getPersonalData",
  UPDATE_BASIC_DATA = "/dogadmin/sysUser/updateBasicData",
  UPDATE_USER_PWD = "/dogadmin/sysUser/updateUserPwd"
}
// 暴露请求函数

// 通过token查询相关用户信息、按钮权限、菜单权限数据，token必须有效
export const koiDynamicData = () => {
  return koi.get(API.KOI_DYNAMIC_DATA);
};

// 多条件分页查询数据
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "?id=" + id);
};

// 根据ID进行修改
export const update = (data: any) => {
  if (!data.postIds || data.postIds.length === 0) {
    data.postIds = [-1];
  }
  if (!data.roleIds || data.roleIds.length === 0) {
    data.roleIds = [-1];
  }
  return koi.post(API.UPDATE, data);
};

// 添加
export const add = (data: any) => {
  if (!data.postIds || data.postIds.length === 0) {
    data.postIds = [-1];
  }
  if (!data.roleIds || data.roleIds.length === 0) {
    data.roleIds = [-1];
  }
  return koi.post(API.ADD, data);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE,{ id: id });
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids});
};

// 修改状态
export const updateStatus = (id: any, status: any) => {
  return koi.post(API.UPDATE_STATUE,{id: id, status: status});
};

// 导出数据
export const exportData = (params: any) => {
  return koi.exportExcel(API.EXPORT_DATA, params);
};

// 下载模版
export const downloadTemplate = () => {
  return koi.exportExcel(API.DOWNLOAD_TEMPLATE);
};

// 重置密码
export const resetPwd = (id: any, password: any) => {
  return koi.post(API.RESET_PWD,{ id: id, password: password });
};

// 个人中心-左侧卡片资料数据
export const getPersonalData = () => {
  return koi.get(API.GET_PERSONAL_DATA);
};

// 个人中心-修改基本资料 和 头像
export const updateBasicData = (data: any) => {
  return koi.post(API.UPDATE_BASIC_DATA, data);
};

// 个人中心-修改密码
export const updateUserPwd = (data: any) => {
  return koi.post(API.UPDATE_USER_PWD, data);
};

