<template>
	<view class="container">
		<view class="tui-page-title">迷雾小助手</view>

		<!-- #ifdef H5 -->
		<view class="tui-form">
			<view class="tui-view-input">
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="mobile" color="#6d7a87" :size="20"></tui-icon>

						<input :adjust-position="false" :value="phone" placeholder="请输入手机号" placeholder-class="tui-phcolor"
							type="number" maxlength="11" @input="inputMobile" />
						<view class="tui-icon-close" v-show="phone" @tap="clearInput(1)"><tui-icon name="close-fill" :size="16"
								color="#bfbfbf"></tui-icon></view>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="pwd" color="#6d7a87" :size="20"></tui-icon>
						<input :adjust-position="false" :value="password" placeholder="请输入密码" :password="true"
							placeholder-class="tui-phcolor" type="text" maxlength="36" @input="inputPwd" />
						<view class="tui-icon-close" v-show="password" @tap="clearInput(2)"><tui-icon name="close-fill" :size="16"
								color="#bfbfbf"></tui-icon></view>
					</view>
				</tui-list-cell>
			</view>
			<view class="tui-cell-text">
				<view class="tui-color-primary" hover-class="tui-opcity" :hover-stay-time="150" @tap="href(1)">忘记密码？</view>
				<view hover-class="tui-opcity" :hover-stay-time="150">
					没有账号？
					<text class="tui-color-primary" @tap="href(2)">注册</text>
				</view>
			</view>
			<view class="tui-btn-box"><tui-button type="blue" :disabledGray="true" :disabled="disabled" :shadow="true"
					shape="circle" @click="toLogin">登录</tui-button></view>
		</view>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<view class="btn-wrap">
			<tui-button type="green" @click="weappLogin">小程序一键登录</tui-button>
			<!-- <button type="primary" class="mp-login-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
				v-if="checkProtocol">小程序一键登录</button>
			<button type="primary" class="mp-login-btn" @click="getPhoneNumber" v-else>小程序一键登录</button> -->
			
			<tui-button type="gray" margin="10px 0" @click="toPage('/pages/index/index','switchTab')">已经登陆
				回到首页</tui-button>
		</view>
		<!-- #endif -->
		<view class="protocol-wrap" @click="checkProtocol = !checkProtocol">
			
			<view class="circle" :class="{'isCheck': checkProtocol}">
				<view v-if="checkProtocol">&#x2713;</view>
			</view>
			<text class="text1">勾选代表同意，迷雾小助手 <text class="text2"
					@click.stop="toPage('/pages/user/protocol')">用户服务协议、隐私政策</text></text>
		</view>
		<!-- #ifdef MP-WEIXIN -->
		<view class="tips">登录以后记得去完善个人头像等信息</view>
		<!-- #endif -->
	</view>
</template>

<script>
	import { wxLogin, login, loginWeapp } from '@/api/login.js';
	import { mapActions } from 'vuex';
	export default {
		computed: {
			disabled() {
				let bool = true;
				if (this.phone && this.password) {
					bool = false;
				}
				return bool;
			}
		},
		data() {
			return {
				phone: '',
				password: '',
				popupShow: false,
				checkProtocol: false
			};
		},
		onLoad() {
			this.getBeforePage();
		},
		methods: {
			...mapActions({
				getUser: 'getUserInfo'
			}),
			getBeforePage() {
				let pages = getCurrentPages(); //当前页
				console.log('getBeforePage', pages)
				if (pages.length > 1) {
					let beforePage = pages[pages.length - 2]; //上个页面
					let beforePageFullPath;
					// #ifdef APP-PLUS || MP
					beforePageFullPath = beforePage.$page.fullPath;
					// #endif
					// #ifdef H5
					beforePageFullPath = beforePage.__page__.fullPath;
					// #endif
					if (beforePageFullPath[0] == '/') {
						uni.setStorageSync('beforePageFullPath', beforePageFullPath);
					} else {
						uni.setStorageSync('beforePageFullPath', '/' + beforePageFullPath);
					}

				}
			},
			toLogin() {
				let data = {
					phone: this.phone,
					password: this.password,
				}
				login(data).then(res => {
					if (res.code == 0) {
						uni.setStorageSync('token', res.data);
						this.getUser().then(res => {
							uni.reLaunch({
								url: uni.getStorageSync('beforePageFullPath') ||
									'/pages/index/index'
							})
						});
					}
				})
			},
			back() {
				uni.navigateBack();
			},
			inputMobile(e) {
				this.phone = e.detail.value;
			},
			inputPwd(e) {
				this.password = e.detail.value;
			},
			clearInput(type) {
				if (type == 1) {
					this.phone = '';
				} else {
					this.password = '';
				}
			},
			href(type) {
				let url = '/pages/login/forgetPwd';
				if (type == 2) {
					url = '/pages/login/reg';
				}
				uni.navigateTo({
					url: url
				})
			},
			showOtherLogin() {
				//打开后 不再关闭
				this.popupShow = true;
			},
			// #ifdef MP-WEIXIN
			getPhoneNumber(e) {
				if (!this.checkProtocol) {
					uni.showToast({
						title: '请勾选同意用户协议和隐私政策',
						icon: 'none'
					})
					return false;
				}
				console.log('getPhoneNumber', e);
				if (e.detail.errMsg == "getPhoneNumber:ok") {
					let data = {
						phone_code: e.detail.code,
						iv: e.detail.iv,
						encryptedData: e.detail.encryptedData
					}
					uni.login({
						provider: 'weixin',
						success: (loginRes) => {
							data.code = loginRes.code;
							uni.showLoading({
								title: '登录中...'
							})
							loginWeapp(data).then(ret => {
								console.log('loginWeapp', ret);
								// 3.用token获取用户信息
								uni.setStorageSync('token', ret.data);
								uni.hideLoading();
								that.getUser().then(res => {
									uni.reLaunch({
										url: uni.getStorageSync('beforePageFullPath') || '/pages/index/index'
									})
								}).catch(err => {
									uni.showToast({
										title: '出错了，请重试',
										icon: 'none'
									})
								});
							})
						},
						fail: (err) => {
							uni.showToast({
								title: '出错了，请重试',
								icon: 'none'
							})
						}
					});
				} else {
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'none'
					})
				}
			},
			weappLogin(e) {
				if (!this.checkProtocol) {
					uni.showToast({
						title: '请勾选同意用户协议和隐私政策',
						icon: 'none'
					})
					return false;
				}
				let that = this;
				wx.getUserProfile({
					desc: '用于注册登录获取信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (res) => {
						// 2.在获取code登录
						console.log('getUserProfile', res);
						uni.login({
							provider: 'weixin',
							success: (loginRes) => {
								let data = {
									iv: res.iv,
									encryptedData: res.encryptedData,
									code: loginRes.code,
									username: res.userInfo.nickName,
									avatar_url: res.userInfo.avatarUrl,
									userInfo: res.userInfo,
									gender: res.userInfo.gender
								}
								uni.showLoading({
									title: '登录中...'
								})
								loginWeapp(data).then(ret => {
									console.log('loginWeapp', ret);
									// 3.用token获取用户信息
									uni.setStorageSync('token', ret.data.token);
									uni.setStorageSync('tokenExpiration', ret.data.exptime);
									uni.hideLoading();
									that.getUser().then(res => {
										console.log('getUser',res);
										uni.reLaunch({
											url: uni.getStorageSync('beforePageFullPath') || '/pages/index/index'
										})
									}).catch(err => {
										uni.showToast({
											title: '出错了，请重试',
											icon: 'none'
										})
									});
								})
							},
							fail: (err) => {
								uni.showToast({
									title: '出错了，请重试',
									icon: 'none'
								})
							}
						});
					},
					fail: (err) => {
						uni.showToast({
							title: '出错了，请重试',
							icon: 'none'
						})
					}
				})
			},
			// #endif
		}
	};
</script>

<style lang="scss">
	.container {
		background-color: #fff;

		.tui-status-bar {
			width: 100%;
			height: var(--status-bar-height);
		}

		.tui-header {
			width: 100%;
			padding: 40rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-sizing: border-box;
		}

		.tui-page-title {
			width: 100%;
			font-size: 48rpx;
			font-weight: bold;
			color: $uni-text-color;
			line-height: 42rpx;
			padding: 40rpx;
			box-sizing: border-box;
		}

		.tui-form {
			padding-top: 20rpx;

			.tui-view-input {
				width: 100%;
				box-sizing: border-box;
				padding: 0 40rpx;

				.tui-cell-input {
					width: 100%;
					display: flex;
					align-items: center;
					padding-top: 48rpx;
					padding-bottom: $uni-spacing-col-base;
					border-bottom: 1px solid $uni-text-color-disable;

					input {
						flex: 1;
						padding-left: $uni-spacing-row-base;
					}

					.tui-icon-close {
						margin-left: auto;
					}
				}
			}

			.tui-cell-text {
				width: 100%;
				padding: $uni-spacing-col-lg $uni-spacing-row-lg;
				box-sizing: border-box;
				font-size: $uni-font-size-sm;
				color: $uni-text-color-grey;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.tui-color-primary {
					color: $uni-color-primary;
				}
			}

			.tui-btn-box {
				width: 100%;
				padding: 0 $uni-spacing-row-lg;
				box-sizing: border-box;
				margin-top: 80rpx;
			}
		}

		.tui-login-way {
			width: 100%;
			font-size: 26rpx;
			color: $uni-color-primary;
			display: flex;
			justify-content: center;
			position: fixed;
			left: 0;
			bottom: 80rpx;

			view {
				padding: 12rpx 0;
			}
		}

		.tui-auth-login {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			padding-bottom: 80rpx;
			padding-top: 20rpx;

			.tui-icon-platform {
				width: 90rpx;
				height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				margin-left: 40rpx;

				&::after {
					content: '';
					position: absolute;
					width: 200%;
					height: 200%;
					transform-origin: 0 0;
					transform: scale(0.5, 0.5) translateZ(0);
					box-sizing: border-box;
					left: 0;
					top: 0;
					border-radius: 180rpx;
					border: 1rpx solid $uni-text-color-placeholder;
				}
			}

			.tui-login-logo {
				width: 60rpx;
				height: 60rpx;
			}
		}
	}

	.tips {
		text-align: center;
		font-size: 30rpx;
		font-weight: 600;
		color: $uni-color-error;
		padding: 20rpx 0;
	}

	.mp-login-btn-tips {
		margin-top: 40rpx;
		font-size: 32rpx;
	}

	.protocol-wrap {
		// padding: 0 20rpx;
		padding-left: 30rpx;
		display: flex;
		align-items: center;
		color: #666;

		.circle {
			border: 1px solid #666;
			border-radius: 50%;
			height: 36rpx;
			width: 36rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #fff;
		}

		.isCheck {
			border: 1px solid #1aad19;
			background-color: #1aad19;
		}

	
		.text1 {
			padding-left: 10rpx;
			font-size: 28rpx;
		}

		.text2 {
			color: #009DFF;
			font-size: 28rpx;
		}
	}
</style>