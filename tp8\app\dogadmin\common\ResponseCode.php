<?php

namespace app\dogadmin\common;

class ResponseCode
{
    // 成功
    const SUCCESS = 1;

    // 通用错误
    const ERROR = 0;

    // 未登录
    const NOT_LOGIN = 401;

    // 无权限
    const NO_PERMISSION = 403;

    // 系统错误
    const SYSTEM_ERROR = 500;

    // 参数错误
    const PARAM_ERROR = 400;

    // 业务错误
    const BUSINESS_ERROR = 1000;

    /**
     * 获取错误信息
     * @param int $code 错误码
     * @return string
     */
    public static function getMessage(int $code): string
    {
        $messages = [
            self::SUCCESS => '操作成功',
            self::ERROR => '操作失败',
            self::NOT_LOGIN => '请先登录',
            self::NO_PERMISSION => '无权限访问',
            self::SYSTEM_ERROR => '系统错误',
            self::PARAM_ERROR => '参数错误',
            self::BUSINESS_ERROR => '业务处理失败',
        ];

        return $messages[$code] ?? '未知错误';
    }
}