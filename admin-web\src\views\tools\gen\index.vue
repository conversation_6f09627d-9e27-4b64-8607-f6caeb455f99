<template>
  <div class="koi-flex">
    <KoiCard>
      <!-- 搜索条件 -->
      <el-form v-show="showSearch" :inline="true">
        <el-form-item label="表名称" prop="tableName">
          <el-input
            placeholder="请输入表名称"
            v-model="searchParams.tableName"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item label="表类名" prop="className">
          <el-input
            placeholder="请输入表类名"
            v-model="searchParams.className"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item label="表描述" prop="tableComment">
          <el-input
            placeholder="请输入表描述"
            v-model="searchParams.tableComment"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleListPage"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch()" v-auth="['system:notice:search']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch()">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格头部按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="primary" icon="Download" plain @click="handleGenPerfectCode()" :disabled="multiple"
            >生成代码</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" icon="BottomRight" plain @click="handleImport()">导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" icon="edit" plain @click="handleUpdate()" :disabled="single">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete()" :disabled="multiple">删除</el-button>
        </el-col>
        <KoiToolbar v-model:showSearch="showSearch" @refreshTable="handleListPage"></KoiToolbar>
      </el-row>

      <div class="h-20px"></div>
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        border
        :data="tableList"
        empty-text="暂时没有数据哟🌻"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" prop="tableId" width="80px" align="center" type="index"></el-table-column>
        <el-table-column
          label="数据库名"
          prop="tableSchema"
          width="180px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="表名"
          prop="tableName"
          width="180px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column label="表类名" prop="className" width="180px" align="center"></el-table-column>
        <el-table-column label="权限字符" prop="tableAuth" width="180px" align="center"></el-table-column>
        <el-table-column
          label="表描述"
          prop="tableComment"
          width="220px"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column label="创建时间" prop="create_time" width="180px" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="240" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="预览🌻" placement="top">
              <el-button type="info" icon="View" circle plain @click="handlePreviewCode(row)"></el-button>
            </el-tooltip>
            <el-tooltip content="修改🌻" placement="top">
              <el-button type="primary" icon="Edit" circle plain @click="handleUpdate(row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button type="danger" icon="Delete" circle plain @click="handleDelete(row)"></el-button>
            </el-tooltip>
            <el-tooltip content="同步🌻" placement="top">
              <el-button type="warning" icon="Refresh" circle plain @click="handleSyncData(row)"></el-button>
            </el-tooltip>
            <el-tooltip content="生成代码🌻" placement="top">
              <el-button type="primary" icon="Download" circle plain @click="handleGenPerfectCode(row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="h-20px"></div>
      <!-- {{ searchParams.page_no }} --- {{ searchParams.page_size }} -->
      <!-- 分页 -->
      <el-pagination
        background
        v-model:current-page="searchParams.page_no"
        v-model:page-size="searchParams.page_size"
        v-show="total > 0"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleListPage"
        @current-change="handleListPage"
      />

      <KoiDialog
        ref="koiDialogImportRef"
        :title="title"
        top="6vh"
        :width="860"
        :height="560"
        @koiConfirm="handleImportConfirm"
        @koiCancel="handleImportCancel"
        :loading="confirmLoading"
      >
        <template #content>
          <!-- 搜索条件 -->
          <el-form :inline="true">
            <el-form-item label="表名称" prop="tableName">
              <el-input
                placeholder="请输入表名称"
                v-model="dialogSearchParams.tableName"
                clearable
                style="width: 220px"
                @keyup.enter.native="handleDialogListPage"
              ></el-input>
            </el-form-item>
            <el-form-item label="数据库名" prop="tableSchema">
              <el-input
                placeholder="请输入数据库名"
                v-model="dialogSearchParams.tableSchema"
                clearable
                style="width: 220px"
                @keyup.enter.native="handleDialogListPage"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="search" plain @click="handleDialogSearch()">搜索</el-button>
              <el-button type="danger" icon="refresh" plain @click="resetDialogSearch()">重置</el-button>
            </el-form-item>
          </el-form>
          <!-- 数据表格 -->
          <el-table
            v-loading="dialogLoading"
            border
            :data="dialogTableList"
            :height="400"
            empty-text="暂时没有数据哟🌻"
            @selection-change="handleDialogSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="序号" prop="tableName" width="80px" align="center" type="index"></el-table-column>
            <el-table-column
              label="表名"
              prop="tableName"
              width="180px"
              align="center"
              :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column
              label="表描述"
              prop="tableComment"
              width="220px"
              align="center"
              :show-overflow-tooltip="true"
            ></el-table-column>
          </el-table>
          <div class="h-20px"></div>
          <!-- {{ dialogSearchParams.page_no }} --- {{ dialogSearchParams.page_size }} -->
          <!-- 分页 -->
          <el-pagination
            background
            v-model:current-page="dialogSearchParams.page_no"
            v-model:page-size="dialogSearchParams.page_size"
            v-show="dialogTotal > 0"
            :page-sizes="[10, 20, 50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="dialogTotal"
            @size-change="handleDialogListPage"
            @current-change="handleDialogListPage"
          />
        </template>
      </KoiDialog>

      <KoiDialog
        ref="koiDialogPreviewRef"
        :title="title"
        :width="1000"
        :height="600"
        :loading="confirmLoading"
        :footerHidden="true"
      >
        <template #content>
          <el-tabs v-model="activeName">
            <el-tab-pane label="pojo" name="pojo">
              <CopyDocument :size="5" v-copy="previewCode.pojo" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.pojo }}</pre>
            </el-tab-pane>
            <el-tab-pane label="vo" name="vo">
              <CopyDocument :size="5" v-copy="previewCode.vo" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.vo }}</pre>
            </el-tab-pane>
            <el-tab-pane label="bo" name="bo">
              <CopyDocument :size="5" v-copy="previewCode.bo" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.bo }}</pre>
            </el-tab-pane>
            <el-tab-pane label="mapper" name="mapper">
              <CopyDocument :size="5" v-copy="previewCode.mapper" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.mapper }}</pre>
            </el-tab-pane>
            <el-tab-pane label="mapperXml" name="mapperXml">
              <CopyDocument :size="5" v-copy="previewCode.mapperXml" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.mapperXml }}</pre>
            </el-tab-pane>
            <el-tab-pane label="service" name="service">
              <CopyDocument :size="5" v-copy="previewCode.service" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.service }}</pre>
            </el-tab-pane>
            <el-tab-pane label="serviceImpl" name="serviceImpl">
              <CopyDocument :size="5" v-copy="previewCode.serviceImpl" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.serviceImpl }}</pre>
            </el-tab-pane>
            <el-tab-pane label="controller" name="controller">
              <CopyDocument :size="5" v-copy="previewCode.controller" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.controller }}</pre>
            </el-tab-pane>
            <el-tab-pane label="api" name="api">
              <CopyDocument :size="5" v-copy="previewCode.api" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.api }}</pre>
            </el-tab-pane>
            <el-tab-pane label="vue" name="vue">
              <CopyDocument :size="5" v-copy="previewCode.vue" style="float: right"></CopyDocument>
              <pre class="text-#303133 dark:text-#cfd3dc">{{ previewCode.vue }}</pre>
            </el-tab-pane>
          </el-tabs>
        </template>
      </KoiDialog>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="genPage">
import { ref, onMounted } from "vue";
import {
  koiMsgSuccess,
  koiNoticeSuccess,
  koiNoticeError,
  koiMsgError,
  koiMsgWarning,
  koiMsgBox,
  koiMsgInfo
} from "@/utils/koi.ts";
import {
  listPage,
  listTableNamePage,
  importGenTable,
  deleteById,
  batchDelete,
  synchronousData,
  previewPerfectCode,
  genPerfectCode
} from "@/api/tools/gen/index.ts";
import { ElLoading } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();
// 数据表格加载页面动画
const loading = ref(false);
/** 是否显示搜索表单 */
const showSearch = ref<boolean>(true); // 默认显示搜索条件
// 数据表格数据
const tableList = ref<any>([]);

// 查询参数
const searchParams = ref({
  page_no: 1, // 第几页
  page_size: 10, // 每页显示多少条
  tableName: "",
  className: "",
  tableComment: ""
});
const total = ref<number>(0);
// 重置搜索参数
const resetSearchParams = () => {
  searchParams.value = {
    page_no: 1,
    page_size: 10,
    tableName: "",
    className: "",
    tableComment: ""
  };
};
/** 搜索 */
const handleSearch = () => {
  console.log("搜索");
  searchParams.value.page_no = 1;
  handleTableData();
};
/** 重置 */
const resetSearch = () => {
  console.log("重置搜索");
  resetSearchParams();
  handleListPage();
};

/** @current-change：点击分页组件页码发生变化：例如：切换第2、3页 OR 上一页 AND 下一页 OR 跳转某一页 */
/** @size-change：点击分页组件下拉选中条数发生变化：例如：选择10条/页、20条/页等 */
// 分页查询，@current-change AND @size-change都会触发分页，调用后端分页接口
/** 数据表格 */
const handleListPage = async () => {
  try {
    tableList.value = []; // 重置表格数据
    loading.value = true;
    const res: any = await listPage(searchParams.value);
    tableList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

/** 数据表格[不带Loading，删除、批量删除等使用] */
const handleTableData = async () => {
  try {
    const res: any = await listPage(searchParams.value);
    tableList.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

onMounted(() => {
  // 获取数据表格数据
  handleListPage();
});

const ids = ref<any>(); // 选中数组
const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
const tableSchemas = ref<any>(); // 选中数组
const tableNames = ref<any>(); // 选中数组
const genCodeList = ref<any>([]); // 数据库名和表名集合
/** 是否多选 */
const handleSelectionChange = (selection: any) => {
  ids.value = selection.map((item: any) => item.tableId);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
  tableNames.value = selection.map((item: any) => item.tableName);
  tableSchemas.value = selection.map((item: any) => item.tableSchema);
  genCodeList.value = selection.map((item: any) => {
    const genCodeObject = {
      tableSchema: item.tableSchema,
      tableName: item.tableName
    };
    return genCodeObject;
  });
};

/** 导入 */
const handleImport = () => {
  // 标题
  title.value = "导入表";
  koiDialogImportRef.value.koiOpen();
  handleDialogListPage();
  koiMsgSuccess("导入🌻");
};

/** 导入查询表格 */
// 查询参数
const dialogSearchParams = ref({
  page_no: 1, // 第几页
  page_size: 10, // 每页显示多少条
  tableName: "",
  tableSchema: ""
});
// 重置搜索参数
const resetDialogSearchParams = () => {
  dialogSearchParams.value = {
    page_no: 1, // 第几页
    page_size: 10, // 每页显示多少条
    tableName: "",
    tableSchema: ""
  };
};

/** 搜索 */
const handleDialogSearch = () => {
  dialogSearchParams.value.page_no = 1;
  handleDialogListPage();
};
/** 重置 */
const resetDialogSearch = () => {
  resetDialogSearchParams();
  handleDialogListPage();
};

const dialogTotal = ref<number>(0);
const dialogLoading = ref(false);
const dialogTableList = ref<any>();
/** 数据表格 */
const handleDialogListPage = async () => {
  try {
    dialogTableList.value = []; // 重置表格数据
    dialogLoading.value = true;
    const res: any = await listTableNamePage(dialogSearchParams.value);
    dialogTableList.value = res.data.records;
    dialogTotal.value = res.data.total;
    dialogLoading.value = false;
  } catch (error) {
    console.log(error);
    koiNoticeError("数据查询失败，请刷新重试🌻");
  }
};

/** 导入抽屉开始 */
const koiDialogImportRef = ref();
const tableNameList = ref();
/** 复选框选中值 */
const handleDialogSelectionChange = (selection: any) => {
  tableNameList.value = selection.map((item: any) => item.tableName);
};

// 确定按钮是否显示loading
const confirmLoading = ref(false);
/** 确定  */
const handleImportConfirm = async () => {
  if (tableNameList.value == null || tableNameList.value.length == 0) {
    koiMsgWarning("请选中导入的数据🌻");
  }
  try {
    confirmLoading.value = true;
    const params = {
      tableNameList: tableNameList.value,
      tableSchema: dialogSearchParams.value.tableSchema
    };
    await importGenTable(params);
    confirmLoading.value = false;
    koiDialogImportRef.value.koiQuickClose();
    handleTableData();
    koiNoticeSuccess("导入成功🌻");
  } catch (error) {
    console.log(error);
    confirmLoading.value = false;
    koiNoticeError("导入失败，请刷新重试🌻");
  }
};

/** 取消 */
const handleImportCancel = () => {
  koiDialogImportRef.value.koiClose();
};
/** 导入抽屉结束 */

/** 修改生成代码配置 */
// 标题
const title = ref("代码生成");
/** 修改 */
const handleUpdate = async (row?: any) => {
  const tableSchema = row?.tableSchema || tableSchemas.value[0];
  const tableName = row?.tableName || tableNames.value[0];
  if (tableName == null || tableName == "" || tableSchema == null || tableSchema == "") {
    koiMsgError("请选中需要修改的数据🌻");
    return;
  }
  router.push("/tools/gen/config/" + tableSchema + "/" + tableName);
};

/** 删除 */
const handleDelete = (row: any) => {
  const id = row.tableId;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认需要删除公告名称[" + row.tableName + "]么？")
    .then(async () => {
      try {
        await deleteById(id);
        handleTableData();
        koiNoticeSuccess("删除成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 批量删除 */
const handleBatchDelete = () => {
  if (ids.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认需要进行批量删除么？")
    .then(async () => {
      try {
        await batchDelete(ids.value);
        handleTableData();
        koiNoticeSuccess("批量删除成功🌻");
      } catch (error) {
        console.log(error);
        handleTableData();
        koiNoticeError("批量删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 同步数据 */
const handleSyncData = (row: any) => {
  if (!row || !row?.tableName || !row?.tableSchema) {
    koiMsgWarning("请选择需要同步的数据🌻");
    return;
  }
  koiMsgBox("您确认需要进行同步么？")
    .then(async () => {
      const loading = ElLoading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      try {
        await synchronousData(row.tableSchema, row.tableName);
        loading.close();
        koiNoticeSuccess("同步成功🌻");
        handleTableData();
      } catch (error) {
        console.log(error);
        loading.close();
        handleTableData();
        koiNoticeError("同步失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

/** 生成代码 */
const handleGenPerfectCode = async (row?: any) => {
  let genCodes: any = [];
  if (row) {
    const genCodeObject = {
      tableSchema: row?.tableSchema,
      tableName: row?.tableName
    };
    genCodes.push(genCodeObject);
  }
  if (genCodes.length == 0 && genCodeList.value.length == 0) {
    koiMsgWarning("请选中需要的数据🌻");
    return;
  }
  koiMsgBox("您确认生成代码么？")
    .then(async () => {
      const loading = ElLoading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      try {
        let response: any;
        // 代码生成
        if (genCodes.length != 0) {
          genCodeList.value = [];
          response = await genPerfectCode("gen", genCodes);
        }
        // 批量生成代码
        if (genCodeList.value.length != 0) {
          response = await genPerfectCode("gen", genCodeList.value);
        }
        try {
          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', 'koi.zip'); // 设置下载文件名
          document.body.appendChild(link);
          link.click();
        } catch (error) {
          console.log(error);
          koiNoticeError("代码生成失败，请刷新重试🌻");
        }
        loading.close();
        koiNoticeSuccess("代码生成成功🌻");
      } catch (error) {
        console.log(error);
        loading.close();
        handleTableData();
        koiNoticeError("代码生成失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

const koiDialogPreviewRef = ref();
const previewCode = ref();
/** 预览代码 */
const handlePreviewCode = async (row?: any) => {
  // 标题
  title.value = "预览代码";
  let previewCodes: any = [];
  if (row) {
    const previewCodeObject = {
      tableSchema: row?.tableSchema,
      tableName: row?.tableName
    };
    previewCodes.push(previewCodeObject);
  }
  if (previewCodes.length == 0) {
    koiMsgWarning("请选中需要的数据🌻");
    return;
  }
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });
  try {
    // 代码生成
    if (previewCodes.length != 0) {
      previewCode.value = "";
      const res: any = await previewPerfectCode("preview", previewCodes);
      previewCode.value = res.data;
      koiDialogPreviewRef.value.koiOpen();
      koiNoticeSuccess("代码预览成功🌻");
    }
    loading.close();
  } catch (error) {
    console.log(error);
    loading.close();
    handleTableData();
    koiNoticeError("代码预览失败，请刷新重试🌻");
  }
};

// 获取点击选中的tab的name属性
const activeName = ref("pojo");
</script>

<style lang="scss" scoped></style>
