// 导入二次封装axios
import koi from "@/utils/axios.ts";

// 统一管理接口
enum API {
  LIST_PAGE = "/dogadmin/sysFile/listPage",
  GET_BY_ID = "/dogadmin/sysFile/getById",
  DELETE = "/dogadmin/sysFile/deleteById",
  BATCH_DELETE = "/dogadmin/sysFile/batchDelete"
}

// 多条件分页查询
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "?fileId=" + id);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE, { fileId: id });
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids});
};
