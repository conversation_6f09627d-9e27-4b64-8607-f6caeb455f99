<?php

namespace app\{{module_name}}\controller;

use app\{{module_name}}\service\{{controller_name}}Service;
use app\dogadmin\common\ApiResponse;
use app\dogadmin\controller\Base;

/**
 * {{table_comment}} 控制器
 */
class {{controller_name}} extends Base
{
    /**
     * @var {{controller_name}}Service
     */
    protected $service;
    
    /**
     * 初始化方法
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = new {{controller_name}}Service();
        $this->params = $this->request->param();
        $this->searchKey = [
{{search_key_content}}
        ];
    }
    
    // 所有基础CRUD方法均继承自Base控制器
     // 如需自定义方法，请在此处添加
}