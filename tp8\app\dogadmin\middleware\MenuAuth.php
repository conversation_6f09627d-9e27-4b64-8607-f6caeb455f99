<?php

namespace app\dogadmin\middleware;

use app\dogadmin\common\ApiResponse;
use app\dogadmin\service\SysMenuService;
use app\Request;
use Closure;

class MenuAuth
{
    public function handle(Request $request, Closure $next)
    {
        $adminId = $request->admin_id;
        if(!$adminId){
            return ApiResponse::noPermission('权限错误');
        }
        $roleIds = $request->role_ids;
        if(!$roleIds){
            return ApiResponse::noPermission('权限错误');
        }
        $passMenu = (new SysMenuService())->getButtonsByRoleIds($roleIds);
        if(empty($passMenu)){
            return ApiResponse::noPermission('权限错误');
        }
        if ($passMenu == ['*']){
            return $next($request);
        }
        $module = app('http')->getName();// 获取当前应用（模块）
        //手动解析控制器和方法
        $path = $request->pathinfo();
        // 先处理连续的斜杠，将多个斜杠替换为单个斜杠
        $path = preg_replace('#/{2,}#', '/', $path);
        // 处理路径，将所有斜杠替换为冒号
        $pathFormatted = str_replace('/', ':', trim($path, '/'));
        // 处理可能存在的连续冒号（由连续斜杠转换而来）
        $pathFormatted = preg_replace('/:+/', ':', $pathFormatted);
        
        // 如果路径为空，则使用默认值
        if (empty($pathFormatted)) {
            $pathFormatted = 'index:index';
        }
        
        // 组合模块和路径
        $useAuth = $module.':'.$pathFormatted;

        if(!in_array($useAuth,$passMenu)){
            return ApiResponse::noPermission('权限错误');
        }
        return $next($request);
    }
}