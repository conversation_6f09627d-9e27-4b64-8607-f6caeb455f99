<?php
declare(strict_types=1);

namespace app\lib\service\storage;

use app\lib\service\storage\QiniuStorageService;
use app\lib\service\storage\TencentCosStorageService;
use app\lib\service\storage\AliyunOssStorageService;
use app\lib\service\storage\CloudStorageService;
use app\lib\exception\dogadmin\FileException;
use think\facade\Config;

/**
 * 云存储工厂类
 * 用于创建不同类型的云存储服务实例
 */
class CloudStorageFactory
{
    /**
     * 支持的存储类型
     * @var array
     */
    private static array $supportedTypes = [
        'qiniu' => QiniuStorageService::class,
        'tencent_cos' => TencentCosStorageService::class,
        'aliyun_oss' => AliyunOssStorageService::class,
    ];
    
    /**
     * 存储服务实例缓存
     * @var array
     */
    private static array $instances = [];
    
    /**
     * 创建云存储服务实例
     * 
     * @param string $type 存储类型
     * @param array $config 配置信息
     * @param bool $mergeConfig 是否合并配置文件中的配置
     * @return CloudStorageService
     * @throws FileUpload
     */
    public static function create(string $type, array $config = [], bool $mergeConfig = false): CloudStorageService
    {
        if (!isset(self::$supportedTypes[$type])) {
            throw new FileException([
                'type' => $type,
                'supported' => array_keys(self::$supportedTypes)
            ], 7); // 7 = 服务类型不支持
        }
        
        $finalConfig = [];
        
        // 处理配置的三种情况
        if (empty($config)) {
            // 1. 没有传入配置，完全使用配置文件
            $finalConfig = self::getConfigFromFile($type);
        } else if ($mergeConfig) {
            // 2. 传入配置并合并，优先使用传入的配置
            try {
                $fileConfig = self::getConfigFromFile($type, false);
                $finalConfig = array_merge($fileConfig, $config);
            } catch (FileException $e) {
                // 如果配置文件中没有配置，则只使用传入的配置
                $finalConfig = $config;
            }
        } else {
            // 3. 只使用传入的配置
            $finalConfig = $config;
        }
        
        $cacheKey = $type . '_' . md5(serialize($finalConfig));
        
        if (!isset(self::$instances[$cacheKey])) {
            $className = self::$supportedTypes[$type];
            self::$instances[$cacheKey] = new $className($finalConfig);
        }
        
        return self::$instances[$cacheKey];
    }
    
    /**
     * 从配置文件获取配置
     * 
     * @param string $type 存储类型
     * @param bool $throwException 是否在配置不存在时抛出异常
     * @return array
     * @throws FileUpload
     */
    private static function getConfigFromFile(string $type, bool $throwException = true): array
    {
        $config = Config::get('cloud_storage.' . $type, []);
        
        if (empty($config) && $throwException) {
            throw new FileException([
                'message' => "云存储配置不存在: {$type}",
                'type' => $type
            ], 4); // 4 = 配置错误
        }
        
        return $config;
    }
    
    /**
     * 根据文件服务类型创建存储服务
     * 
     * @param string $fileService 文件服务类型 (1-LOCAL，2-MINIO，3-OSS)
     * @param array $config 配置信息
     * @return CloudStorageService|null
     */
    public static function createByFileService(string $fileService, array $config = []): ?CloudStorageService
    {
        $typeMapping = [
            '2' => 'qiniu',      // MINIO 映射到七牛云
            '3' => 'aliyun_oss', // OSS 映射到阿里云OSS
            '4' => 'tencent_cos' // 新增腾讯云COS
        ];
        
        if (!isset($typeMapping[$fileService])) {
            return null;
        }
        
        return self::create($typeMapping[$fileService], $config);
    }
    
    /**
     * 获取支持的存储类型列表
     * 
     * @return array
     */
    public static function getSupportedTypes(): array
    {
        return array_keys(self::$supportedTypes);
    }
    
    /**
     * 检查存储类型是否支持
     * 
     * @param string $type 存储类型
     * @return bool
     */
    public static function isSupported(string $type): bool
    {
        return isset(self::$supportedTypes[$type]);
    }
    
    /**
     * 清除实例缓存
     */
    public static function clearCache(): void
    {
        self::$instances = [];
    }
    
    /**
     * 获取默认存储服务
     * 
     * @return CloudStorageService
     * @throws FileUpload
     */
    public static function getDefault(): CloudStorageService
    {
        $defaultType = Config::get('cloud_storage.default', 'qiniu');
        return self::create($defaultType);
    }
}