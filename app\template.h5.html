<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta name="referrer" content="never">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta http-equiv="pragram" content="no-cache">
		<meta http-equiv="cache-control" content="no-cache,no-store, must-revalidate">
		<meta http-equiv="expires" content="0">
		<meta name="keywords" content="基础">
		<meta name="description" content="基础">
		
		<meta name="viewport"
			content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
		<title>
			<%= htmlWebpackPlugin.options.title %>
		</title>
		<style>
			::-webkit-scrollbar {
				display: none;
			}
			body{
				background-color: #111111;
			}
			.html-tip {
				width: 100%;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #fff;
				font-size: 18px;
				text-align: center;
			}
			.html-btn{
				width: 100%;
				position: absolute;
				top: 60%;
				left: 50%;
				transform: translate(-50%, -50%);
				background-color: #111111;
				color: #FFFFFF;
				width: 138px;
				height: 50px;
				line-height: 50px;
				font-size: 20px;
				text-align: center;
				border-radius: 8px;
			}
			.html-title{
				width: 100%;
				position: absolute;
				top: 35%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #fff;
				font-size: 28px;
				text-align: center;
				font-weight: 600;
			}
			.html-progress {
			  width:268px;
			  height:20px;
			  border-radius: 20px;
				position: absolute;
				top: 45%;
				left: 50%;
				transform: translate(-50%, -50%);
			  background:
			   repeating-linear-gradient(135deg,#111111 0 10px,#FDD095 0 20px) 0/0% no-repeat,
			   repeating-linear-gradient(135deg,#ddd 0 10px,#eee 0 20px) 0/100%;
			  animation:p3 2s infinite;
			}
			@keyframes p3 {
			    100% {background-size:100%}
			}
			
		</style>
		<!-- 正式发布的时候使用，开发期间不启用。↑ -->
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
			})
		</script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.css" />
	</head>
	<body>
		<noscript>
			<strong>本站点必须要开启JavaScript才能运行</strong>
		</noscript>
		<div id="app">
			<div class="html-title">基础</div>
			<div class="html-progress"></div>
			<div class="html-tip">如果有问题，请点击刷新</div>
			<div class="html-btn" onclick="location.reload()">刷新</div>
		</div>
		<!-- built files will be auto injected -->
		<script>
			/*BAIDU_STAT*/
		</script>
		<div style="display: none">
			<!-- <script src="https://map.qq.com/api/gljs?v=1.exp&key=PZSBZ-3RXRU-NCVV4-BKIQS-YDW5Z-ZVFB4"></script> -->
			<script src="<%= BASE_URL %>static/vconsole.min.js"></script>
			<script src="<%= BASE_URL %>static/quill.min.js"></script>
			<script src="<%= BASE_URL %>static/image-resize.min.js"></script>
			<script>
				
				function getQueryVariable(variable) {
					let query = window.location.search.substring(1);
					let vars = query.split("&");
					for (let i = 0; i < vars.length; i++) {
						let pair = vars[i].split("=");
						if (pair[0] == variable) {
							return pair[1];
						}
					}
					return false;
				}
				if(getQueryVariable('debug') == 1){
					var vConsole = new window.VConsole();
				}
			</script>
		</div>
	</body>
</html>
