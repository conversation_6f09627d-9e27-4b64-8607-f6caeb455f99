@echo off
setlocal enabledelayedexpansion

:: 获取当前脚本所在目录
set "SCRIPT_DIR=%~dp0"

:: 获取第一个参数作为命令
set "COMMAND=%~1"

:: 如果没有提供命令，显示帮助信息
if "%COMMAND%"=="" (
    echo Dog Tools 使用帮助：
    echo.
    echo   dog build ^<模块名^>            - 创建模块目录结构
    echo   dog make:model ^<模块名/类名^>     - 创建模型类
    echo   dog make:controller ^<模块名/类名^> - 创建控制器类
    echo   dog make:service ^<模块名/类名^>    - 创建服务类
    echo.
    goto :end
)

:: 处理build命令
if "%COMMAND%"=="build" (
    php "%SCRIPT_DIR%build.php" %2
    goto :end
)

:: 处理make命令
if "%COMMAND:~0,5%"=="make:" (
    set "TYPE=%COMMAND:~5%"
    php "%SCRIPT_DIR%make.php" !TYPE! %2
    goto :end
)

:: 如果命令不匹配，显示错误信息
echo 未知命令: %COMMAND%
echo 使用 dog 命令查看帮助信息

:end
endlocal