// 导入二次封装axios
import koi from "@/utils/axios.ts";

// 统一管理接口
enum API {
  LIST_PAGE = "/dogadmin/sysDept/listPage",
  CASCADER_LIST = "/dogadmin/sysDept/cascaderList",
  GET_BY_ID = "/dogadmin/sysDept/getById",
  UPDATE = "/dogadmin/sysDept/update",
  ADD = "/dogadmin/sysDept/add",
  DELETE = "/dogadmin/sysDept/deleteById",
  BATCH_DELETE = "/dogadmin/sysDept/batchDelete",
  UPDATE_STATUE = "/dogadmin/sysDept/updateStatus",
  UPDATE_SPREAD = "/dogadmin/sysDept/updateSpread",
  LIST_DEPT_NORMAL = "/dogadmin/sysDept/listDeptNormal",
  LIST_DEPTIDS_BY_ROLEID = "/dogadmin/sysDept/listDeptIdsByRoleId",
  SAVE_ROLE_DEPT = "/dogadmin/sysDept/saveRoleDept"
}

// 多条件分页查询
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 部门级联下拉框
export const cascaderList = () => {
  return koi.get(API.CASCADER_LIST);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "?id=" + id);
};

// 根据ID进行修改
export const update = (data: any) => {
  return koi.post(API.UPDATE, data);
};

// 添加
export const add = (data: any) => {
  return koi.post(API.ADD, data);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE + "?id=" + id);
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids});
};

// 修改状态
export const updateStatus = (id: any, status: any) => {
  return koi.post(API.UPDATE_STATUE,{id,status});
};

// 修改是否折叠/展开
export const updateSpread = (id: any, spread: any) => {
  return koi.post(API.UPDATE_SPREAD,{id,spread});
};

// 查询分配部门所有数据
export const listDeptNormal = () => {
  return koi.get(API.LIST_DEPT_NORMAL);
};

// 根据用户拥有的角色ID查询部门权限数据
export const listDeptIdsByRoleId = (id: any) => {
  return koi.get(API.LIST_DEPTIDS_BY_ROLEID + "?id=" + id);
};

// 保存角色和部门权限数据之间的关系
export function saveRoleDept(role_id: any, dept_ids: any) {
  // 处理如果没有选择部门数据。无法匹配后台数据的问题
  if (dept_ids.length === 0) {
    dept_ids = [-1];
  }
  return koi.post(API.SAVE_ROLE_DEPT, { role_id, dept_ids });
}
