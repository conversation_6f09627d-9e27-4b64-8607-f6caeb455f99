<?php
namespace app\dogadmin\service;

use app\dogadmin\model\SysFileModel;
use app\lib\service\storage\CloudStorageFactory;
use app\lib\exception\dogadmin\FileException;
use think\File;
use think\facade\Config;

class SysFileService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysFileModel();
    }
    
    /**
     * 上传文件到云存储
     * 
     * @param string|File $file 文件对象或本地路径
     * @param string $storageType 存储类型 (qiniu|tencent_cos|aliyun_oss)
     * @param string $category 文件分类 (image|document|media|archive)
     * @param array $options 额外选项
     * @param string|bool $nameMode 文件名模式：
     *                             - 字符串: 使用自定义文件名（保留扩展名）
     *                             - true: 使用原始文件名
     *                             - false: 生成唯一文件名（默认）
     * @return array
     * @throws FileException
     */
    public function uploadToCloud(File|string $file, string $storageType = '', string $category = 'image', array $options = [], $nameMode = false): array
    {
        try {
            if (empty($storageType)) {
                $storageType = Config::get('cloud_storage.default', 'qiniu');
            }
            $config = [];
            $mergeConfig = false;
            if (isset($options['config']) && is_array($options['config'])) {
                $config = $options['config'];
                $mergeConfig = $options['merge_config'] ?? true;
            }
            if ($mergeConfig || empty($config)) {
                $rules = Config::get('cloud_storage.upload_rules.' . $category, []);
                if (!empty($rules)) {
                    if (!empty($config) && $mergeConfig) {
                        $config['rules'] = $config['rules'] ?? $rules;
                    } else {
                        $fileConfig = Config::get('cloud_storage.' . $storageType, []);
                        $fileConfig['rules'] = $rules;
                        $config = $fileConfig;
                    }
                }
            }
            $storageService = CloudStorageFactory::create($storageType, $config, $mergeConfig);
            $uploadPath = $options['upload_path'] ?? Config::get('cloud_storage.upload_paths.' . $category, 'uploads/');
            $fileName = $options['fileName'] ?? '';
            $result = $storageService->upload($file, $uploadPath, $fileName, $nameMode);
            $suffix = $options['suffix'] ?? Config::get('cloud_storage.suffix', '-small');
            // 统一获取文件信息
            $info = $storageService->parseFileInfo($file);
            $fileData = [
                'file_name' => $info['originalName'],
                'new_name' => basename($result['key']),
                'file_mime' => $result['mimeType'],
                'file_type' => $this->getFileTypeByExtension($result['extension']),
                'file_size' => $this->formatFileSize($result['size']),
                'file_ext' => $result['extension'],
                'file_upload' => $result['key'],
                'file_path' => $result['url'],
                'file_suffix'=> $suffix,
                'file_service' => $this->getFileServiceCode($storageType),
                'status' => '1',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
            $fileId = $this->model->insertGetId($fileData);
            $fileUploadPath = $result['url'] . $suffix;
            return [
                'success' => true,
                'file_id' => $fileId,
                'file_info' => array_merge($result, $fileData),
                'fileUploadPath' => $fileUploadPath,
            ];
        } catch (FileException $e) {
            throw $e;
        } catch (\Exception $e) {
            $originalName = '';
            try {
                $info = CloudStorageFactory::getDefault()->parseFileInfo($file);
                $originalName = $info['originalName'] ?? '';
            } catch (\Exception $ee) {
                $originalName = is_string($file) ? basename($file) : '';
            }
            throw new FileException([
                'message' => '文件上传失败: ' . $e->getMessage(),
                'file' => $originalName
            ], 5);
        }
    }
    
    /**
     * 删除云存储文件
     * 
     * @param int $fileId 文件ID
     * @return bool
     * @throws FileException
     */
    public function deleteCloudFile(int $fileId): bool
    {
        try {
            $fileInfo = $this->model->find($fileId);
            if (!$fileInfo) {
                throw new FileException(['file_id' => $fileId], 6); // 6 = 文件不存在
            }
            
            // 获取存储类型
            $storageType = $this->getStorageTypeByCode($fileInfo['file_service']);
            if ($storageType) {
                $storageService = CloudStorageFactory::create($storageType);
                $storageService->delete($fileInfo['file_upload']);
            }
            
            // 删除数据库记录
            $this->model->destroy($fileId);
            
            return true;
            
        } catch (FileException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '文件删除失败: ' . $e->getMessage(),
                'file_id' => $fileId
            ], 8); // 8 = 文件删除失败
        }
    }
    
    /**
     * 获取文件下载URL
     * 
     * @param int $fileId 文件ID
     * @param int $expires 过期时间（秒）
     * @return string
     * @throws FileException
     */
    public function getDownloadUrl(int $fileId, int $expires = 3600): string
    {
        $fileInfo = $this->model->find($fileId);
        if (!$fileInfo) {
            throw new FileException(['file_id' => $fileId], 6); // 6 = 文件不存在
        }
        
        $storageType = $this->getStorageTypeByCode($fileInfo['file_service']);
        if (!$storageType) {
            return $fileInfo['file_path'];
        }
        
        try {
            $storageService = CloudStorageFactory::create($storageType);
            
            // 如果支持预签名URL，使用预签名URL
            if (method_exists($storageService, 'getPresignedUrl')) {
                return $storageService->getPresignedUrl($fileInfo['file_upload'], $expires);
            }
            
            return $storageService->getUrl($fileInfo['file_upload']);
            
        } catch (\Exception $e) {
            return $fileInfo['file_path'];
        }
    }
    
    /**
     * 根据文件扩展名获取文件类型
     * 
     * @param string $extension 文件扩展名
     * @return string
     */
    private function getFileTypeByExtension(string $extension): string
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        $documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
        $audioTypes = ['mp3', 'wav', 'flac', 'aac', 'ogg'];
        $videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'];
        $archiveTypes = ['zip', 'rar', '7z', 'tar', 'gz'];
        $appTypes = ['exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm'];
        
        $extension = strtolower($extension);
        
        if (in_array($extension, $imageTypes)) {
            return '1'; // 图片
        } elseif (in_array($extension, $documentTypes)) {
            return '2'; // 文档
        } elseif (in_array($extension, $audioTypes)) {
            return '3'; // 音频
        } elseif (in_array($extension, $videoTypes)) {
            return '4'; // 视频
        } elseif (in_array($extension, $archiveTypes)) {
            return '5'; // 压缩包
        } elseif (in_array($extension, $appTypes)) {
            return '6'; // 应用程序
        } else {
            return '9'; // 其他
        }
    }
    
    /**
     * 格式化文件大小
     * 
     * @param int $size 文件大小（字节）
     * @return string
     */
    private function formatFileSize(int $size): string
    {
        if ($size < 1024) {
            return $size . 'B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . 'KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / (1024 * 1024), 2) . 'MB';
        } else {
            return round($size / (1024 * 1024 * 1024), 2) . 'GB';
        }
    }
    
    /**
     * 获取文件服务代码
     * 
     * @param string $storageType 存储类型
     * @return string
     */
    private function getFileServiceCode(string $storageType): string
    {
        $mapping = [
            'local' => '1',
            'qiniu' => '2',
            'aliyun_oss' => '3',
            'tencent_cos' => '4'
        ];
        
        return $mapping[$storageType] ?? '1';
    }
    
    /**
     * 根据服务代码获取存储类型
     * 
     * @param string $serviceCode 服务代码
     * @return string|null
     */
    private function getStorageTypeByCode(string $serviceCode): ?string
    {
        $mapping = [
            '1' => 'local',
            '2' => 'qiniu',
            '3' => 'aliyun_oss',
            '4' => 'tencent_cos'
        ];
        
        return $mapping[$serviceCode] ?? null;
    }
}