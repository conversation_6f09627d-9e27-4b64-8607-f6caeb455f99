<?php

namespace app\common_api\controller;

use app\common_api\service\CommonUserService;
use think\facade\Log;

/**
 * 测试事件控制器
 */
class TestEventController extends Base
{
    /**
     * 测试普通注册事件
     */
    public function testRegister()
    {
        $params = [
            'username' => 'test_user_' . time(),
            'password' => '123456',
        ];

        $userService = new CommonUserService();
        $userId = $userService->register($params);

        if ($userId) {
            return sRet('注册成功，用户ID: ' . $userId);
        } else {
            return fRet('注册失败');
        }
    }

    /**
     * 测试邮箱注册事件
     */
    public function testEmailRegister()
    {
        $params = [
            'username' => 'email_user_' . time(),
            'email' => 'test_' . time() . '@example.com',
            'password' => '123456',
        ];

        $userService = new CommonUserService();
        $userId = $userService->emailRegister($params);
        
        if ($userId) {
            return sRet('邮箱注册成功，用户ID: ' . $userId);
        } else {
            return fRet('邮箱注册失败');
        }
    }
}
