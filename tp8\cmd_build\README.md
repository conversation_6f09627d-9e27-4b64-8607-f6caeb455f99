# DogAdmin 代码生成工具

## 简介

DogAdmin 代码生成工具是一套基于 ThinkPHP 8.0 的代码生成工具，用于快速创建模块目录结构和生成基础代码文件，提高开发效率。

## 功能特点

- **模块生成**：一键创建标准的模块目录结构和基础文件
- **代码生成**：快速生成模型、控制器和服务类文件
- **统一规范**：生成的代码遵循项目开发规范，保持一致性
- **简化命令**：通过简单的命令行调用，无需记忆复杂参数

## 安装说明

1. 将 `cmd_build` 目录下的所有文件放置在项目根目录下的 `cmd_build` 目录中
2. 确保 `dog.bat` 文件可执行
3. 将项目根目录添加到系统环境变量中，或者在项目根目录下使用命令

## 使用方法

### 1. 创建模块目录结构

```bash
dog build <模块名>
```

例如，创建一个名为 `user` 的模块：

```bash
dog build user
```

### 2. 创建模型类

```bash
dog make:model <模块名/类名>
```

例如，在 `user` 模块下创建 `Admin` 模型：

```bash
dog make:model user/Admin
```

### 3. 创建控制器类

```bash
dog make:controller <模块名/类名>
```

例如，在 `user` 模块下创建 `AdminController` 控制器：

```bash
dog make:controller user/Admin
```

### 4. 创建服务类

```bash
dog make:service <模块名/类名>
```

例如，在 `user` 模块下创建 `AdminService` 服务：

```bash
dog make:service user/Admin
```

## 生成文件说明

### 模块目录结构

```
├── 模块名
│   ├── common.php         # 公共函数文件
│   ├── event.php          # 事件定义文件
│   ├── middleware.php     # 中间件定义文件
│   ├── common             # 公共目录
│   │   └── ApiResponse.php # API响应类
│   ├── controller         # 控制器目录
│   │   └── Base.php       # 基础控制器
│   ├── model              # 模型目录
│   │   └── BaseModel.php  # 基础模型
│   ├── service            # 服务目录
│   │   └── BaseService.php # 基础服务
│   ├── middleware         # 中间件目录
│   ├── route              # 路由目录
│   │   └── 模块名.php      # 路由定义文件
│   └── view               # 视图目录
```

### 生成的模型类

```php
<?php

namespace app\模块名\model;

/**
 * 类名 模型
 */
class 类名 extends BaseModel
{
    // 设置表名
    protected $name = ''; // 请设置表名

    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'delete_time' => 'datetime',
        // 在这里添加更多字段
    ];

    // 设置字段自动完成
    protected $autoWriteTimestamp = true;

    // 追加属性
    protected $append = [];
}
```

### 生成的控制器类

```php
<?php

namespace app\模块名\controller;

use app\模块名\service\类名Service;

/**
 * 类名 控制器
 */
class 类名Controller extends Base
{
    /**
     * 初始化方法
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = new 类名Service();
        $this->searchKey = [
            // 在这里定义可搜索字段
            // 例如：'name' => 'like',
            // 'status' => 'eq',
        ];
    }

    /**
     * 自定义方法示例
     * @return \think\Response
     */
    public function customAction()
    {
        $params = $this->params;
        // 实现自定义逻辑
        return json(['code' => 200, 'msg' => '操作成功', 'data' => []]);
    }
}
```

### 生成的服务类

```php
<?php

namespace app\模块名\service;

use app\模块名\model\类名;

/**
 * 类名 服务类
 */
class 类名Service extends BaseService
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new 类名();
    }

    // 包含标准的 CRUD 方法和自定义业务方法
}
```

## 注意事项

1. 在使用工具前，请确保已经安装并配置好 ThinkPHP 8.0 环境
2. 生成的文件如果已存在，工具不会覆盖，以防止丢失已有代码
3. 生成后的代码可能需要根据实际业务需求进行调整
4. 建议在开发初期使用此工具快速搭建项目骨架

## 自定义扩展

你可以根据项目需求修改 `build.php` 和 `make.php` 文件，自定义生成的代码模板：

1. 修改 `build.php` 可以调整生成的模块目录结构和基础文件内容
2. 修改 `make.php` 可以调整生成的模型、控制器和服务类的代码模板

## 与 ThinkPHP 原生命令的区别

DogAdmin 代码生成工具与 ThinkPHP 原生的 `build` 和 `make` 命令相比：

1. 更符合 DogAdmin 项目的架构和规范
2. 生成的代码包含更多实用的基础功能和注释
3. 提供了更简洁的命令调用方式
4. 生成的文件结构更加统一，便于团队协作

---

© DogAdmin 开发团队