<template>
  <!-- 头像 -->
  <el-image class="w-34px h-34px rounded-full select-none user-avatar" :src="avatar">
    <template #error>
      <el-image class="w-34px h-34px rounded-full select-none user-avatar" :src="errorAvatar"></el-image>
    </template>
  </el-image>
  <el-dropdown class="m-l-10px" :hide-on-click="false" @command="handleCommand">
    <div class="koi-dropdown">
      <div class="max-w-113px text-14px m-r-6px line-clamp-1 select-none">{{ user_name }}</div>
      <el-icon><arrow-down /></el-icon>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="koiMine" v-if="authStore.buttonList.includes('system:personage:list') || authStore.buttonList.includes('*')">{{ $t("header.personalCenter") }}</el-dropdown-item>
        <el-dropdown-item command="logout">{{ $t("header.logout") }}</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { koiSessionStorage, koiLocalStorage } from "@/utils/storage.ts";
import { LOGIN_URL } from "@/config";
import { useRouter } from "vue-router";
import { koiLogout } from '@/api/system/login/index.ts'
import { koiNoticeError } from "@/utils/koi";
import useAuthStore from "@/stores/modules/auth.ts";

const authStore = useAuthStore();
const router = useRouter();

onMounted(() => {
  handleLoginUser();
})

// 获取用户姓名
const user_name = ref();
// 用户头像
const avatar = ref();
const errorAvatar = "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png";
const handleLoginUser = () => {
  user_name.value = authStore.loginUser.user_name || '无名小辈';
  avatar.value = authStore.loginUser.avatar  || 'https://pic4.zhimg.com/v2-702a23ebb518199355099df77a3cfe07_b.webp';
}

// 退出登录
const handleLayout = async () => {
  try {
    // await koiLogout();
    koiSessionStorage.clear();
    // 如果不想要保存上次登录设置的全局颜色、布局等，则将下方第一行清空全部代码打开。
    // koiLocalStorage.clear();
    koiLocalStorage.remove("user");
    koiLocalStorage.remove("keepAlive");
    koiLocalStorage.remove("tabs");
    // 退出登录。必须使用replace把页面缓存刷掉。
    window.location.replace(LOGIN_URL);
  } catch (error) {
    console.log(error);
    koiNoticeError("退出登录失败🌻")
  }
}

// 下拉折叠
const handleCommand = (command: string | number) => {
  switch (command) {
    case "koiMine":
      router.push("/system/personage");
      break;
    case "logout":
      handleLayout();
      break;
  }
};
</script>

<style lang="scss" scoped>
// dropdown字体颜色
.koi-dropdown {
  color: var(--el-color-primary);
  white-space: nowrap; /* 不换行 */
  cursor: pointer;
  outline: none; // 去除伪元素
  display: flex;
  align-items: center;
}
</style>
