<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

Route::get('think', function () {
    return 'hello,ThinkPHP8!';
});

Route::get('hello/:name', 'index/hello');

// 异常测试路由组
Route::group('test/exception', function () {
    // 系统异常测试
    Route::get('sys', 'test.ExceptionTestController/testSysException');
    
    // 认证异常测试
    Route::get('auth', 'test.ExceptionTestController/testAuthException');
    
    // 验证异常测试（简单验证）
    Route::get('validate', 'test.ExceptionTestController/testValidateException');
    
    // 参数验证测试（带参数）
    Route::get('check', 'test.ExceptionTestController/testValidation');
    
    // 调试模式异常测试
    Route::get('debug', 'test.ExceptionTestController/testDebugException');
});