export default {
  home: {
    welcome: "Welcome"
  },
  tabs: {
    refresh: "Refresh",
    maximize: "Maximize",
    closeCurrent: "Close Current",
    closeLeft: "Close Left",
    closeRight: "Close Right",
    closeOther: "Close Other",
    closeAll: "Close All"
  },
  header: {
    searchMenu: "Search menu",
    componentSize: "Component size",
    refreshCache: "Refresh cache",
    lightMode: "Light mode",
    darkMode: "Dark mode",
    language: "Language translation",
    fullScreen: "Full Screen",
    exitFullScreen: "Exit Full Screen",
    personalCenter: "Personal Center",
    settings: "Settings",
    logout: "Log out"
  },
  login: {
    welcome: "Welcome to login",
    platform: "Management platform",
    description: "Maybe we just got lucky",
    account: "Account password login",
    login_name: "Please enter your user_name",
    password: "Please enter password",
    security: "Please enter the verification code",
    blur: "I can't see it. Change it",
    in: "Log in",
    center: "Be logging in",
    <PERSON><PERSON><PERSON>: "Website record number"
  }
};
