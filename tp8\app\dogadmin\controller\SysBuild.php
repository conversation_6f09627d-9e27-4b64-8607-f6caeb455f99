<?php

namespace app\dogadmin\controller;

use app\dogadmin\common\ApiResponse;
use app\dogadmin\common\ResponseCode;
use app\dogadmin\model\SysMenuModel;
use app\lib\exception\dogadmin\SysException;
use think\facade\Db;
use think\facade\Config;

/**
 * 代码生成器控制器
 * 用于自动生成模型、服务和控制器文件
 */
class SysBuild extends Base
{
    /**
     * 初始化方法
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->params = $this->request->param();
    }

    /**
     * 获取数据库表列表
     * @return \think\Response
     */
    public function listTables()
    {
        try {
            // 获取数据库配置
            $dbConfig = Config::get('database.connections.' . Config::get('database.default'));
            $dbName = $dbConfig['database'];
            
            // 查询所有表
            $tables = Db::query("SELECT TABLE_NAME AS name, TABLE_COMMENT AS comment FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{$dbName}'");
            
            return ApiResponse::success($tables, '获取成功');
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, $e->getMessage());
        }
    }

    /**
     * 获取表字段信息
     * @return \think\Response
     */
    public function getTableInfo()
    {
        $params = $this->params;
        $tableName = $params['table_name'] ?? '';
        
        if (empty($tableName)) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '表名不能为空');
        }
        
        try {
            // 获取数据库配置
            $dbConfig = Config::get('database.connections.' . Config::get('database.default'));
            $dbName = $dbConfig['database'];
            
            // 查询表信息
            $tableInfo = Db::query("SELECT TABLE_NAME, TABLE_COMMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{$dbName}' AND TABLE_NAME = '{$tableName}'");
            
            if (empty($tableInfo)) {
                return ApiResponse::error(ResponseCode::PARAM_ERROR, '表不存在');
            }
            
            // 查询表字段信息
            $columns = Db::query("SELECT COLUMN_NAME AS name, DATA_TYPE AS type, COLUMN_COMMENT AS comment, COLUMN_KEY AS `key`, IS_NULLABLE AS `nullable`, COLUMN_DEFAULT AS `default` FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '{$dbName}' AND TABLE_NAME = '{$tableName}' ORDER BY ORDINAL_POSITION");
            
            $result = [
                'table' => $tableInfo[0],
                'columns' => $columns
            ];
            
            return ApiResponse::success($result, '获取成功');
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, $e->getMessage());
        }
    }

    /**
     * 生成代码
     * @return \think\Response
     */
    public function generate()
    {
        $params = $this->params;
        
        // 验证必要参数
        if (empty($params['module_name'])) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '模块名不能为空');
        }
        
        if (empty($params['controller_name'])) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '控制器名不能为空');
        }
        
        if (empty($params['table_name'])) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '表名不能为空');
        }
        
        $moduleName = $params['module_name'];
        $controllerName = $params['controller_name'];
        $tableName = $params['table_name'];
        $tableComment = $params['table_comment'] ?? $controllerName;
        $modelFields = $params['model_fields'] ?? [];
        $searchFields = $params['search_fields'] ?? [];
        
        try {
            // 获取数据库配置
            $dbConfig = Config::get('database.connections.' . Config::get('database.default'));
            $dbName = $dbConfig['database'];
            
            // 如果没有提供字段信息，则从数据库获取
            if (empty($modelFields)) {
                $columns = Db::query("SELECT COLUMN_NAME AS name, DATA_TYPE AS type, COLUMN_COMMENT AS comment, COLUMN_KEY AS `key`, IS_NULLABLE AS `nullable`, COLUMN_DEFAULT AS `default` FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '{$dbName}' AND TABLE_NAME = '{$tableName}' ORDER BY ORDINAL_POSITION");
                $modelFields = $columns;
            }
            
            // 生成模型文件
            $modelResult = $this->generateModel($moduleName, $controllerName, $tableName, $modelFields);
            
            // 生成服务文件
            $serviceResult = $this->generateService($moduleName, $controllerName, $searchFields);
            
            // 生成控制器文件
            $controllerResult = $this->generateController($moduleName, $controllerName, $tableComment, $searchFields);
            
            // 生成前端API文件
            $frontendApiResult = $this->generateFrontendApi($moduleName, $controllerName, $tableComment);
            
            // 生成前端视图
            $frontendViewResult = $this->generateFrontendView($moduleName, $controllerName, $tableComment, $modelFields, $searchFields);

            $parentId = $params['parent_id'] ?? 0;
            $createParentMenu = $params['create_parent_menu'] ?? true;
            // 生成权限SQL文件
            $sqlResult = $this->generatePermissionSql($moduleName, $controllerName, $tableComment,$parentId,$createParentMenu);
            
            $result = [
                'model' => $modelResult,
                'service' => $serviceResult,
                'controller' => $controllerResult,
                'frontend_api' => $frontendApiResult,
                'frontend_view' => $frontendViewResult,
                'permission_sql' => $sqlResult
            ];
            
            return ApiResponse::success($result, '生成成功');
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, '生成失败：' . $e->getMessage());
        }
    }

    /**
     * 生成模型文件
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param string $tableName 表名
     * @param array $fields 字段信息
     * @return array 生成结果
     */
    private function generateModel($moduleName, $controllerName, $tableName, $fields)
    {
        $modelName = $controllerName . 'Model';
        $modelPath = base_path() . $moduleName . '/model/' . $modelName . '.php';
        
        // 检查目录是否存在，不存在则创建
        $modelDir = base_path() . $moduleName . '/model/';
        if (!is_dir($modelDir)) {
            mkdir($modelDir, 0755, true);
        }
        
        // 构建字段定义
        $schemaFields = [];
        foreach ($fields as $field) {
            $fieldName = $field['name'];
            $fieldType = $this->convertDbTypeToPhpType($field['type']);
            $schemaFields[] = "        '{$fieldName}' => '{$fieldType}',";
        }
        
        $schemaContent = implode("\n", $schemaFields);
        

        
        // 从模板文件加载内容
        $templatePath = app_path(). '/template/model.tpl';
        if (!file_exists($templatePath)) {
            return ['success' => false, 'message' => 'Model template file not found at ' . $templatePath];
        }
        $modelContent = file_get_contents($templatePath);

        // 替换占位符
        $modelContent = str_replace('{{module_name}}', $moduleName, $modelContent);
        $modelContent = str_replace('{{controller_name}}', $controllerName, $modelContent);
        $modelContent = str_replace('{{table_name}}', $tableName, $modelContent);
        $modelContent = str_replace('{{schemaContent}}', $schemaContent, $modelContent);

        // 写入文件
        $result = file_put_contents($modelPath, $modelContent); // Test with Hindi to ensure it's not a character set issue
        
        return [
            'path' => $modelPath,
            'content' => $modelContent,
            'success' => $result !== false
        ];
    }

    /**
     * 生成服务文件
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param array $searchFields 搜索字段
     * @return array 生成结果
     */
    private function generateService($moduleName, $controllerName, $searchFields)
    {
        $serviceName = $controllerName . 'Service';
        $modelName = $controllerName . 'Model';
        $servicePath = base_path() . $moduleName . '/service/' . $serviceName . '.php';
        
        // 检查目录是否存在，不存在则创建
        $serviceDir = base_path() . $moduleName . '/service/';
        if (!is_dir($serviceDir)) {
            mkdir($serviceDir, 0755, true);
        }
        

        
        // 从模板文件加载内容
        $templatePath = app_path(). '/template/service.tpl';
        if (!file_exists($templatePath)) {
            return ['success' => false, 'message' => 'Service template file not found at ' . $templatePath];
        }
        $serviceContent = file_get_contents($templatePath);

        // 替换占位符
        $serviceContent = str_replace('{{module_name}}', $moduleName, $serviceContent);
        $serviceContent = str_replace('{{controller_name}}', $controllerName, $serviceContent);
        $serviceContent = str_replace('{{table_comment}}', $controllerName, $serviceContent);

        // 写入文件
        $result = file_put_contents($servicePath, $serviceContent);
        
        return [
            'path' => $servicePath,
            'content' => $serviceContent,
            'success' => $result !== false
        ];
    }

    /**
     * 生成控制器文件
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param string $tableComment 表注释
     * @param array $searchFields 搜索字段
     * @return array 生成结果
     */
    private function generateController($moduleName, $controllerName, $tableComment, $searchFields)
    {
        $serviceName = $controllerName . 'Service';
        $controllerPath = base_path() . $moduleName . '/controller/' . $controllerName . '.php';
        
        // 检查目录是否存在，不存在则创建
        $controllerDir = base_path() . $moduleName . '/controller/';
        if (!is_dir($controllerDir)) {
            mkdir($controllerDir, 0755, true);
        }
        
        // 构建搜索字段配置
        $searchKeyItems = [];
        foreach ($searchFields as $field) {
            $fieldName = $field['name'];
            $searchType = $field['search_type'] ?? 'eq';
            $searchKeyItems[] = "            ['{$fieldName}' => '{$searchType}'],";
        }
        
        $searchKeyContent = empty($searchKeyItems) ? "            // 在这里定义可搜索字段" : implode("\n", $searchKeyItems);
        

        
        // 从模板文件加载内容
        $templatePath = app_path(). '/template/controller.tpl';
        if (!file_exists($templatePath)) {
            return ['success' => false, 'message' => 'Controller template file not found at ' . $templatePath];
        }
        $controllerContent = file_get_contents($templatePath);

        // 替换占位符
        $controllerContent = str_replace('{{module_name}}', $moduleName, $controllerContent);
        $controllerContent = str_replace('{{controller_name}}', $controllerName, $controllerContent);
        $controllerContent = str_replace('{{table_comment}}', $tableComment, $controllerContent);
        $controllerContent = str_replace('{{search_key_content}}', $searchKeyContent, $controllerContent);

        // 写入文件
        $result = file_put_contents($controllerPath, $controllerContent);
        
        return [
            'path' => $controllerPath,
            'content' => $controllerContent,
            'success' => $result !== false
        ];
    }

    /**
     * 将数据库类型转换为PHP类型
     * @param string $dbType 数据库类型
     * @return string PHP类型
     */
    private function convertDbTypeToPhpType($dbType)
    {
        $typeMap = [
            'int' => 'int',
            'bigint' => 'int',
            'tinyint' => 'int',
            'smallint' => 'int',
            'mediumint' => 'int',
            'float' => 'float',
            'double' => 'float',
            'decimal' => 'float',
            'char' => 'string',
            'varchar' => 'string',
            'text' => 'string',
            'mediumtext' => 'string',
            'longtext' => 'string',
            'datetime' => 'datetime',
            'timestamp' => 'datetime',
            'date' => 'date',
            'time' => 'time',
            'json' => 'json',
        ];
        
        return $typeMap[$dbType] ?? 'string';
    }
    
    /**
     * 首字母小写转换
     * @param string $str 要转换的字符串
     * @return string 转换后的字符串
     */
    private function lcfirstCamel($str)
    {
        return lcfirst($str);
    }
    
    /**
     * 生成前端API文件
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param string $tableComment 表注释
     * @return array 生成结果
     */
    private function generateFrontendApi($moduleName, $controllerName, $tableComment)
    {
        // 转换控制器名为首字母小写的驼峰格式，用于API路径
        $apiName = $this->lcfirstCamel($controllerName);
        
        // 创建API目录
        $apiDir = root_path() . '../admin-web/src/api/'.$moduleName.'/' . $apiName . '/';
        if (!is_dir($apiDir)) {
            mkdir($apiDir, 0755, true);
        }
        
        // API文件路径
        $apiFilePath = $apiDir . 'index.ts';
        
        // 读取模板文件
        $templateFile = app_path(). '/template/frontend_api.tpl';
        if (!file_exists($templateFile)) {
            throw new \Exception('前端API模板文件不存在：' . $templateFile);
        }
        
        $apiContent = file_get_contents($templateFile);
        
        // 替换占位符
        $apiContent = str_replace([
            '{{table_comment}}',
            '{{module_name}}',
            '{{api_name}}'
        ], [
            $tableComment,
            $moduleName,
            $apiName
        ], $apiContent);
        
        // 写入文件
        $result = file_put_contents($apiFilePath, $apiContent);
        
        return [
            'path' => $apiFilePath,
            'content' => $apiContent,
            'success' => $result !== false
        ];
    }
    
    /**
     * 生成前端视图文件
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param string $tableComment 表注释
     * @param array $fields 字段信息
     * @param array $searchFields 搜索字段信息
     * @return array 生成结果
     */
    private function generateFrontendView($moduleName, $controllerName, $tableComment, $fields, $searchFields = [])
    {
        // 转换控制器名为首字母小写的驼峰格式，用于视图路径和组件名
        $viewName = $this->lcfirstCamel($controllerName);
        $pageName = $controllerName . 'Page'; // 例如 DictTypePage
        $moduleNameLower = $moduleName; // Keep original case
        $viewNameLower = $viewName; // Keep original case

        // 创建视图目录
        $viewDir = root_path() . '../admin-web/src/views/'.$moduleName.'/' . $viewName . '/';
        if (!is_dir($viewDir)) {
            mkdir($viewDir, 0755, true);
        }
        $viewFilePath = $viewDir . 'index.vue';

        // 读取模板文件内容
        $templatePath = app_path(). '/template/frontend_view.tpl';
        if (!file_exists($templatePath)) {
            return [
                'path' => $viewFilePath,
                'content' => '',
                'success' => false,
                'error' => 'Template file not found: ' . $templatePath
            ];
        }
        $viewContent = file_get_contents($templatePath);

        // 生成搜索表单项、表格列、表单项、表单规则、表单初始数据
        $searchFormItems = [];
        $tableColumns = [];
        $formItems = [];
        $formRules = [];
        $formInitData = [];
        $searchParamsInit = [];
        
        // 将搜索字段转换为关联数组，方便查找
        $searchFieldsMap = [];
        foreach ($searchFields as $field) {
            $searchFieldsMap[$field['name']] = $field['search_type'];
        }

        // 检查是否有必填字段
        $hasRequiredFields = false;

        foreach ($fields as $field) {
            $fieldName = $field['name'];
            $fieldComment = $field['comment'] ?: $fieldName;
            $fieldType = $this->convertDbTypeToVueType($field['type']); // 转换为Vue组件类型
            $columnKey = $field['key'] ?? '';
            $isNullable = ($field['nullable'] ?? 'YES') === 'YES'; // 是否允许为空
            $isShowInTable = isset($field['isShow']) ? $field['isShow'] : true; // 是否在表格中显示，默认显示
            
            // 如果有不允许为空的字段或用户指定为必填字段，标记有必填字段
            $isRequired = (!$isNullable || (isset($field['isRequired']) && $field['isRequired'])) && !in_array($fieldName, ['id', 'create_time', 'update_time', 'delete_time', 'status', 'sorted']);
            if ($isRequired) {
                $hasRequiredFields = true;
            }

            // 1. 生成搜索表单项 - 只为指定的搜索字段生成搜索表单项
            if (isset($searchFieldsMap[$fieldName])) {
                if ($fieldName === 'status') {
                    $searchFormItems[] = <<<EOT
        <el-form-item label="{$fieldComment}" prop="status">
          <el-select v-model="searchParams.status" placeholder="请选择{$fieldComment}" clearable style="width: 220px" @keyup.enter.native="handleListPage">
            <el-option
              v-for="koi in koiDicts.sys_switch_status"
              :key="koi.dictValue"
              :label="koi.dictLabel"
              :value="koi.dictValue"
            />
          </el-select>
        </el-form-item>
EOT;
                } else if (in_array($fieldName, ['create_time', 'update_time'])) {
                    // 时间字段使用日期范围选择器
                    $searchFormItems[] = <<<EOT
        <el-form-item label="{$fieldComment}" prop="{$fieldName}">
          <el-date-picker
            v-model="searchParams.{$fieldName}"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择{$fieldComment}范围"
            clearable
            style="width: 380px"
            @keyup.enter.native="handleListPage"
          />
        </el-form-item>
EOT;
                    // 时间范围字段初始化为数组
                    $searchParamsInit[] = "  {$fieldName}: [],";
                } else {
                    $searchFormItems[] = <<<EOT
        <el-form-item label="{$fieldComment}" prop="{$fieldName}">
          <el-input v-model="searchParams.{$fieldName}" placeholder="请输入{$fieldComment}" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
EOT;
                    $searchParamsInit[] = "  {$fieldName}: '',";
                }
            }

            // 2. 生成表格列 (排除 delete_time) - 只为指定显示的字段生成表格列
            if (!$isShowInTable) {
                continue; // 如果不显示在表格中，跳过生成表格列
            }
            
            if ($fieldName == 'delete_time') {
                // delete_time 不显示
                continue;
            } else if ($fieldName == 'id') {
                // id 字段显示在表格中
                $tableColumns[] = <<<EOT
        <el-table-column label="{$fieldComment}" prop="{$fieldName}" width="80" align="center"></el-table-column>
EOT;
            } else if ($fieldName == 'status') {
                $tableColumns[] = <<<EOT
        <el-table-column label="{$fieldComment}" prop="status" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              active-value="1"
              inactive-value="0"
              inline-prompt
              @change="handleSwitch(scope.row)"
              v-auth="['{$moduleNameLower}:{$viewNameLower}:update']"
            />
          </template>
        </el-table-column>
EOT;
            } else if (in_array($fieldName, ['create_time', 'update_time'])) {
                // 时间字段显示在表格中
                $tableColumns[] = <<<EOT
        <el-table-column label="{$fieldComment}" prop="{$fieldName}" width="180" align="center" :show-overflow-tooltip="true"></el-table-column>
EOT;
            } else {
                $width = ($fieldType == 'textarea') ? '250' : '150'; // 文本域给更宽的默认值
                $tableColumns[] = <<<EOT
        <el-table-column label="{$fieldComment}" prop="{$fieldName}" width="{$width}" align="center" :show-overflow-tooltip="true"></el-table-column>
EOT;
            }

            // 3. 生成表单项 (排除 delete_time)
            if ($fieldName == 'delete_time') {
                // delete_time 不显示在表单中
                continue;
            } else if ($fieldName == 'id') {
                // id 字段在编辑时为不可编辑的input
                $formItems[] = <<<EOT
              <el-col :span="12">
                <el-form-item label="{$fieldComment}" prop="{$fieldName}">
                  <el-input v-model="form.{$fieldName}" placeholder="{$fieldComment}" disabled />
                </el-form-item>
              </el-col>
EOT;
            } else if ($fieldName == 'status') {
                $formItems[] = <<<EOT
              <el-col :span="24">
                <el-form-item label="{$fieldComment}" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio value="1">启用</el-radio>
                    <el-radio value="0">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
EOT;
            } else if (in_array($fieldName, ['create_time', 'update_time'])) {
                // 时间字段使用日期时间选择器
                $formItems[] = <<<EOT
              <el-col :span="12">
                <el-form-item label="{$fieldComment}" prop="{$fieldName}">
                  <el-date-picker
                    v-model="form.{$fieldName}"
                    type="datetime"
                    placeholder="选择{$fieldComment}"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </el-form-item>
              </el-col>
EOT;
            } elseif ($fieldType == 'textarea') {
                $formItems[] = <<<EOT
              <el-col :span="24">
                <el-form-item label="{$fieldComment}" prop="{$fieldName}">
                  <el-input v-model="form.{$fieldName}" type="textarea" :rows="3" placeholder="请输入{$fieldComment}" clearable />
                </el-form-item>
              </el-col>
EOT;
            } else { // 其他类型如 input
                 $formItems[] = <<<EOT
              <el-col :span="12">
                <el-form-item label="{$fieldComment}" prop="{$fieldName}">
                  <el-input v-model="form.{$fieldName}" placeholder="请输入{$fieldComment}" clearable />
                </el-form-item>
              </el-col>
EOT;
            }

            // 4. 生成表单验证规则 - 只为必填字段生成验证规则
            // 检查字段是否必填：1. 数据库中不允许为空 或 2. 用户在前端指定为必填
            $isRequired = (!$isNullable || (isset($field['isRequired']) && $field['isRequired'])) && !in_array($fieldName, ['id', 'create_time', 'update_time', 'delete_time', 'status', 'sorted']);
            if ($isRequired) {
                $formRules[] = "  {$fieldName}: [{ required: true, message: '{$fieldComment}不能为空', trigger: 'blur' }],";
            }

            // 5. 生成表单初始数据 (排除 delete_time)
            if ($fieldName == 'delete_time') {
                // delete_time 不包含在表单初始数据中
                continue;
            } else if ($fieldName == 'id') {
                $formInitData[] = "  {$fieldName}: '',";
            } else if ($fieldName == 'status') {
                $formInitData[] = "  status: '1', // 默认启用";
            } else if (in_array($fieldName, ['create_time', 'update_time'])) {
                $formInitData[] = "  {$fieldName}: '',";
            } else {
                $formInitData[] = "  {$fieldName}: '',";
            }
        }

        $searchFormItemsStr = implode("\n", $searchFormItems);
        $tableColumnsStr = implode("\n", $tableColumns);
        $formItemsStr = implode("\n", $formItems);
        // 移除末尾可能多余的逗号
        $formRulesStr = rtrim(implode("\n", $formRules), ',');
        $formInitDataStr = rtrim(implode("\n", $formInitData), ',');
        $searchParamsInitStr = rtrim(implode("\n", $searchParamsInit), ',');

        // 如果没有必填字段，则不生成验证规则
        if (!$hasRequiredFields || empty($formRules)) {
            $formRulesStr = '  // 没有必填字段，不进行校验';
        }

        // 替换占位符
        $replacements = [
            '{{search_form_items}}' => $searchFormItemsStr,
            '{{table_columns}}' => $tableColumnsStr,
            '{{form_items}}' => $formItemsStr,
            '{{form_rules}}' => $formRulesStr,
            '{{form_data}}' => $formInitDataStr,
            '{{search_form_data}}' => $searchParamsInitStr,
            '{{module_name}}' => $moduleName,
            '{{api_path}}' => $viewName,
            '{{table_comment}}' => $tableComment
        ];
        
        $viewContent = str_replace(array_keys($replacements), array_values($replacements), $viewContent);

        // 写入文件
        $result = file_put_contents($viewFilePath, $viewContent);

        return [
            'path' => $viewFilePath,
            'content' => $viewContent, // 返回生成的内容用于预览或调试
            'success' => $result !== false
        ];
    }

    /**
     * 将数据库类型转换为Vue组件适用的类型或Element Plus组件类型提示
     * @param string $dbType 数据库类型
     * @return string Vue组件类型或提示
     */
    private function convertDbTypeToVueType($dbType)
    {
        // 简单映射，可以根据需要扩展
        $dbTypeLower = strtolower($dbType);
        if (strpos($dbTypeLower, 'int') !== false) {
            return 'number'; // el-input number
        }
        if (in_array($dbTypeLower, ['float', 'double', 'decimal'])) {
            return 'number'; // el-input number
        }
        // 检查是否包含varchar并提取长度
        if (preg_match('/varchar\((\d+)\)/', $dbTypeLower, $matches)) {
            $length = intval($matches[1]);
            if ($length > 255) {
                return 'textarea'; // 长度超过255的varchar视为textarea
            }
            return 'text'; // el-input text
        }
        if (in_array($dbTypeLower, ['text', 'mediumtext', 'longtext', 'char'])) {
            if (strpos($dbTypeLower, 'text') !== false ) {
                 return 'textarea'; // el-input textarea
            }
            return 'text'; // el-input text
        }
        if (in_array($dbTypeLower, ['date', 'datetime', 'timestamp'])) {
            return 'datetime'; // el-date-picker
        }
        // 更多类型判断...
        return 'text'; // 默认为普通文本输入框
    }

    
    /**
     * 生成权限SQL文件
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param string $tableComment 表注释
     * @param int $parentId 父菜单ID
     * @param bool $createParentMenu 是否创建父级目录
     * @return array 生成结果
     */
    private function generatePermissionSql($moduleName, $controllerName, $tableComment, $parentId = 0, $createParentMenu = true)
    {
        // 转换控制器名为首字母小写的驼峰格式，用于权限标识
        $permName = $this->lcfirstCamel($controllerName);

        // 生成SQL文件路径
        // 检查目录是否存在，不存在则创建
        $sqlDir = base_path() . $moduleName . '/sql/';
        if (!is_dir($sqlDir)) {
            mkdir($sqlDir, 0755, true);
        }
        
        // SQL文件路径
        $sqlFilePath = base_path() . $moduleName . '/sql/' . $controllerName . '_menu.sql';

        $auth = $moduleName.':'.$permName.':listPage';
        
        // 生成父级目录SQL（如果需要创建新目录）
        $parentMenuSql = '';
        if ($createParentMenu && $parentId == 0) {
            $parentMenuSql = "-- 添加{$tableComment}目录\nINSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) \nVALUES ('{$tableComment}', 0, '1', '{$permName}', 'FolderOpened','{$auth}', '1', '1', 999, NOW(), NOW());\n\n-- 获取插入的目录ID\nSET @parentId = LAST_INSERT_ID();";
            $parentId = '@parentId';
        }
        
        // 读取模板文件
        $templateFile = app_path(). '/template/permission_sql.tpl';
        if (!file_exists($templateFile)) {
            throw new \Exception('权限SQL模板文件不存在：' . $templateFile);
        }
        
        $sqlContent = file_get_contents($templateFile);
        
        // 替换占位符
        $sqlContent = str_replace([
            '{{table_comment}}',
            '{{module_name}}',
            '{{perm_name}}',
            '{{parent_id}}',
            '{{parent_menu_sql}}'
        ], [
            $tableComment,
            $moduleName,
            $permName,
            $parentId,
            $parentMenuSql
        ], $sqlContent);
        
        // 写入文件
        $result = file_put_contents($sqlFilePath, $sqlContent);
        
        return [
            'path' => $sqlFilePath,
            'content' => $sqlContent,
            'success' => $result !== false
        ];
    }

    /**
     * 预览代码
     * @return \think\Response
     */
    public function preview()
    {
        $params = $this->params;
        
        // 验证必要参数
        if (empty($params['module_name'])) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '模块名不能为空');
        }
        
        if (empty($params['controller_name'])) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '控制器名不能为空');
        }
        
        if (empty($params['table_name'])) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '表名不能为空');
        }
        
        $moduleName = $params['module_name'];
        $controllerName = $params['controller_name'];
        $tableName = $params['table_name'];
        $tableComment = $params['table_comment'] ?? $controllerName;
        $modelFields = $params['model_fields'] ?? [];
        $searchFields = $params['search_fields'] ?? [];
        
        try {
            // 获取数据库配置
            $dbConfig = Config::get('database.connections.' . Config::get('database.default'));
            $dbName = $dbConfig['database'];
            
            // 如果没有提供字段信息，则从数据库获取
            if (empty($modelFields)) {
                $columns = Db::query("SELECT COLUMN_NAME AS name, DATA_TYPE AS type, COLUMN_COMMENT AS comment, COLUMN_KEY AS `key`, IS_NULLABLE AS `nullable`, COLUMN_DEFAULT AS `default` FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '{$dbName}' AND TABLE_NAME = '{$tableName}' ORDER BY ORDINAL_POSITION");
                $modelFields = $columns;
            }
            
            // 生成模型代码（不写入文件）
            $modelName = $controllerName . 'Model';
            
            // 读取模型模板文件
            $modelTemplateFile = app_path(). '/template/model.tpl';
            if (!file_exists($modelTemplateFile)) {
                throw new \Exception('模型模板文件不存在：' . $modelTemplateFile);
            }
            
            $modelContent = file_get_contents($modelTemplateFile);
            
            // 替换占位符
            $modelContent = str_replace([
                '{{module_name}}',
                '{{controller_name}}',
                '{{table_name}}',
                '{{table_comment}}'
            ], [
                $moduleName,
                $controllerName,
                $tableName,
                $tableComment
            ], $modelContent);
            
            // 生成服务代码（不写入文件）
            $serviceName = $controllerName . 'Service';
            
            // 读取服务模板文件
            $serviceTemplateFile = app_path(). '/template/service.tpl';
            if (!file_exists($serviceTemplateFile)) {
                throw new \Exception('服务模板文件不存在：' . $serviceTemplateFile);
            }
            
            $serviceContent = file_get_contents($serviceTemplateFile);
            
            // 替换占位符
            $serviceContent = str_replace([
                '{{module_name}}',
                '{{controller_name}}',
                '{{table_comment}}'
            ], [
                $moduleName,
                $controllerName,
                $tableComment
            ], $serviceContent);
            
            // 构建搜索字段配置
            $searchKeyItems = [];
            foreach ($searchFields as $field) {
                $fieldName = $field['name'];
                $searchType = $field['search_type'] ?? 'eq';
                $searchKeyItems[] = "            ['{$fieldName}' => '{$searchType}'],";
            }
            
            $searchKeyContent = empty($searchKeyItems) ? "            // 在这里定义可搜索字段" : implode("\n", $searchKeyItems);
            
            // 读取控制器模板文件
            $controllerTemplateFile = app_path(). '/template/controller.tpl';
            if (!file_exists($controllerTemplateFile)) {
                throw new \Exception('控制器模板文件不存在：' . $controllerTemplateFile);
            }
            
            $controllerContent = file_get_contents($controllerTemplateFile);
            
            // 替换占位符
            $controllerContent = str_replace([
                '{{module_name}}',
                '{{controller_name}}',
                '{{table_comment}}',
                '{{search_key_content}}'
            ], [
                $moduleName,
                $controllerName,
                $tableComment,
                $searchKeyContent
            ], $controllerContent);
            
            // 生成前端API代码（不写入文件）
            $apiFileName = 'index.ts';
            $apiPath = $this->lcfirstCamel($controllerName);
            
            // API内容模板
            $apiContent = $this->generateFrontendApiContent($moduleName, $controllerName, $tableComment);
            
            // 生成前端视图代码（不写入文件）
            $viewFileName = 'index.vue';
            $viewPath = $this->lcfirstCamel($controllerName);
            
            // 视图内容模板
            $viewContent = $this->generateFrontendViewContent($moduleName, $controllerName, $tableComment, $modelFields, $searchFields);
            
            // 生成权限SQL代码（不写入文件）
            $sqlFileName = $this->lcfirstCamel($controllerName) . '.sql';
            
            // SQL内容模板
            $parentId = $params['parent_id'] ?? 0;
            $createParentMenu = $params['create_parent_menu'] ?? true;
            $sqlContent = $this->generatePermissionSqlContent($moduleName, $controllerName, $tableComment, $parentId, $createParentMenu);
            
            $result = [
                'model' => [
                    'name' => $modelName,
                    'content' => $modelContent
                ],
                'service' => [
                    'name' => $serviceName,
                    'content' => $serviceContent
                ],
                'controller' => [
                    'name' => $controllerName,
                    'content' => $controllerContent
                ],
                'frontend_api' => [
                    'name' => $apiFileName,
                    'content' => $apiContent
                ],
                'frontend_view' => [
                    'name' => $viewFileName,
                    'content' => $viewContent
                ],
                'permission_sql' => [
                    'name' => $sqlFileName,
                    'content' => $sqlContent
                ]
            ];
            
            return ApiResponse::success($result, '预览成功');
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, '预览失败：' . $e->getMessage());
        }
    }

    /**
     * 生成前端API内容
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param string $tableComment 表注释
     * @return string 生成的内容
     */
    private function generateFrontendApiContent($moduleName, $controllerName, $tableComment)
    {
        // 控制器名称保持驼峰格式，不全部转小写
        $apiPath = $this->lcfirstCamel($controllerName);
        
        // 读取前端API模板文件
        $apiTemplateFile = app_path(). '/template/frontend_api.tpl';
        if (!file_exists($apiTemplateFile)) {
            throw new \Exception('前端API模板文件不存在：' . $apiTemplateFile);
        }
        
        $apiContent = file_get_contents($apiTemplateFile);
        
        // 替换占位符
        $apiContent = str_replace([
            '{{module_name}}',
            '{{api_name}}',
            '{{table_comment}}'
        ], [
            $moduleName,
            $apiPath,
            $tableComment
        ], $apiContent);
        
        return $apiContent;
    }
    
    /**
     * 生成前端视图内容
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param string $tableComment 表注释
     * @param array $fields 字段信息
     * @param array $searchFields 搜索字段信息
     * @return string 生成的内容
     */
    private function generateFrontendViewContent($moduleName, $controllerName, $tableComment, $fields, $searchFields = [])
    {
        $templatePath = 	app_path() . '/template/frontend_view.tpl';
        
        if (!file_exists($templatePath)) {
            // throw new Exception("Frontend view template file not found: {$templatePath}");
            throw new SysException([],0,"Frontend view template file not found: {$templatePath}");
        }
        
        $template = file_get_contents($templatePath);
        
        // 转换控制器名为首字母小写的驼峰格式，用于API路径
        $apiPath = $this->lcfirstCamel($controllerName);
        $moduleNameLower = $moduleName; // Keep original case
        $viewNameLower = $apiPath; // Keep original case
        
        // 生成搜索表单项
        $searchFormItems = [];
        // 生成表格列
        $tableColumns = [];
        // 生成表单项
        $formItems = [];
        // 生成表单验证规则
        $formRules = [];
        // 生成表单初始数据
        $formData = [];
        // 生成搜索参数初始化
        $searchParamsInit = [];
        
        // 将搜索字段转换为关联数组，方便查找
        $searchFieldsMap = [];
        foreach ($searchFields as $field) {
            $searchFieldsMap[$field['name']] = $field['search_type'];
        }
        
        // 检查是否有必填字段
        $hasRequiredFields = false;
        
        foreach ($fields as $field) {
            $fieldName = $field['name'];
            $fieldComment = $field['comment'] ?: $fieldName;
            $fieldType = $this->convertDbTypeToVueType($field['type']); // 转换为Vue组件类型
            $isNullable = ($field['nullable'] ?? 'YES') === 'YES'; // 是否允许为空
            $isShowInTable = isset($field['isShow']) ? $field['isShow'] : true; // 是否在表格中显示，默认显示
            
            // 如果有不允许为空的字段或用户指定为必填字段，标记有必填字段
            $isRequired = (!$isNullable || (isset($field['isRequired']) && $field['isRequired'])) && !in_array($fieldName, ['id', 'create_time', 'update_time', 'delete_time', 'status', 'sorted']);
            if ($isRequired) {
                $hasRequiredFields = true;
            }
            
            // 1. 生成搜索表单项 - 只为指定的搜索字段生成搜索表单项
            if (isset($searchFieldsMap[$fieldName])) {
                if ($fieldName === 'status') {
                    $searchFormItems[] = <<<EOT
        <el-form-item label="{$fieldComment}" prop="status">
          <el-select v-model="searchParams.status" placeholder="请选择{$fieldComment}" clearable style="width: 220px" @keyup.enter.native="handleListPage">
            <el-option
              v-for="koi in koiDicts.sys_switch_status"
              :key="koi.dictValue"
              :label="koi.dictLabel"
              :value="koi.dictValue"
            />
          </el-select>
        </el-form-item>
EOT;
                } else if (in_array($fieldName, ['create_time', 'update_time'])) {
                    // 时间字段使用日期范围选择器
                    $searchFormItems[] = <<<EOT
        <el-form-item label="{$fieldComment}" prop="{$fieldName}">
          <el-date-picker
            v-model="searchParams.{$fieldName}"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择{$fieldComment}范围"
            clearable
            style="width: 380px"
            @keyup.enter.native="handleListPage"
          />
        </el-form-item>
EOT;
                    // 时间范围字段初始化为数组
                    $searchParamsInit[] = "  {$fieldName}: [],";
                } else {
                    $searchFormItems[] = <<<EOT
        <el-form-item label="{$fieldComment}" prop="{$fieldName}">
          <el-input v-model="searchParams.{$fieldName}" placeholder="请输入{$fieldComment}" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
EOT;
                    $searchParamsInit[] = "  {$fieldName}: '',";
                }
            }
            
            // 2. 生成表格列 (排除 delete_time) - 只为指定显示的字段生成表格列
            if (!$isShowInTable) {
                continue; // 如果不显示在表格中，跳过生成表格列
            }
            
            if ($fieldName == 'delete_time') {
                // delete_time 不显示
                continue;
            } else if ($fieldName == 'id') {
                // id 字段显示在表格中
                $tableColumns[] = <<<EOT
        <el-table-column label="{$fieldComment}" prop="{$fieldName}" width="80" align="center"></el-table-column>
EOT;
            } else if ($fieldName == 'status') {
                $tableColumns[] = <<<EOT
        <el-table-column label="{$fieldComment}" prop="status" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              active-value="1"
              inactive-value="0"
              inline-prompt
              @change="handleSwitch(scope.row)"
              v-auth="['{$moduleNameLower}:{$viewNameLower}:update']"
            />
          </template>
        </el-table-column>
EOT;
            } else if (in_array($fieldName, ['create_time', 'update_time'])) {
                // 时间字段显示在表格中
                $tableColumns[] = <<<EOT
        <el-table-column label="{$fieldComment}" prop="{$fieldName}" width="180" align="center" :show-overflow-tooltip="true"></el-table-column>
EOT;
            } else {
                $width = ($fieldType == 'textarea') ? '250' : '150'; // 文本域给更宽的默认值
                $tableColumns[] = <<<EOT
        <el-table-column label="{$fieldComment}" prop="{$fieldName}" width="{$width}" align="center" :show-overflow-tooltip="true"></el-table-column>
EOT;
            }
            
            // 3. 生成表单项 (排除 delete_time)
            if ($fieldName == 'delete_time') {
                // delete_time 不显示在表单中
                continue;
            } else if ($fieldName == 'id') {
                // id 字段在编辑时为不可编辑的input
                $formItems[] = <<<EOT
              <el-col :span="12">
                <el-form-item label="{$fieldComment}" prop="{$fieldName}">
                  <el-input v-model="form.{$fieldName}" placeholder="{$fieldComment}" disabled />
                </el-form-item>
              </el-col>
EOT;
            } else if ($fieldName == 'status') {
                $formItems[] = <<<EOT
              <el-col :span="24">
                <el-form-item label="{$fieldComment}" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio value="1">启用</el-radio>
                    <el-radio value="0">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
EOT;
            } else if (in_array($fieldName, ['create_time', 'update_time'])) {
                // 时间字段使用日期时间选择器
                $formItems[] = <<<EOT
              <el-col :span="12">
                <el-form-item label="{$fieldComment}" prop="{$fieldName}">
                  <el-date-picker
                    v-model="form.{$fieldName}"
                    type="datetime"
                    placeholder="选择{$fieldComment}"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </el-form-item>
              </el-col>
EOT;
            } elseif ($fieldType == 'textarea') {
                $formItems[] = <<<EOT
              <el-col :span="24">
                <el-form-item label="{$fieldComment}" prop="{$fieldName}">
                  <el-input v-model="form.{$fieldName}" type="textarea" :rows="3" placeholder="请输入{$fieldComment}" clearable />
                </el-form-item>
              </el-col>
EOT;
            } else {
                $formItems[] = <<<EOT
              <el-col :span="12">
                <el-form-item label="{$fieldComment}" prop="{$fieldName}">
                  <el-input v-model="form.{$fieldName}" placeholder="请输入{$fieldComment}" clearable />
                </el-form-item>
              </el-col>
EOT;
            }
            
            // 4. 生成表单验证规则 - 只为必填字段生成验证规则
            // 检查字段是否必填：1. 数据库中不允许为空 或 2. 用户在前端指定为必填
            $isRequired = (!$isNullable || (isset($field['isRequired']) && $field['isRequired'])) && !in_array($fieldName, ['id', 'create_time', 'update_time', 'delete_time', 'status', 'sorted']);
            if ($isRequired) {
                $formRules[] = "  {$fieldName}: [{ required: true, message: '{$fieldComment}不能为空', trigger: 'blur' }],";
            }
            
            // 5. 生成表单初始数据 (排除 delete_time)
            if ($fieldName == 'delete_time') {
                // delete_time 不包含在表单初始数据中
                continue;
            } else if ($fieldName == 'id') {
                $formData[] = "  {$fieldName}: '',";
            } else if ($fieldName == 'status') {
                $formData[] = "  status: '1', // 默认启用";
            } else if (in_array($fieldName, ['create_time', 'update_time'])) {
                $formData[] = "  {$fieldName}: '',";
            } else {
                $formData[] = "  {$fieldName}: '',";
            }
        }
        
        $searchFormItemsStr = implode("\n", $searchFormItems);
        $tableColumnsStr = implode("\n", $tableColumns);
        $formItemsStr = implode("\n", $formItems);
        
        // 移除末尾可能多余的逗号
        $formRulesStr = rtrim(implode("\n", $formRules), ',');
        $formDataStr = rtrim(implode("\n", $formData), ',');
        $searchParamsInitStr = rtrim(implode("\n", $searchParamsInit), ',');
        
        // 如果没有必填字段，则不生成验证规则
        if (!$hasRequiredFields || empty($formRules)) {
            $formRulesStr = '  // 没有必填字段，不进行校验';
        }
        
        // 替换模板中的占位符
        $replacements = [
            '{{search_form_items}}' => $searchFormItemsStr,
            '{{table_columns}}' => $tableColumnsStr,
            '{{form_items}}' => $formItemsStr,
            '{{form_data}}' => $formDataStr,
            '{{form_rules}}' => $formRulesStr,
            '{{api_path}}' => $apiPath,
            '{{module_name}}' => $moduleName,
            '{{table_comment}}' => $tableComment
        ];
        
        return str_replace(array_keys($replacements), array_values($replacements), $template);
    }
    
    /**
     * 生成权限SQL内容
     * @param string $moduleName 模块名
     * @param string $controllerName 控制器名
     * @param string $tableComment 表注释
     * @return string 生成的内容
     */
    private function generatePermissionSqlContent($moduleName, $controllerName, $tableComment, $parentId = 0, $createParentMenu = true)
    {
        $templatePath = app_path().'/template/permission_sql.tpl';
        
        if (!file_exists($templatePath)) {
            throw new \Exception('权限SQL模板文件不存在: ' . $templatePath);
        }
        
        $template = file_get_contents($templatePath);
        $apiPath = $this->lcfirstCamel($controllerName);
        $auth = $moduleName.':'.$apiPath.':listPage';
        // 生成父级目录SQL（如果需要创建新目录）
        $parentMenuSql = '';
        if ($createParentMenu && $parentId == 0) {
            $parentMenuSql = "-- 添加{$tableComment}目录\nINSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `icon`,`auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) \nVALUES ('{$tableComment}管理', 0, '1', '{$apiPath}', 'FolderOpened', {$auth}, '1', '1', 999, NOW(), NOW());\n\n-- 获取插入的目录ID\nSET @parentId = LAST_INSERT_ID();";
            $parentId = '@parentId';
        }
        
        // 替换模板占位符
        $replacements = [
            '{{table_comment}}' => $tableComment,
            '{{module_name}}' => $moduleName,
            '{{perm_name}}' => $apiPath,
            '{{parent_id}}' => $parentId,
            '{{parent_menu_sql}}' => $parentMenuSql
        ];
        
        return str_replace(array_keys($replacements), array_values($replacements), $template);
    }

    /**
     * 获取API接口文档
     * @return \think\Response
     */
    public function getApiDocs()
    {
        $params = $this->params;
        
        // 验证必要参数
        if (empty($params['module_name'])) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '模块名不能为空');
        }
        
        if (empty($params['controller_name'])) {
            return ApiResponse::error(ResponseCode::PARAM_ERROR, '控制器名不能为空');
        }
        
        $moduleName = $params['module_name'];
        $controllerName = $params['controller_name'];
        $tableComment = $params['table_comment'] ?? $controllerName;
        $modelFields = $params['model_fields'] ?? [];
        
        try {
            // 构建API文档
            $apiDocs = [
                [
                    'name' => '获取分页列表',
                    'url' => '/' . $this->lcfirstCamel($moduleName) . '/' . $this->lcfirstCamel($controllerName) . '/listPage',
                    'method' => 'GET',
                    'params' => [
                        ['name' => 'page', 'type' => 'int', 'required' => false, 'default' => 1, 'desc' => '页码'],
                        ['name' => 'limit', 'type' => 'int', 'required' => false, 'default' => 15, 'desc' => '每页数量'],
                        ['name' => 'order', 'type' => 'string', 'required' => false, 'default' => 'id desc', 'desc' => '排序字段']
                    ],
                    'response' => [
                        'code' => 0,
                        'msg' => '获取成功',
                        'count' => '总记录数',
                        'data' => '数据列表'
                    ]
                ],
                [
                    'name' => '根据ID获取详情',
                    'url' => '/' . $this->lcfirstCamel($moduleName) . '/' . $this->lcfirstCamel($controllerName) . '/getById',
                    'method' => 'GET',
                    'params' => [
                        ['name' => 'id', 'type' => 'int', 'required' => true, 'desc' => '记录ID']
                    ],
                    'response' => [
                        'code' => 0,
                        'msg' => '获取成功',
                        'data' => '数据详情'
                    ]
                ],
                [
                    'name' => '新增数据',
                    'url' => '/' . $this->lcfirstCamel($moduleName) . '/' . $this->lcfirstCamel($controllerName) . '/add',
                    'method' => 'POST',
                    'params' => [],
                    'response' => [
                        'code' => 0,
                        'msg' => '添加成功',
                        'data' => ['id' => '新增记录ID']
                    ]
                ],
                [
                    'name' => '更新数据',
                    'url' => '/' . $this->lcfirstCamel($moduleName) . '/' . $this->lcfirstCamel($controllerName) . '/update',
                    'method' => 'POST',
                    'params' => [
                        ['name' => 'id', 'type' => 'int', 'required' => true, 'desc' => '记录ID']
                    ],
                    'response' => [
                        'code' => 0,
                        'msg' => '更新成功',
                        'data' => null
                    ]
                ],
                [
                    'name' => '删除数据',
                    'url' => '/' . $this->lcfirstCamel($moduleName) . '/' . $this->lcfirstCamel($controllerName) . '/deleteById',
                    'method' => 'POST',
                    'params' => [
                        ['name' => 'id', 'type' => 'int', 'required' => true, 'desc' => '记录ID']
                    ],
                    'response' => [
                        'code' => 0,
                        'msg' => '删除成功',
                        'data' => null
                    ]
                ],
                [
                    'name' => '批量删除数据',
                    'url' => '/' . $this->lcfirstCamel($moduleName) . '/' . $this->lcfirstCamel($controllerName) . '/batchDelete',
                    'method' => 'POST',
                    'params' => [
                        ['name' => 'ids', 'type' => 'string', 'required' => true, 'desc' => '记录ID，多个ID用逗号分隔']
                    ],
                    'response' => [
                        'code' => 0,
                        'msg' => '删除成功',
                        'data' => null
                    ]
                ],
                [
                    'name' => '更新状态',
                    'url' => '/' . $this->lcfirstCamel($moduleName) . '/' . $this->lcfirstCamel($controllerName) . '/updateStatus',
                    'method' => 'POST',
                    'params' => [
                        ['name' => 'id', 'type' => 'int', 'required' => true, 'desc' => '记录ID'],
                        ['name' => 'status', 'type' => 'int', 'required' => true, 'desc' => '状态值']
                    ],
                    'response' => [
                        'code' => 0,
                        'msg' => '更新成功',
                        'data' => null
                    ]
                ]
            ];
            
            // 添加字段参数
            foreach ($apiDocs as &$api) {
                if ($api['name'] === '新增数据' || $api['name'] === '更新数据') {
                    foreach ($modelFields as $field) {
                        $fieldName = $field['name'];
                        $fieldType = $this->convertDbTypeToPhpType($field['type']);
                        $fieldComment = $field['comment'] ?? $fieldName;
                        $required = $api['name'] === '新增数据' && !in_array($fieldName, ['id', 'create_time', 'update_time', 'delete_time']);
                        
                        if ($fieldName !== 'id' || $api['name'] === '更新数据') {
                            $api['params'][] = [
                                'name' => $fieldName,
                                'type' => $fieldType,
                                'required' => $required,
                                'desc' => $fieldComment
                            ];
                        }
                    }
                }
            }
            
            return ApiResponse::success($apiDocs, '获取成功');
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, '获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取菜单列表（用于选择父级目录）
     * @return \think\Response
     */
    public function getMenuList()
    {
        try {
            $menuModel = new SysMenuModel();
            
            // 只获取目录类型的菜单（menu_type = 1）
            $menus = $menuModel->where('menu_type', '1')
                              ->where('status', '1')
                              ->field('id,menu_name,parent_id')
                              ->order('sorted asc, id asc')
                              ->select()
                              ->toArray();
            
            // 构建树形结构
            $menuTree = $this->buildMenuTree($menus);
            
            return ApiResponse::success($menuTree, '获取成功');
        } catch (\Exception $e) {
            return ApiResponse::error(ResponseCode::SYSTEM_ERROR, '获取失败：' . $e->getMessage());
        }
    }

    /**
     * 构建菜单树形结构
     * @param array $menus
     * @param int $parentId
     * @return array
     */
    private function buildMenuTree($menus, $parentId = 0)
    {
        $tree = [];
        foreach ($menus as $menu) {
            if ($menu['parent_id'] == $parentId) {
                $children = $this->buildMenuTree($menus, $menu['id']);
                if ($children) {
                    $menu['children'] = $children;
                }
                $tree[] = $menu;
            }
        }
        return $tree;
    }
}