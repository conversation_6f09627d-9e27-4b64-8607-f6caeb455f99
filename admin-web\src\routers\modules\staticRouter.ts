import { RouteRecordRaw } from "vue-router";
import { HOME_URL, LOGIN_URL } from "@/config";
import Layout from "@/layouts/index.vue";

/**
 * layoutRouter[布局路由]
 */
export const layoutRouter: RouteRecordRaw[] = [
  // {
  //   path: "/",
  //   redirect: HOME_URL
  // },
  // {
  //   path: "/layout",
  //   name: "layout",
  //   component: Layout,
  //   redirect: HOME_URL,
  //   children: [
  //      {
  //       path: HOME_URL, // [唯一]
  //       component: () => import("@/views/home/<USER>"),
  //       meta: {
  //         title: "主控台", // 标题
  //         en_name: "Master Station", // 英文名称
  //         icon: "HomeFilled", // 图标
  //         is_hide: "0", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
  //         is_link: "", // 是否外链[有值则是外链]
  //         is_keep_alive: "0", // 是否缓存路由数据[0是，1否]
  //         is_full: "1", // 是否缓存全屏[0是，1否]
  //         is_affix: "0" // 是否缓存固定路由[0是，1否]
  //       }
  //     }
  //   ]
  // },
  // 上方或者下方效果一样
  {
    // 登录成功以后展示数据的路由[一级路由，可以将子路由放置Main模块中(核心)]
    path: "/", // 路由访问路径[唯一]
    name: "layout", // 命名路由[唯一]
    component: Layout, // 登录进入这个页面，这个页面是整个布局
    redirect: HOME_URL, // path路径，<router-link name="/404"> 也是使用path进行跳转
    children: [
      {
        path: HOME_URL, // [唯一]
        name: "home", // 添加name属性解决Vue Router警告
        component: () => import("@/views/home/<USER>"),
        meta: {
          title: "主控台", // 标题
          en_name: "Master Station", // 英文名称
          icon: "HomeFilled", // 图标
          is_hide: "0", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
          is_link: "", // 是否外链[有值则是外链]
          is_keep_alive: "0", // 是否缓存路由数据[0是，1否]
          is_full: "1", // 是否缓存全屏[0是，1否]
          is_affix: "0" // 是否缓存固定路由[0是，1否]
        }
      }
    ]
  },
  {
    path: LOGIN_URL,
    name: "login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录"
    }
  }
];

// 若是想将主控台放置后端，上方注释，JSON数据在下方
// {
//   "menu_id": 3,
//   "menu_name": "主控台",
//   "en_name": "Master Station",
//   "parent_id": 0,
//   "menu_type": "1",
//   "path": "/home/<USER>",
//   "name": "homePage",
//   "component": "home/index",
//   "icon": "HomeFilled",
//   "is_hide": "1",
//   "is_link": "",
//   "is_keep_alive": "0",
//   "is_full": "1",
//   "is_affix": "1",
//   "redirect": "/home/<USER>"
// },
/**
 * staticRouter[静态路由]
 */
export const staticRouter: RouteRecordRaw[] = [
  /** 主控台 */
  {
    path: HOME_URL, // [唯一]
    component: () => import("@/views/home/<USER>"),
    meta: {
      title: "主控台", // 标题
      en_name: "Master Station", // 英文名称
      icon: "HomeFilled", // 图标 HomeFilled
      is_hide: "1", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
      is_link: "", // 是否外链[有值则是外链]
      is_keep_alive: "0", // 是否缓存路由数据[0是，1否]
      is_full: "1", // 是否缓存全屏[0是，1否]
      is_affix: "0" // 是否缓存固定路由[0是，1否]
    }
  },
  {
    path: "/system/static", // 路由访问路径[唯一]
    name: "staticPage", // 命名路由[唯一]
    component: Layout, // 一级路由，可以将子路由放置Main模块中
    meta: {
      title: "静态路由", // 标题
      nName: "Static Router", // 英文名称
      icon: "House", // 图标
      is_hide: "0", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
      is_link: "", // 是否外链[有值则是外链]
      is_keep_alive: "1", // 是否缓存路由数据[0是，1否]
      is_full: "1", // 是否缓存全屏[0是，1否]
      is_affix: "1", // 是否缓存固定路由[0是，1否]
      active_menu: HOME_URL // 默认选中哪个路由
    },
    children: [
      {
        path: "/system/dict/data/:dict_type", // 路由访问路径[唯一]
        name: "dictDataPage", // 命名路由[唯一]
        component: () => import("@/views/system/dict/data.vue"), // 一级路由，可以将子路由放置Main模块中
        meta: {
          title: "字典详情", // 标题
          en_name: "DictData Manage", // 英文名称
          icon: "Flag", // 图标
          is_hide: "0", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
          is_link: "", // 是否外链[有值则是外链]
          is_keep_alive: "0", // 是否缓存路由数据[0是，1否]
          is_full: "1", // 是否缓存全屏[0是，1否]
          is_affix: "1", // 是否缓存固定路由[0是，1否]
          active_menu: "/system/dict/type" // 默认选中哪个路由
        }
      },
      {
        path: "/tools/gen/config/:tableSchema/:tableName", // 路由访问路径[唯一]
        name: "configPage", // 命名路由[唯一]
        component: () => import("@/views/tools/gen/config.vue"), // 一级路由，可以将子路由放置Main模块中
        meta: {
          title: "代码配置", // 标题
          en_name: "Code Config", // 英文名称
          icon: "ChatSquare", // 图标
          is_hide: "0", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
          is_link: "", // 是否外链[有值则是外链]
          is_keep_alive: "0", // 是否缓存路由数据[0是，1否]
          is_full: "1", // 是否缓存全屏[0是，1否]
          is_affix: "1", // 是否缓存固定路由[0是，1否]
          active_menu: "/tools/gen" // 默认选中哪个路由
        }
      }
    ]
  }  
];

/**
 * errorRouter[错误页面路由]
 */
export const errorRouter = [
  {
    path: "/403",
    name: "403",
    component: () => import("@/views/error/403.vue"),
    meta: {
      title: "403页面",
      en_name: "403 Page", // 英文名称
      icon: "QuestionFilled", // 菜单图标
      is_hide: "1", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
      is_link: "1", // 是否外链[有值则是外链]
      is_keep_alive: "0", // 是否缓存路由数据[0是，1否]
      is_full: "1", // 是否缓存全屏[0是，1否]
      is_affix: "1" // 是否缓存固定路由[0是，1否]
    }
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/error/404.vue"),
    meta: {
      title: "404页面",
      en_name: "404 Page", // 英文名称
      icon: "CircleCloseFilled", // 菜单图标
      is_hide: "1", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
      is_link: "1", // 是否外链[有值则是外链]
      is_keep_alive: "0", // 是否缓存路由数据[0是，1否]
      is_full: "1", // 是否缓存全屏[0是，1否]
      is_affix: "1" // 是否缓存固定路由[0是，1否]
    }
  },
  {
    path: "/500",
    name: "500",
    component: () => import("@/views/error/500.vue"),
    meta: {
      title: "500页面",
      en_name: "500 Page", // 英文名称
      icon: "WarningFilled", // 图标
      is_hide: "1", // 代表路由在菜单中是否隐藏，是否隐藏[0隐藏，1显示]
      is_link: "1", // 是否外链[有值则是外链]
      is_keep_alive: "0", // 是否缓存路由数据[0是，1否]
      is_full: "1", // 是否缓存全屏[0是，1否]
      is_affix: "1" // 是否缓存固定路由[0是，1否]
    }
  },
  // 找不到path将跳转404页面
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404.vue")
  }
];
