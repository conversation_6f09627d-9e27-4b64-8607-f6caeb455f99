<template>
  <div class="koi-flex">
    <KoiCard>
      <!-- 步骤条 -->
      <el-steps :active="activeStep" finish-status="success" simple>
        <el-step title="选择数据表" />
        <el-step title="配置字段" />
        <el-step title="生成代码" />
      </el-steps>

      <!-- 步骤1：选择数据表 -->
      <div v-if="activeStep === 1" class="mt-20px">
        <el-form :inline="true">
          <el-form-item label="表名搜索">
            <el-input 
              v-model="tableSearch" 
              placeholder="请输入表名" 
              clearable 
              @input="filterTables" 
              @clear="() => { tableSearch = ''; filterTables(); }" 
            />
          </el-form-item>
        </el-form>

        <el-table
          v-loading="tableLoading"
          :data="filteredTables"
          border
          style="width: 100%"
          @selection-change="handleTableSelectionChange"
          highlight-current-row
          @row-click="handleTableRowClick"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="表名" prop="name" />
          <el-table-column label="表注释" prop="comment" />
          <el-table-column label="操作" width="180" align="center">
            <template #default="{ row }">
              <el-button type="primary" @click="selectTable(row)" :disabled="selectedTable?.name === row.name">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="mt-20px button-container pagination-button-container">
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page_no"
              v-model:page-size="pagination.page_size"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </div>
          <div class="button-right">
            <el-button type="primary" @click="nextStep" :disabled="!selectedTable">下一步</el-button>
          </div>
        </div>
      </div>

      <!-- 步骤2：配置字段 -->
      <div v-if="activeStep === 2" class="mt-20px">
        <el-form :model="formData" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模块名称" required>
                <el-input v-model="formData.module_name" placeholder="请输入模块名称，如：dogadmin" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="控制器名称" required>
                <el-input v-model="formData.controller_name" placeholder="请输入控制器名称，如：SysUser" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="表名">
                <el-input v-model="formData.table_name" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="表注释">
                <el-input v-model="formData.table_comment" placeholder="请输入表注释" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="父级目录">
                <el-tree-select
                  v-model="formData.parent_id"
                  :data="menuTreeData"
                  :props="{ label: 'menu_name', value: 'id', children: 'children' }"
                  placeholder="请选择父级目录（不选择则创建新目录）"
                  clearable
                  check-strictly
                  :render-after-expand="false"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建新目录">
                <el-switch
                  v-model="formData.create_parent_menu"
                  active-text="是"
                  inactive-text="否"
                  :inline-prompt="true"
                  :disabled="formData.parent_id > 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="mt-20px">
          <el-divider content-position="left">字段配置</el-divider>
          <el-table 
            :data="tableColumns" 
            border 
            style="width: 100%" 
            height="400" 
            max-height="400"
            :header-cell-style="{background:'#f5f7fa'}"
          >
            <el-table-column label="字段名" prop="name" width="180" />
            <el-table-column label="字段类型" prop="type" width="120" />
            <el-table-column label="字段注释" width="180">
              <template #default="{ row }">
                <el-input v-model="row.comment" placeholder="请输入字段注释" @change="(value) => handleCommentChange(row, value)" />
              </template>
            </el-table-column>
            <el-table-column label="主键" width="80" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.key === 'PRI'" type="danger">是</el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="允许为空" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.nullable === 'YES'" type="success">是</el-tag>
                <el-tag v-else type="info">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="默认值" prop="default" width="120" />
            <el-table-column label="是否为搜索字段" width="120" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.isSearch"
                  active-text="是"
                  inactive-text="否"
                  :inline-prompt="true"
                />
              </template>
            </el-table-column>
            <el-table-column label="搜索方式" width="120">
              <template #default="{ row }">
                <el-select v-model="row.search_type" placeholder="请选择" :disabled="!row.isSearch">
                  <el-option label="等于(=)" value="eq" />
                  <el-option label="不等于(!=)" value="neq" />
                  <el-option label="大于(>)" value="gt" />
                  <el-option label="大于等于(>=)" value="egt" />
                  <el-option label="小于(<)" value="lt" />
                  <el-option label="小于等于(<=)" value="elt" />
                  <el-option label="范围(between)" value="between" />
                  <el-option label="模糊查询(like)" value="like" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="是否显示" width="100" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.isShow"
                  active-text="是"
                  inactive-text="否"
                  :inline-prompt="true"
                  :active-value="true"
                  :inactive-value="false"
                  @change="(value) => handleShowChange(row, value)"
                />
              </template>
            </el-table-column>
            <el-table-column label="是否必填" width="100" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.isRequired"
                  active-text="是"
                  inactive-text="否"
                  :inline-prompt="true"
                  :active-value="true"
                  :inactive-value="false"
                  :disabled="row.nullable === 'NO'"
                  @change="(value) => handleRequiredChange(row, value)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
.
        <div class="mt-20px text-right button-container">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤3：生成代码 -->
      <div v-if="activeStep === 3" class="mt-20px">
        <el-tabs v-model="previewTab">
          <el-tab-pane label="模型代码" name="model">
            <el-card shadow="never">
              <pre class="code-preview">{{ codePreview.model?.content || '暂无预览' }}</pre>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="服务代码" name="service">
            <el-card shadow="never">
              <pre class="code-preview">{{ codePreview.service?.content || '暂无预览' }}</pre>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="控制器代码" name="controller">
            <el-card shadow="never">
              <pre class="code-preview">{{ codePreview.controller?.content || '暂无预览' }}</pre>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="前端API" name="frontend_api">
            <el-card shadow="never">
              <pre class="code-preview">{{ codePreview.frontend_api?.content || '暂无预览' }}</pre>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="前端视图" name="frontend_view">
            <el-card shadow="never">
              <pre class="code-preview">{{ codePreview.frontend_view?.content || '暂无预览' }}</pre>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="权限SQL" name="permission_sql">
            <el-card shadow="never">
              <pre class="code-preview">{{ codePreview.permission_sql?.content || '暂无预览' }}</pre>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="API文档" name="api">
            <el-card shadow="never">
              <pre class="code-preview">{{ apiDocs || '暂无API文档' }}</pre>
            </el-card>
          </el-tab-pane>
        </el-tabs>

        <div class="mt-20px text-right button-container">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="handlePreview">预览代码</el-button>
          <el-button type="success" @click="handleGenerate">生成代码</el-button>
          <el-button type="info" @click="handleDownloadZip">下载代码包</el-button>
        </div>
      </div>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="systemBuild">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listTables, getTableInfo, previewCode, generateCode, getApiDocs, getMenuList } from '@/api/system/build';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';

// 步骤控制
const activeStep = ref(1);
const nextStep = () => {
  if (activeStep.value === 1 && !selectedTable.value) {
    ElMessage.warning('请先选择一个数据表');
    return;
  }
  
  if (activeStep.value === 2) {
    // 验证表单
    if (!formData.module_name) {
      ElMessage.warning('请输入模块名称');
      return;
    }
    if (!formData.controller_name) {
      ElMessage.warning('请输入控制器名称');
      return;
    }
    // 预览代码
    handlePreview();
  }
  
  activeStep.value++;
};
const prevStep = () => {
  activeStep.value--;
};

// 表格数据
const tableLoading = ref(false);
const tableList = ref<any[]>([]);
const tableSearch = ref('');
const pagination = reactive({
  page_no: 1,
  page_size: 10,
  total: 0
});
const filteredTables = computed(() => {
  if (!tableSearch.value) return tableList.value;
  
  // 当有搜索条件时，在前端进行过滤
  const filtered = tableList.value.filter(item => 
    item.name.toLowerCase().includes(tableSearch.value.toLowerCase()) || 
    (item.comment && item.comment.toLowerCase().includes(tableSearch.value.toLowerCase()))
  );
  
  // 更新过滤后的总数，但不影响实际分页
  if (filtered.length === 0) {
    return [];
  }
  return filtered;
});

// 分页变化处理
const handlePageChange = (page: number) => {
  pagination.page_no = page;
  loadTableList();
};

const handleSizeChange = (size: number) => {
  pagination.page_size = size;
  pagination.page_no = 1;
  loadTableList();
};

// 表格选择
const selectedTableIds = ref<any[]>([]);
const selectedTable = ref<any>(null);
const handleTableSelectionChange = (selection: any[]) => {
  selectedTableIds.value = selection.map(item => item.name);
  if (selection.length === 1) {
    selectTable(selection[0]);
  } else if (selection.length === 0) {
    selectedTable.value = null;
  }
};
const handleTableRowClick = (row: any) => {
  selectTable(row);
};
const selectTable = async (row: any) => {
  selectedTable.value = row;
  // 获取表字段信息
  try {
    const res = await getTableInfo(row.name);
    if (res.code === 1) {
      tableColumns.value = res.data.columns.map((item: any) => ({
        ...item,
        isSearch: false,
        search_type: 'eq',
        isShow: true,
        isRequired: item.nullable === 'NO'
      }));
      // 设置表单数据
      formData.table_name = row.name;
      formData.table_comment = row.comment || '';
    } else {
      ElMessage.error(res.msg || '获取表字段信息失败');
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取表字段信息失败');
  }
};

// 过滤表格
const filterTables = () => {
  if (tableSearch.value) {
    // 本地过滤，使用计算属性
  } else {
    // 重置分页并重新加载
    pagination.page_no = 1;
    loadTableList();
  }
};

// 表字段数据
const tableColumns = ref<any[]>([]);

// 处理字段注释修改
const handleCommentChange = (row: any, value: string) => {
  row.comment = value;
};

// 处理字段显示切换
const handleShowChange = (row: any, value: boolean) => {
  row.isShow = value;
};

// 处理必填字段切换
const handleRequiredChange = (row: any, value: boolean) => {
  row.isRequired = value;
};

// 表单数据
const formData = reactive({
  module_name: '',
  controller_name: '',
  table_name: '',
  table_comment: '',
  parent_id: 0,
  create_parent_menu: true
});

// 菜单树数据
const menuTreeData = ref<any[]>([]);

// 预览代码
const previewTab = ref('model');
const codePreview = ref<any>({});
const apiDocs = ref('');
const handlePreview = async () => {
  try {
    // 构建请求参数
    const params = {
      ...formData,
      search_fields: tableColumns.value
        .filter(item => item.isSearch)
        .map(item => ({
          name: item.name,
          search_type: item.search_type
        })),
      model_fields: tableColumns.value.map(item => ({
        ...item,
        isShowInTable: item.isShow,
        isNullable: item.nullable === 'YES' && !item.isRequired,
        isRequired: item.isRequired === true || item.nullable === 'NO'
      }))
    };
    
    const res = await previewCode(params);
    if (res.code === 1) {
      codePreview.value = res.data;
      ElMessage.success('预览代码成功');
      
      // 获取API文档
      try {
        const docsRes = await getApiDocs(params);
        if (docsRes.code === 1) {
          apiDocs.value = JSON.stringify(docsRes.data, null, 2);
        }
      } catch (error) {
        console.error(error);
      }
    } else {
      ElMessage.error(res.msg || '预览代码失败');
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('预览代码失败');
  }
};

// 生成代码
const handleGenerate = async () => {
  try {
    ElMessageBox.confirm('确定要生成代码吗？生成的代码将覆盖同名文件', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      // 构建请求参数
      const params = {
        ...formData,
        search_fields: tableColumns.value
          .filter(item => item.isSearch)
          .map(item => ({
            name: item.name,
            search_type: item.search_type
          })),
        model_fields: tableColumns.value.map(item => ({
          ...item,
          isShowInTable: item.isShow,
          isNullable: item.nullable === 'YES' && !item.isRequired,
          isRequired: item.isRequired === true || item.nullable === 'NO'
        }))
      };
      
      const res = await generateCode(params);
      if (res.code === 1) {
        ElMessage.success('生成代码成功');
        // 创建zip打包下载
        // await createAndDownloadZip(params.module_name, codePreview.value);
      } else {
        ElMessage.error(res.msg || '生成代码失败');
      }
    }).catch(() => {
      // 取消操作
    });
  } catch (error) {
    console.error(error);
    ElMessage.error('生成代码失败');
  }
};

// 创建并下载ZIP文件
const createAndDownloadZip = async (moduleName: string, codeFiles: any) => {
  try {
    const zip = new JSZip();
    const timestamp = new Date().getTime();
    const zipFileName = `${moduleName}_${timestamp}.zip`;
    
    // 添加模型代码
    if (codeFiles.model?.content) {
      zip.file(`${moduleName}/model/${formData.controller_name}.php`, codeFiles.model.content);
    }
    
    // 添加服务代码
    if (codeFiles.service?.content) {
      zip.file(`${moduleName}/service/${formData.controller_name}Service.php`, codeFiles.service.content);
    }
    
    // 添加控制器代码
    if (codeFiles.controller?.content) {
      zip.file(`${moduleName}/controller/${formData.controller_name}Controller.php`, codeFiles.controller.content);
    }
    
    // 添加前端API
    if (codeFiles.frontend_api?.content) {
      zip.file(`${moduleName}/frontend/api/${formData.controller_name}.ts`, codeFiles.frontend_api.content);
    }
    
    // 添加前端视图
    if (codeFiles.frontend_view?.content) {
      zip.file(`${moduleName}/frontend/views/${formData.controller_name}/index.vue`, codeFiles.frontend_view.content);
    }
    
    // 添加权限SQL
    if (codeFiles.permission_sql?.content) {
      zip.file(`${moduleName}/sql/permission.sql`, codeFiles.permission_sql.content);
    }
    
    // 添加API文档
    if (apiDocs.value) {
      zip.file(`${moduleName}/docs/api.json`, apiDocs.value);
    }
    
    // 生成并下载zip文件
    const content = await zip.generateAsync({ type: 'blob' });
    saveAs(content, zipFileName);
    
    ElMessage.success(`代码已打包下载为 ${zipFileName}`);
  } catch (error) {
    console.error('打包下载失败:', error);
    ElMessage.error('打包下载失败');
  }
};

// 直接下载代码包
const handleDownloadZip = async () => {
  try {
    // 检查是否有预览代码
    if (!codePreview.value || Object.keys(codePreview.value).length === 0) {
      ElMessage.warning('请先预览代码');
      return;
    }
    
    // 检查模块名称
    if (!formData.module_name) {
      ElMessage.warning('请输入模块名称');
      return;
    }
    
    // 直接打包下载预览的代码
    await createAndDownloadZip(formData.module_name, codePreview.value);
  } catch (error) {
    console.error(error);
    ElMessage.error('下载代码包失败');
  }
};

// 加载表格数据
const loadTableList = async () => {
  try {
    tableLoading.value = true;
    const res = await listTables({
      page_no: pagination.page_no,
      page_size: pagination.page_size
    });
    if (res.code === 1) {
      // 如果后端返回的是分页数据结构
      if (res.data && res.data.list && typeof res.data.total === 'number') {
        tableList.value = res.data.list;
        pagination.total = res.data.total;
      } else {
        // 如果后端返回的仍然是完整数组，前端进行分页处理
        const allData = res.data;
        pagination.total = allData.length;
        const start = (pagination.page_no - 1) * pagination.page_size;
        const end = start + pagination.page_size;
        tableList.value = allData.slice(start, end);
      }
    } else {
      ElMessage.error(res.msg || '获取表列表失败');
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取表列表失败');
  } finally {
    tableLoading.value = false;
  }
};

// 加载菜单数据
const loadMenuList = async () => {
  try {
    const res = await getMenuList();
    if (res.code === 1) {
      menuTreeData.value = res.data;
    } else {
      console.error('获取菜单列表失败:', res.msg);
    }
  } catch (error) {
    console.error('获取菜单列表失败:', error);
  }
};

// 初始化
onMounted(() => {
  loadTableList();
  loadMenuList();
});
</script>

<style lang="scss" scoped>
.code-preview {
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.button-container {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding: 15px 0;
  margin-top: 20px !important;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.pagination-button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  flex: 1;
}

.button-right {
  margin-left: 20px;
}
</style>