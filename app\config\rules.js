function checkPhone(value) {
	return /^(\d{11}|\d{10}|\d{7}|\d{8})$/.test(value)
}
const rule_login = [{
		name: "phone",
		rule: ["required"],
		msg: ["请输入手机号"],
		validator: [{
			msg: '请输入正确的手机号',
			method: checkPhone
		}]
	},
	{
		name: "password",
		rule: ["required"],
		msg: ["请输入密码"]
	},
];
const rule_register = [{
	name: "phone",
	rule: ["required"],
	msg: ["请输入手机号"],
	validator: [{
		msg: '请输入正确的手机号',
		method: checkPhone
	}]
}, {
	name: "password",
	rule: ["required"],
	msg: ["请输入密码"]
}, {
	name: "rePassword",
	rule: ["required", "isSame:password"],
	msg: ["请输入确认密码", "两次输入的密码不一致"]
}, {
	name: "vcode",
	rule: ["required"],
	msg: ["请输入数字验证码"]
}, {
	name: "code",
	rule: ["required"],
	msg: ["请输入手机验证码"]
}];
const rule_edit = [{
		name: "title",
		rule: ["required"],
		msg: ["请输入标题"]
	},
	{
		name: "contact_name",
		rule: ["required"],
		msg: ["请输入联系人"]
	},
	{
		name: "contact_phone",
		rule: ["required"],
		msg: ["请输入联系手机"],
		validator: [{
			msg: '请输入正确的联系手机',
			method: checkPhone
		}]
	},
	{
		name: "content",
		rule: ["required"],
		msg: ["请输入内容"]
	}
];
const rule_bindPhone = [{
	name: "phone",
	rule: ["required"],
	msg: ["请输入手机号"],
	validator: [{
		msg: '请输入正确的手机号',
		method: checkPhone
	}]
}];
const rule_userInfo = [{
	name: "nickname",
	rule: ["required"],
	msg: ["请输入昵称"]
}, {
	name: "avatar_url",
	rule: ["required"],
	msg: ["请上传头像"]
}];
const rule_task_join = [{
	name: "contact_phone",
	rule: ["required"],
	msg: ["请输入联系方式"],
	validator: [{
		msg: '请输入正确的手机号',
		method: checkPhone
	}]
}];

const rule_real_name = [{
		name: "id_card",
		rule: ["required", "isIdCard"],
		msg: ["请输入身份证号码","请输入正确身份证"]
	},
	{
		name: "real_name",
		rule: ["required"],
		msg: ["请输入真实姓名"]
	},
	{
		name: "contact_phone",
		rule: ["required"],
		msg: ["请输入联系手机"],
		validator: [{
			msg: '请输入正确的手机号',
			method: checkPhone
		}]
	}
];

export { rule_login, rule_register, rule_edit, rule_bindPhone, rule_userInfo, rule_task_join, rule_real_name };