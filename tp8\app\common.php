<?php
// 应用公共文件
function sRet($data=1,$msg='ok',$code=1): \think\response\Json
{
    $ret = [
        'code'=>$code,
        'msg'=>$msg,
        'data'=>$data
    ];
    return json($ret);
}
function fRet($msg='err',$data=0,$code=0): \think\response\Json
{
    $ret = [
        'code'=>$code,
        'msg'=>$msg,
        'data'=>$data
    ];
    return json($ret);
}

function md5_salt($password, $salt = 'bigdog'){

    return md5($password.$salt);
}

/**
 * 增强版 json_decode，无法解析时原样返回，兼容非字符串输入
 *
 * @param mixed $json 待解析的 JSON 字符串（或其他类型）
 * @param bool $assoc 是否返回关联数组（默认 false，返回对象）
 * @param int $depth 递归深度（默认 512）
 * @param int $options 解析选项（默认 0）
 * @return mixed 解析成功返回对应数据，失败或非字符串输入原样返回
 */
function json_decode_pro($json, $assoc = false, $depth = 512, $options = 0) {
    // 非字符串类型直接返回原数据（解决 array given 错误）
    if (!is_string($json)) {
        return $json;
    }

    // 尝试解析 JSON
    $decoded = json_decode($json, $assoc, $depth, $options);

    // 检查是否解析成功（无错误且结果非 null 时才认为有效）
    // 注意：空字符串/空 JSON 会解析为 null，但属于合法情况，需特殊处理
    $is_valid = json_last_error() === JSON_ERROR_NONE;
    $is_empty = $json === '' || $json === 'null'; // 空字符串或 "null" 视为合法空值

    // 若解析失败或结果为 null 但非空输入，则返回原数据
    if (!$is_valid || ($decoded === null && !$is_empty)) {
        return $json;
    }

    return $decoded;
}

function generateRandomString($length = 5) {
    // 生成随机字节
    $bytes = openssl_random_pseudo_bytes($length);
    // 将字节转换为十六进制字符串
    return substr(bin2hex($bytes), 0, $length);
}