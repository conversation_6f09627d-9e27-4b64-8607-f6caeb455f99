<?php
use think\facade\Route;
use app\dogadmin\middleware\AdminCheck;
use app\dogadmin\middleware\MenuAuth;
use app\dogadmin\controller\Base;

Route::group('login', function () {
    Route::post('loginUser', 'app\sys\controller\Login@login');
});

// 不需要登录验证的用户相关路由
Route::group('sysUser', function () {
    Route::get('captcha', 'app\dogadmin\controller\SysUser@captcha');
    Route::get('originPassword', 'app\dogadmin\controller\SysUser@originPassword');
});

// 使用Base::registerCrudRoutes自动注册标准CRUD路由
Base::registerCrudRoutes('sysUser', 'app\dogadmin\controller\SysUser', [AdminCheck::class,MenuAuth::class]);
// 需要登录验证的用户相关路由
Route::group('sysUser', function () {
    Route::get('getUserInfo', 'app\dogadmin\controller\SysUser@getUserInfo');
    Route::post('resetPwd', 'app\dogadmin\controller\SysUser@resetPwd');
    Route::get('getPersonalData', 'app\dogadmin\controller\SysUser@getPersonalData');
    Route::post('updateBasicData', 'app\dogadmin\controller\SysUser@updateBasicData');
})->middleware([AdminCheck::class]);

Base::registerCrudRoutes('sysRole', 'app\dogadmin\controller\SysRole', [AdminCheck::class,MenuAuth::class]);
Route::group('sysRole', function () {
    Route::get('listRoleElSelect', 'app\dogadmin\controller\SysRole@listRoleElSelect');
})->middleware([AdminCheck::class]);



Route::group('sysMenu', function () {
    Route::get('listRouters', 'app\dogadmin\controller\SysMenu@listRouters');
    Route::get('listMenuNormal', 'app\dogadmin\controller\SysMenu@listMenuNormal');
    Route::get('listMenuIdsByRoleId', 'app\dogadmin\controller\SysMenu@listMenuIdsByRoleId');
    Route::post('saveRoleMenu', 'app\dogadmin\controller\SysMenu@saveRoleMenu');
    Route::get('cascaderList', 'app\dogadmin\controller\SysMenu@cascaderList');
    Route::post('addButtonAuth', 'app\dogadmin\controller\SysMenu@addButtonAuth');
})->middleware([AdminCheck::class]);

// 部门管理路由
Base::registerCrudRoutes('sysDept', 'app\dogadmin\controller\SysDept', [AdminCheck::class,MenuAuth::class]);
Route::group('sysDept', function () {
    Route::get('cascaderList', 'app\dogadmin\controller\SysDept@cascaderList');
    Route::get('listDeptIdsByRoleId', 'app\dogadmin\controller\SysDept@listDeptIdsByRoleId');
    Route::post('saveRoleDept', 'app\dogadmin\controller\SysDept@saveRoleDept');
})->middleware([AdminCheck::class]);

Base::registerCrudRoutes('sysDictData', 'app\dogadmin\controller\SysDictData', [AdminCheck::class,MenuAuth::class]);
Route::group('sysDictData', function () {
    Route::get('getDictDataByType', 'app\dogadmin\controller\SysDictData@getDictDataByType');
})->middleware([AdminCheck::class]);

Base::registerCrudRoutes('sysDictType', 'app\dogadmin\controller\SysDictType', [AdminCheck::class,MenuAuth::class]);

Base::registerCrudRoutes('sysFile', 'app\dogadmin\controller\SysFile', [AdminCheck::class,MenuAuth::class]);
Route::group('sysFile', function () {
    Route::post('upload', 'app\dogadmin\controller\SysFile@upload');
})->middleware([AdminCheck::class]);

Route::group('sysBuild', function () {
    Route::get('listTables', 'app\dogadmin\controller\SysBuild@listTables');
})->middleware([AdminCheck::class]);

