<?php

namespace app\dogadmin\controller;

use app\dogadmin\common\ApiResponse;
use app\dogadmin\service\SysRoleService;

class SysRole extends Base
{
    /**
     * @var SysRoleService
     */
    protected $service;
    protected function initialize(): void
    {
        // 子类可以重写此方法以添加自定义初始化逻辑
        $this->service = new SysRoleService();
        $this->params = $this->request->param();
    }
    public function listRoleElSelect(){
        $res = $this->service->listRoleElSelect();
        return ApiResponse::success($res,'获取成功');
    }
}