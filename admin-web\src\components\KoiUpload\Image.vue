<template>
  <div class="upload-box">
    <el-upload
      :id="uuid"
      :action="props.action"
      :class="['upload', imageDisabled ? 'disabled' : '', drag ? 'no-border' : '']"
      :multiple="false"
      :disabled="imageDisabled"
      :show-file-list="false"
      :http-request="handleHttpUpload"
      :before-upload="beforeUpload"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :drag="drag"
      :accept="fileType.join(',')"
      :folderName="folderName"
      :fileParam="fileParam"
    >
      <template v-if="imageUrl">
        <img :src="imageUrl" class="upload-image" />
        <div class="upload-operate" @click.stop>
          <div v-if="!imageDisabled" class="upload-icon" @click="handleEditImage">
            <el-icon><Edit /></el-icon>
            <span>编辑</span>
          </div>
          <div class="upload-icon" @click="imageViewShow = true">
            <el-icon><ZoomIn /></el-icon>
            <span>查看</span>
          </div>
          <div v-if="!imageDisabled" class="upload-icon" @click="handleDeleteImage">
            <el-icon><Delete /></el-icon>
            <span>删除</span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="upload-content">
          <slot name="content">
            <el-icon><Plus /></el-icon>
            <!-- <span>请上传图片</span> -->
          </slot>
        </div>
      </template>
    </el-upload>
    <div class="upload-tip">
      <slot name="tip"></slot>
    </div>
    <el-image-viewer v-if="imageViewShow" :url-list="[imageUrl]" @close="imageViewShow = false" />
  </div>
</template>

<script setup lang="ts" name="KoiUploadImage">
import { ref, computed, inject } from "vue";
import { ElLoading } from "element-plus";
import { generateUUID } from "@/utils";
import koi from "@/utils/axios.ts";
import { ElNotification, formContextKey, formItemContextKey } from "element-plus";
import type { UploadProps, UploadRequestOptions } from "element-plus";

interface IUploadImageProps {
  imageUrl: string; // 图片地址 ==> 必传
  action?: string; // 上传图片的 api 方法，一般项目上传都是同一个 api 方法，在组件里直接引入即可 ==> 非必传
  drag?: boolean; // 是否支持拖拽上传 ==> 非必传[默认为 true]
  disabled?: boolean; // 是否禁用上传组件 ==> 非必传[默认为 false]
  fileSize?: number; // 图片大小限制 ==> 非必传[默认为 3M]
  fileType?: any; // 图片类型限制 ==> 非必传[默认为 ["image/webp","image/jpg", "image/jpeg", "image/png", "image/gif"]]
  height?: string; // 组件高度 ==> 非必传[默认为 120px]
  width?: string; // 组件宽度 ==> 非必传[默认为 120px]
  borderRadius?: string; // 组件边框圆角 ==> 非必传[默认为 6px]
  folderName?: string; // 后端文件夹名称
  fileParam?: string; // 文件类型[可向后端传递参数]
}

// 接收父组件参数
const props = withDefaults(defineProps<IUploadImageProps>(), {
  imageUrl: "",
  action: "/dogadmin/sysFile/upload",
  drag: true,
  disabled: false,
  fileSize: 3,
  fileType: () => ["image/webp", "image/jpg", "image/jpeg", "image/png", "image/gif"],
  height: "120px",
  width: "120px",
  borderRadius: "6px",
  folderName: "files",
  fileParam: "-1"
});

// 生成组件唯一id
const uuid = ref("id-" + generateUUID());

// 查看图片
const imageViewShow = ref(false);
// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0);
// 获取 el-form-item 组件上下文
const formItemContext = inject(formItemContextKey, void 0);
// 判断是否禁用上传和删除
const imageDisabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

/**
 * @description 图片上传
 * @param options upload 所有配置项
 * */
const emit = defineEmits<{
  "update:imageUrl": [value: string];
}>();
const handleHttpUpload = async (options: UploadRequestOptions) => {
  let formData = new FormData();
  formData.append("file", options.file);
  console.log(options.file);
  const loadingInstance = ElLoading.service({
    text: "正在上传",
    background: "rgba(0,0,0,.2)"
  });
  try {
    if (props.fileParam == "-1" || props.fileParam == "") {
      props.fileParam === "-1";
    }
    // props.action + "/" + props.fileSize + "/" + props.folderName + "/" + props.fileParam
    const res: any = await koi.upload(props.action, formData);
    emit("update:imageUrl", res.data.fileUploadPath);
    loadingInstance.close();
    // 调用 el-form 内部的校验方法[可自动校验]
    formItemContext?.prop && formContext?.validateField([formItemContext.prop as string]);
  } catch (error) {
    loadingInstance.close();
    options.onError(error as any);
  }
};

/**
 * @description 删除图片
 * */
const handleDeleteImage = () => {
  emit("update:imageUrl", "");
};

/**
 * @description 编辑图片
 * */
const handleEditImage = () => {
  const dom = document.querySelector(`#${uuid.value} .el-upload__input`);
  dom && dom.dispatchEvent(new MouseEvent("click"));
};

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload: UploadProps["beforeUpload"] = rawFile => {
  const imgSize = rawFile.size / 1024 / 1024 < props.fileSize;
  const imgType = props.fileType.includes(rawFile.type);
  if (!imgType)
    ElNotification({
      title: "温馨提示",
      message: "上传图片不符合所需的格式！",
      type: "warning"
    });
  if (!imgSize)
    setTimeout(() => {
      ElNotification({
        title: "温馨提示",
        message: `上传图片大小不能超过 ${props.fileSize}M！`,
        type: "warning"
      });
    }, 0);
  return imgType && imgSize;
};

/**
 * @description 图片上传成功
 * */
const uploadSuccess = () => {
  ElNotification({
    title: "温馨提示",
    message: "图片上传成功！",
    type: "success"
  });
};

/**
 * @description 图片上传错误
 * */
const uploadError = () => {
  ElNotification({
    title: "温馨提示",
    message: "图片上传失败，请您重新上传！",
    type: "error"
  });
};
</script>

<style scoped lang="scss">
.is-error {
  .upload {
    :deep(.el-upload),
    :deep(.el-upload-dragger) {
      border: 2px dashed var(--el-color-danger) !important;
      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}
:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed !important;
    background: var(--el-color-primary-light-9);
    border: 2px dashed var(--el-color-primary) !important;
    &:hover {
      border: 2px dashed var(--el-color-primary) !important;
    }
  }
}
.upload-box {
  .no-border {
    :deep(.el-upload) {
      border: none !important;
    }
  }
  :deep(.upload) {
    .el-upload {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: v-bind(width);
      height: v-bind(height);
      overflow: hidden;
      border: 2px dashed var(--el-color-primary);
      border-radius: v-bind(borderRadius);
      transition: var(--el-transition-duration-fast);
      &:hover {
        background: var(--el-color-primary-light-9);
        .upload-operate {
          opacity: 1;
        }
      }
      .el-upload-dragger {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0;
        overflow: hidden;
        background-color: transparent;
        border: 2px dashed var(--el-color-primary);
        border-radius: v-bind(borderRadius);
        &:hover {
          border: 2px dashed var(--el-color-primary);
        }
      }
      .el-upload-dragger.is-dragover {
        background-color: var(--el-color-primary-light-9);
        border: 2px dashed var(--el-color-primary) !important;
      }
      .upload-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      .upload-content {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        line-height: 30px;
        color: var(--el-color-primary);
        .el-icon {
          font-size: 28px;
          color: var(--el-color-primary);
        }
      }
      .upload-operate {
        position: absolute;
        top: 0;
        right: 0;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        cursor: pointer;
        background: rgb(0 0 0 / 50%);
        opacity: 0;
        transition: var(--el-transition-duration-fast);
        .upload-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 0 6%;
          color: var(--el-color-primary-light-2);
          .el-icon {
            margin-bottom: 40%;
            font-size: 130%;
            line-height: 130%;
          }
          span {
            font-size: 85%;
            line-height: 85%;
          }
        }
      }
    }
  }
  .upload-tip {
    font-size: 12px;
    line-height: 26px;
    color: var(--el-color-primary);
    text-align: left;
  }
}
</style>
