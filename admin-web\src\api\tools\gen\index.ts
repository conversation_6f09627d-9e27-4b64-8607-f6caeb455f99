// 导入二次封装axios
import koi from "@/utils/axios.ts";

// 统一管理接口
enum API {
  LIST_PAGE = "/koi/genTable/listPage",
  LIST_ALL_TABLE_NAME = "/koi/genTable/listAllTableName",
  IMPORT_GEN_TABLE = "/koi/genTable/importGenTable",
  GET_BY_ID = "/koi/genTable/getById",
  UPDATE = "/koi/genTable/update",
  ADD = "/koi/genTable/add",
  DELETE = "/koi/genTable/deleteById",
  BATCH_DELETE = "/koi/genTable/batchDelete",
  GET_BY_TABLE_NAME = "/koi/genTable/getByTableName",
  LIST_COLUMN = "/koi/genColumn/listColumn",
  SYNCHRONOUS_DATA = "/koi/genTable/synchronousData",
  SAVE_COLUMN_DATA = "/koi/genTable/saveColumnData",
  PREVIEW_PERFECT_CODE = "/koi/genTable/previewPerfectCode",
  GEN_PERFECT_CODE = "/koi/genTable/generatePerfectCode",
  LIST_ELSELECT_COLUMN = "/koi/genColumn/listElSelectColumn"
}
// 暴露请求函数
// 多条件分页查询数据
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 多条件分页查询导入表数据
export const listTableNamePage = (params: any) => {
  return koi.get(API.LIST_ALL_TABLE_NAME, params);
};

// 导入表数据
export const importGenTable = (params: any) => {
  return koi.post(API.IMPORT_GEN_TABLE, params);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "/" + id);
};

// 根据ID进行修改
export const update = (data: any) => {
  return koi.post(API.UPDATE, data);
};

// 添加
export const add = (data: any) => {
  return koi.post(API.ADD, data);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE + "/" + id);
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids});
};

// 根据表名进行查询
export const getByTableName = (tableSchema: any, tableName: any) => {
  return koi.get(API.GET_BY_TABLE_NAME + "/" + tableSchema + "/" + tableName);
};

// 查询表的列相关数据
export const listColumn = (tableSchema: any, tableName: any) => {
  return koi.get(API.LIST_COLUMN + "/" + tableSchema + "/" + tableName);
};

// 同步数据
export const synchronousData = (tableSchema: any, tableName: any) => {
  return koi.get(API.SYNCHRONOUS_DATA + "/" + tableSchema + "/" + tableName);
};

// 保存列数据
export const saveColumnData = (data: any) => {
  return koi.post(API.SAVE_COLUMN_DATA, data);
};

// 预览代码
export const previewPerfectCode = (type: any, genCodeList: any) => {
  return koi.post(API.PREVIEW_PERFECT_CODE + "/" + type, genCodeList);
};

// 生成代码
export const genPerfectCode = (type: any, genCodeList: any) => {
  return koi.download(API.GEN_PERFECT_CODE + "/" + type, genCodeList);
};

// 查询树形下拉框数据
export const listElSelectColumn = (tableSchema: any , tableName: any) => {
  return koi.get(API.LIST_ELSELECT_COLUMN + "/" + tableSchema + "/" + tableName);
};




