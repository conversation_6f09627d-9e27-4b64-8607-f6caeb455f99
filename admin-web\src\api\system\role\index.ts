// 导入二次封装axios
import koi from "@/utils/axios.ts";
import { ITableParams } from "./type.ts";

// 统一管理接口
enum API {
  LIST_PAGE = "/dogadmin/sysRole/listPage",
  GET_BY_ID = "/dogadmin/sysRole/getById",
  UPDATE = "/dogadmin/sysRole/update",
  ADD = "/dogadmin/sysRole/add",
  DELETE = "/dogadmin/sysRole/deleteById",
  BATCH_DELETE = "/dogadmin/sysRole/batchDelete",
  UPDATE_STATUE = "/dogadmin/sysRole/updateStatus",
  LIST_NORMAL_ROLE = "/dogadmin/sysRole/listNormalRole",
  ASSIGN_USER_ROLE = "/dogadmin/sysRole/assignUserRole",
  LIST_ROLE_ELSELECT = "/dogadmin/sysRole/listRoleElSelect",
  GET_SORTED = "/dogadmin/sysRole/getSorted"
}
// 暴露请求函数

// 多条件分页查询数据
export const listPage = (params) => {
  return koi.get(API.LIST_PAGE, params);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "?id=" + id);
};

// 根据ID进行修改
export const update = (data: any) => {
  return koi.post(API.UPDATE, data);
};

// 添加
export const add = (data: any) => {
  return koi.post(API.ADD, data);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE + "?id=" + id);
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids});
};

// 修改状态
export const updateStatus = (id: any, status: any) => {
  return koi.post(API.UPDATE_STATUE ,{id: id, status});
};

// 查询所有正常角色[穿梭框]
export const listNormalRole = (user_id: any) => {
  return koi.get(API.LIST_NORMAL_ROLE + "?user_id=" + user_id);
};

// 根据当前用户ID分配角色
export const assignUserRole = (user_id: any, roleIds: any) => {
  if(!roleIds || roleIds.length === 0){
    roleIds = [-1];
  }
  return koi.get(API.ASSIGN_USER_ROLE + "?user_id=" + user_id + "&roleIds=" + roleIds);
};

// 查询角色下拉框
export const listRoleElSelect = () => {
  return koi.get(API.LIST_ROLE_ELSELECT);
};

// 获取最新排序
export const getSorted = () => {
  return koi.get(API.GET_SORTED);
};

