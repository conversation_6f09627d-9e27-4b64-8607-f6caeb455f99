<?php
declare(strict_types=1);

namespace app\lib\exception;

/**
 * DebugException 使用示例
 */
class DebugExceptionExample
{
    /**
     * 示例：抛出包含字符串的调试异常
     */
    public static function demoString(): void
    {
        try {
            // 抛出包含字符串的调试异常
            throw new DebugException('这是一个测试字符串', 9999, '字符串调试');
        } catch (DebugException $e) {
            echo "捕获到调试异常：" . $e->getMessage() . "\n";
            echo "数据类型：" . $e->getDataType() . "\n";
            echo "调试数据：";
            print_r($e->getData());
            echo "\n";
        }
    }

    /**
     * 示例：抛出包含数组的调试异常
     */
    public static function demoArray(): void
    {
        $testArray = [
            'name' => '张三',
            'age' => 25,
            'skills' => ['PHP', 'JavaScript', 'Python'],
            'contact' => [
                'email' => '<PERSON><PERSON><PERSON>@example.com',
                'phone' => '13800138000'
            ]
        ];

        try {
            // 抛出包含数组的调试异常
            throw new DebugException($testArray, 9999, '数组调试');
        } catch (DebugException $e) {
            echo "捕获到调试异常：" . $e->getMessage() . "\n";
            echo "数据类型：" . $e->getDataType() . "\n";
            echo "调试数据：";
            print_r($e->getData());
            echo "\n";
        }
    }

    /**
     * 示例：抛出包含对象的调试异常
     */
    public static function demoObject(): void
    {
        // 创建一个测试对象
        $testObject = new \stdClass();
        $testObject->name = '李四';
        $testObject->age = 30;
        $testObject->address = '北京市海淀区';

        try {
            // 抛出包含对象的调试异常
            throw new DebugException($testObject, 9999, '对象调试');
        } catch (DebugException $e) {
            echo "捕获到调试异常：" . $e->getMessage() . "\n";
            echo "数据类型：" . $e->getDataType() . "\n";
            echo "调试数据：";
            print_r($e->getData());
            echo "\n";
        }
    }

    /**
     * 示例：抛出包含JSON的调试异常
     */
    public static function demoJson(): void
    {
        $jsonString = '{"name":"王五","age":35,"hobbies":["读书","旅游","摄影"]}';

        try {
            // 抛出包含JSON的调试异常
            throw new DebugException($jsonString, 9999, 'JSON调试');
        } catch (DebugException $e) {
            echo "捕获到调试异常：" . $e->getMessage() . "\n";
            echo "数据类型：" . $e->getDataType() . "\n";
            echo "调试数据：";
            print_r($e->getData());
            echo "\n";
        }
    }

    /**
     * 示例：使用静态dump方法快速抛出调试异常
     */
    public static function demoDump(): void
    {
        try {
            // 使用静态方法快速抛出调试异常
            DebugException::dump(['id' => 1, 'name' => '赵六'], '使用dump方法');
        } catch (DebugException $e) {
            echo "捕获到调试异常：" . $e->getMessage() . "\n";
            echo "数据类型：" . $e->getDataType() . "\n";
            echo "调试数据：";
            print_r($e->getData());
            echo "\n";
        }
    }

    /**
     * 示例：使用静态print方法打印调试信息（不抛出异常）
     */
    public static function demoPrint(): void
    {
        // 使用静态方法打印调试信息（不抛出异常）
        DebugException::print(['id' => 2, 'name' => '钱七'], '使用print方法');

        // 继续执行后续代码
        echo "这行代码会继续执行，因为print方法不会抛出异常\n";
    }

    /**
     * 运行所有示例
     */
    public static function runAll(): void
    {
        echo "=== 字符串示例 ===\n";
        self::demoString();

        echo "\n=== 数组示例 ===\n";
        self::demoArray();

        echo "\n=== 对象示例 ===\n";
        self::demoObject();

        echo "\n=== JSON示例 ===\n";
        self::demoJson();

        echo "\n=== 使用dump方法 ===\n";
        self::demoDump();

        echo "\n=== 使用print方法 ===\n";
        self::demoPrint();
    }
}

// 如果直接运行此文件，则执行所有示例
if (basename($_SERVER['SCRIPT_FILENAME']) === basename(__FILE__)) {
    DebugExceptionExample::runAll();
}
