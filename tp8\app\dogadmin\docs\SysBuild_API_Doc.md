# 代码生成器 API 文档

## 概述

代码生成器是一个用于自动生成模型、服务和控制器文件的工具，支持数据库表的增删改查操作。通过简单的配置，可以快速生成符合项目规范的代码文件，提高开发效率。

## 使用流程

1. 获取数据库表列表
2. 选择需要生成代码的表
3. 获取表字段信息
4. 配置生成参数（模块名、控制器名等）
5. 预览生成的代码
6. 生成代码文件
7. 获取API接口文档

## API 接口

### 1. 获取数据库表列表

获取当前数据库中的所有表信息。

- **URL**: `/dogadmin/sysBuild/listTables`
- **Method**: `GET`
- **请求参数**: 无
- **响应示例**:

```json
{
  "code": 0,
  "msg": "获取成功",
  "data": [
    {
      "name": "sys_user",
      "comment": "系统用户表"
    },
    {
      "name": "sys_role",
      "comment": "角色表"
    }
  ]
}
```

### 2. 获取表字段信息

获取指定表的字段信息，包括字段名、类型、注释等。

- **URL**: `/dogadmin/sysBuild/getTableInfo`
- **Method**: `GET`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| table_name | string | 是 | 表名 |

- **响应示例**:

```json
{
  "code": 0,
  "msg": "获取成功",
  "data": {
    "table": {
      "TABLE_NAME": "sys_user",
      "TABLE_COMMENT": "系统用户表"
    },
    "columns": [
      {
        "name": "id",
        "type": "int",
        "comment": "用户ID",
        "key": "PRI",
        "nullable": "NO",
        "default": null
      },
      {
        "name": "username",
        "type": "varchar",
        "comment": "用户名",
        "key": "",
        "nullable": "NO",
        "default": ""
      }
    ]
  }
}
```

### 3. 预览生成的代码

预览将要生成的模型、服务和控制器代码，不实际写入文件。

- **URL**: `/dogadmin/sysBuild/preview`
- **Method**: `POST`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| module_name | string | 是 | 模块名称，如 dogadmin |
| controller_name | string | 是 | 控制器名称，如 SysFile |
| table_name | string | 是 | 数据库表名 |
| table_comment | string | 否 | 表注释，默认使用控制器名 |
| model_fields | array | 否 | 模型字段信息，不提供则自动从数据库获取 |
| search_fields | array | 否 | 搜索字段配置 |

- **响应示例**:

```json
{
  "code": 0,
  "msg": "预览成功",
  "data": {
    "model": {
      "name": "SysFileModel",
      "content": "<?php\n\nnamespace app\\dogadmin\\model;\n\n/**\n * SysFile 模型\n */\nclass SysFileModel extends BaseModel\n{\n    // 设置表名\n    protected \$table = 'sys_file';\n\n    // 设置字段信息\n    protected \$schema = [\n        'id' => 'int',\n        'create_time' => 'datetime',\n        'update_time' => 'datetime',\n        'delete_time' => 'datetime'\n    ];\n\n    // 设置字段自动完成\n    protected \$autoWriteTimestamp = true;\n\n    // 追加属性\n    protected \$append = [];\n}\n"
    },
    "service": {
      "name": "SysFileService",
      "content": "...服务类代码..."
    },
    "controller": {
      "name": "SysFile",
      "content": "...控制器代码..."
    }
  }
}
```

### 4. 生成代码

生成模型、服务和控制器文件。

- **URL**: `/dogadmin/sysBuild/generate`
- **Method**: `POST`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| module_name | string | 是 | 模块名称，如 dogadmin |
| controller_name | string | 是 | 控制器名称，如 SysFile |
| table_name | string | 是 | 数据库表名 |
| table_comment | string | 否 | 表注释，默认使用控制器名 |
| model_fields | array | 否 | 模型字段信息，不提供则自动从数据库获取 |
| search_fields | array | 否 | 搜索字段配置 |

- **响应示例**:

```json
{
  "code": 0,
  "msg": "生成成功",
  "data": {
    "model": {
      "path": "E:\\work_code\\baseadmin_thinkphp8\\tp8\\app\\dogadmin\\model\\SysFileModel.php",
      "content": "...模型代码...",
      "success": true
    },
    "service": {
      "path": "E:\\work_code\\baseadmin_thinkphp8\\tp8\\app\\dogadmin\\service\\SysFileService.php",
      "content": "...服务代码...",
      "success": true
    },
    "controller": {
      "path": "E:\\work_code\\baseadmin_thinkphp8\\tp8\\app\\dogadmin\\controller\\SysFile.php",
      "content": "...控制器代码...",
      "success": true
    }
  }
}
```

### 5. 获取API接口文档

获取生成的控制器API接口文档。

- **URL**: `/dogadmin/sysBuild/getApiDocs`
- **Method**: `GET`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| module_name | string | 是 | 模块名称，如 dogadmin |
| controller_name | string | 是 | 控制器名称，如 SysFile |
| table_comment | string | 否 | 表注释，默认使用控制器名 |
| model_fields | array | 否 | 模型字段信息，不提供则自动从数据库获取 |

- **响应示例**:

```json
{
  "code": 0,
  "msg": "获取成功",
  "data": [
    {
      "name": "获取分页列表",
      "url": "/dogadmin/sysfile/listPage",
      "method": "GET",
      "params": [
        {
          "name": "page",
          "type": "int",
          "required": false,
          "default": 1,
          "desc": "页码"
        },
        {
          "name": "limit",
          "type": "int",
          "required": false,
          "default": 15,
          "desc": "每页数量"
        }
      ],
      "response": {
        "code": 0,
        "msg": "获取成功",
        "count": "总记录数",
        "data": "数据列表"
      }
    },
    {
      "name": "根据ID获取详情",
      "url": "/dogadmin/sysfile/getById",
      "method": "GET",
      "params": [
        {
          "name": "id",
          "type": "int",
          "required": true,
          "desc": "记录ID"
        }
      ],
      "response": {
        "code": 0,
        "msg": "获取成功",
        "data": "数据详情"
      }
    }
  ]
}
```

## 生成的文件说明

### 1. 模型文件

生成的模型文件继承自 `BaseModel`，包含以下内容：

- 表名设置
- 字段信息定义
- 自动时间戳设置
- 追加属性设置

### 2. 服务文件

生成的服务文件继承自 `BaseService`，包含以下方法：

- `listPage`: 获取分页列表数据
- `getById`: 获取单条数据
- `add`: 新增数据
- `update`: 更新数据
- `deleteById`: 删除数据
- `batchDelete`: 批量删除数据
- `updateStatus`: 更新状态

### 3. 控制器文件

生成的控制器文件继承自 `Base`，包含以下方法：

- `listPage`: 获取分页列表
- `getById`: 根据ID获取详情
- `add`: 新增数据
- `update`: 更新数据
- `deleteById`: 删除数据
- `batchDelete`: 批量删除数据
- `updateStatus`: 更新状态

## 前端传参说明

### 1. 列表查询参数

```javascript
// 列表查询参数示例
const params = {
  page: 1,                // 页码
  limit: 15,              // 每页数量
  order: 'id desc',       // 排序字段
  // 搜索条件（根据searchKey配置）
  name: '测试',           // 等值查询
  status: 1,              // 等值查询
  create_time: ['2023-01-01', '2023-12-31'] // 范围查询
};
```

### 2. 新增/更新参数

```javascript
// 新增参数示例
const addParams = {
  name: '测试名称',
  status: 1,
  remark: '备注信息'
  // 其他字段...
};

// 更新参数示例
const updateParams = {
  id: 1,                  // 必填，记录ID
  name: '更新后的名称',
  status: 0,
  // 其他字段...
};
```

### 3. 删除参数

```javascript
// 单条删除参数
const deleteParams = {
  id: 1                   // 记录ID
};

// 批量删除参数
const batchDeleteParams = {
  ids: '1,2,3,4,5'        // 多个ID用逗号分隔
};
```

### 4. 更新状态参数

```javascript
// 更新状态参数
const statusParams = {
  id: 1,                  // 记录ID
  status: 1               // 状态值
};
```

## 注意事项

1. 生成的代码文件会自动放置在对应的模块目录下，如果目录不存在会自动创建。
2. 如果文件已存在，生成操作会覆盖原有文件，请注意备份重要文件。
3. 生成的代码仅包含基础的CRUD功能，复杂业务逻辑需要手动添加。
4. 搜索字段配置决定了前端可以传入哪些搜索条件，建议根据实际需求配置。
5. 生成的API接口遵循RESTful风格，可以通过路由规则进行访问。