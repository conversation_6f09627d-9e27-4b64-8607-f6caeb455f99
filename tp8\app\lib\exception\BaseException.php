<?php
declare(strict_types=1);

namespace app\lib\exception;

use Exception;
use Throwable;

/**
 * 基础异常类
 * 所有自定义异常的基类
 */
class BaseException extends Exception
{
    /**
     * 错误码
     * @var int
     */
    protected $code = 1;

    /**
     * 错误消息
     * @var string
     */
    protected string $msg = '系统错误';

    /**
     * 附加数据
     * @var array
     */
    protected array $data = [];

    /**
     * 错误码映射
     * @var array
     */
    protected static array $errorCodes = [
        1 => '系统错误',
        2 => 'Token已过期',
        3 => 'Token验证失败',
        4 => '权限不足',
        5 => '账号已被禁用',
        6 => '账号不存在'
    ];

    /**
     * 构造函数
     * 
     * @param array $data 附加数据
     * @param int $code 错误码
     * @param string $message 自定义错误消息，为空时使用错误码映射的消息
     * @param Throwable|null $previous 上一个异常
     */
    public function __construct(array $data = [], int $code = 1, string $message = '', ?Throwable $previous = null)
    {
        $this->data = $data;
        $this->code = $code;
        
        // 如果没有提供自定义消息，则使用错误码映射的消息
        if (empty($message)) {
            $this->msg = static::$errorCodes[$code] ?? '未知错误';
        } else {
            $this->msg = $message;
        }

        parent::__construct($this->msg, $code, $previous);
    }

    /**
     * 获取错误消息
     * 
     * @return string
     */
    public function getMsg(): string
    {
        return $this->msg;
    }

    /**
     * 获取附加数据
     * 
     * @return array
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * 转换为数组
     * 
     * @return array
     */
    public function toArray(): array
    {
        return [
            'code' => $this->code,
            'msg' => $this->msg,
            'data' => $this->data
        ];
    }
}