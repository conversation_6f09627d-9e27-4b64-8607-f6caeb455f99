// 导入二次封装axios
import koi from "@/utils/axios.ts";

// 统一管理接口
enum API {
  LIST_TABLES = "/dogadmin/sysBuild/listTables",
  GET_TABLE_INFO = "/dogadmin/sysBuild/getTableInfo",
  PREVIEW = "/dogadmin/sysBuild/preview",
  GENERATE = "/dogadmin/sysBuild/generate",
  GET_API_DOCS = "/dogadmin/sysBuild/getApiDocs",
  GET_MENU_LIST = "/dogadmin/sysBuild/getMenuList"
}

// 获取数据库表列表
export const listTables = (params?: { page_no?: number; page_size?: number }) => {
  return koi.get(API.LIST_TABLES, params);
};

// 获取表字段信息
export const getTableInfo = (tableName: string) => {
  return koi.get(API.GET_TABLE_INFO, { table_name: tableName });
};

// 预览生成的代码
export const previewCode = (data: any) => {
  return koi.post(API.PREVIEW, data);
};

// 生成代码
export const generateCode = (data: any) => {
  return koi.post(API.GENERATE, data);
};

// 获取API接口文档
export const getApiDocs = (data: any) => {
  return koi.get(API.GET_API_DOCS, data);
};

// 获取菜单列表
export const getMenuList = () => {
  return koi.get(API.GET_MENU_LIST);
};