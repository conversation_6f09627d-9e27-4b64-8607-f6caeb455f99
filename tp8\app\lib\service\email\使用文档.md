# 邮件服务模块使用文档

## 模块概述
邮件服务模块提供了完整的邮件发送功能，包括：
- 邮件配置管理
- 邮件模板管理
- HTML邮件发送
- 验证码邮件发送

## 文件结构
```
email/
├── EmailService.php   # 核心邮件发送服务
├── MailConfig.php     # 邮件配置管理
├── MailTemplate.php   # 邮件模板管理
└── 使用文档.md        # 本使用文档
```

## 组件说明

### 1. EmailService
核心邮件发送服务，提供以下方法：
- `sendHtml($to, $subject, $content)` - 发送HTML格式邮件
- `sendText($to, $subject, $content)` - 发送纯文本邮件

### 2. MailConfig
邮件配置管理，提供以下方法：
- `getConfig()` - 获取当前邮件配置
- `setTempConfig($config)` - 设置临时配置
- `resetConfig()` - 重置为默认配置

默认配置项：
```php
[
    'host' => '',       // SMTP服务器
    'port' => 465,      // SMTP端口
    'username' => '',   // 邮箱账号
    'password' => '',   // 邮箱密码/授权码
    'from' => '',       // 发件人邮箱
    'fromName' => '',   // 发件人名称
    'charset' => 'UTF-8',
    'smtpSecure' => 'ssl',
    'timeout' => 30
]
```

### 3. MailTemplate
邮件模板管理，提供以下方法：
- `setTemplateDir($dir)` - 设置模板目录
- `assign($name, $value)` - 分配模板变量
- `render($template)` - 渲染模板
- `fetch($template)` - 获取模板内容

## 配置说明

1. 在项目配置文件中添加邮件配置：
```php
// config/mail.php
return [
    'host' => 'smtp.example.com',
    'port' => 465,
    'username' => '<EMAIL>',
    'password' => 'your_password_or_token',
    'from' => '<EMAIL>',
    'fromName' => 'Your Site Name',
    'template_dir' => 'path/to/email/templates'
];
```

2. 或者通过环境变量配置：
```
MAIL_HOST=smtp.example.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_password_or_token
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME=Your Site Name
```

## 使用示例

### 1. 发送验证码邮件
```php
use app\lib\service\email\EmailService;

$emailService = new EmailService();
$result = $emailService->sendHtml(
    '<EMAIL>',
    '您的验证码',
    '<p>您的验证码是：<strong>123456</strong></p>'
);
```

### 2. 使用模板发送邮件
```php
use app\lib\service\email\{EmailService, MailTemplate};

$template = new MailTemplate();
$template->assign('code', '123456');
$content = $template->render('verify_code.html');

$emailService = new EmailService();
$result = $emailService->sendHtml(
    '<EMAIL>',
    '您的验证码',
    $content
);
```

### 3. 临时修改配置
```php
use app\lib\service\email\{EmailService, MailConfig};

MailConfig::setTempConfig([
    'from' => '<EMAIL>',
    'fromName' => 'No Reply'
]);

$emailService = new EmailService();
// 发送邮件...
```

## 注意事项
1. 生产环境建议使用SSL加密连接
2. 密码建议使用授权码而非邮箱密码
3. 发送频率需控制，避免被识别为垃圾邮件
4. 模板文件需放在配置的template_dir目录下
5. 发送失败时会记录错误日志
```