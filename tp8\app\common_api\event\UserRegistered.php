<?php

namespace app\common_api\event;

/**
 * 用户注册成功事件
 */
class UserRegistered
{
    /**
     * 用户ID
     * @var int
     */
    public $userId;

    /**
     * 用户数据
     * @var array
     */
    public $userData;

    /**
     * 注册时间
     * @var int
     */
    public $registerTime;

    /**
     * 注册类型
     * @var string
     */
    public $registerType;

    /**
     * 构造函数
     *
     * @param int $userId 用户ID
     * @param array $userData 用户数据
     * @param string $registerType 注册类型（normal:普通注册, email:邮箱注册）
     */
    public function __construct(int $userId, array $userData, string $registerType = 'normal')
    {
        $this->userId = $userId;
        $this->userData = $userData;
        $this->registerTime = time();
        $this->registerType = $registerType;
    }
}
