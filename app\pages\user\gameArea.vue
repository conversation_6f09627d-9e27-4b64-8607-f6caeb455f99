<template>
	<view class="container">
		<view class="content">
			<view class="nickname-container">
				<tui-list-cell :hover="false" :padding="listCellPadding" unlined @click="showPlatformHandle">
					<tui-input padding="20rpx 30rpx" border-color="#979BB5" label="平台" radius="30"
						inputBorder disabled placeholder="请选择平台"></tui-input>
				</tui-list-cell>
				<tui-list-cell :hover="false" :padding="listCellPadding" unlined>
					<tui-input  padding="20rpx 30rpx" border-color="#979BB5" label="大区" radius="30"
						inputBorder placeholder="请输入大区数字" type="number"></tui-input>
				</tui-list-cell>
				<tui-list-cell :hover="false" :padding="listCellPadding" unlined>
					<tui-input  padding="20rpx 30rpx" border-color="#979BB5" label="ID" radius="30"
						inputBorder placeholder="请输入游戏ID"></tui-input>
				</tui-list-cell>
			</view>
			<view class="tips pd-20">小技巧：点击蓝色游戏ID，可以复制</view>
			<tui-button>保存</tui-button>
			
			<tui-select :list="platformList" :show="showPlatform" @confirm="confirmPlatform" @close="onClosePlatform"></tui-select>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex';
	export default {
		data() {
			return {
				listCellPadding: "20rpx 30rpx",
				showPlatform:false,
				platformList:['微（微信）','tap（TapTap）','抖（抖音）','ios（苹果）','支（支付宝）'],
				region:'',
				formData:{
					userId:'',
					
				}
			}
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		methods: {
			changeAvatar() {
				// 处理修改头像的逻辑
			},
			showPlatformHandle(){
				this.showPlatform = true;
			},
			confirmPlatform(e){
				console.log('confirmPlatform',e);
			},
			onClosePlatform(){
				this.showPlatform = false;
			}
		}
	}
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		// padding: 20px;
		background-color: #f5f5f5;

		.content {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;
			background-color: #fff;
			padding: 40rpx;

			.avatar-container {
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-bottom: 20px;

				.avatar {
					width: 100px;
					height: 100px;
					border-radius: 50%;
				}

				.change-avatar {
					margin-top: 10px;
					font-size: 16px;
					color: #000;
				}
			}

			.nickname-container {
				display: flex;
				align-items: center;
				flex-direction: column;
				width: 100%;
				// margin-bottom: 20px;

				.nickname-input {
					flex: 1;
					padding: 10px;
					font-size: 16px;
					border: 1px solid #ccc;
					border-radius: 5px;
				}

				.char-count {
					margin-left: 10px;
					font-size: 16px;
					color: #999;
				}
			}

			.save-button {
				width: 100%;
				padding: 10px;
				font-size: 16px;
				color: #fff;
				background-color: #007bff;
				border: none;
				border-radius: 5px;
			}
		}
	}
</style>