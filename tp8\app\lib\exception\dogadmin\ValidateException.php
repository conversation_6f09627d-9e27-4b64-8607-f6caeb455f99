<?php
declare(strict_types=1);

namespace app\lib\exception\dogadmin;

use app\lib\exception\BaseException;
use Throwable;

/**
 * 验证异常类
 * 处理数据验证相关的异常
 */
class ValidateException extends BaseException
{
    /**
     * 验证异常的默认错误码起始值
     */
    const VALIDATION_ERROR_CODE_BASE = 4000;

    /**
     * 错误码映射
     * @var array
     */
    protected static array $errorCodes = [
        4001 => '参数验证失败',
        4002 => '必填字段为空',
        4003 => '数据格式错误',
        4004 => '数据不符合规则'
    ];

    /**
     * 构造函数
     * 
     * @param array $data 附加数据，通常包含验证失败的字段信息
     * @param int $code 错误码
     * @param string $message 自定义错误消息，为空时使用错误码映射的消息
     * @param Throwable|null $previous 上一个异常
     */
    public function __construct(array $data = [], int $code = 4001, string $message = '', ?Throwable $previous = null)
    {
        // 确保错误码在验证异常的范围内
        if ($code < self::VALIDATION_ERROR_CODE_BASE) {
            $code = 4001;
        }
        
        parent::__construct($data, $code, $message, $previous);
    }

    /**
     * 快速创建验证异常
     * 
     * @param string $field 验证失败的字段名
     * @param string $message 错误消息
     * @return static
     */
    public static function invalidField(string $field, string $message): self
    {
        return new static(['field' => $field], 4001, $message);
    }

    /**
     * 创建必填字段验证失败异常
     * 
     * @param string $field 必填字段名
     * @return static
     */
    public static function requiredField(string $field): self
    {
        return new static(
            ['field' => $field],
            4002,
            sprintf('字段 %s 不能为空', $field)
        );
    }
}