<?php

namespace app\dogadmin\controller;

use app\dogadmin\common\ApiResponse;
use app\dogadmin\service\SysMenuService;
use app\dogadmin\service\SysRoleMenuService;
use think\Exception;

class SysMenu extends Base
{
    /**
     * @var SysMenuService
     */
    protected $service;
    protected function initialize(): void
    {
        // 子类可以重写此方法以添加自定义初始化逻辑
        $this->service = new SysMenuService();
        $this->params = $this->request->param();
    }

    public function listRouters()
    {
        $params = $this->params;
        $params['admin_id'] = request()->admin_id;
        $params['role_ids'] = request()->role_ids;
        $res = $this->service->listRouters($params);
        return ApiResponse::success($res);
    }

    public function listMenuNormal()
    {
        $list = $this->service->listMenuNormal();
        $res['menu_list'] = [];
        $res['spread_list'] = [];
        
        // 处理列表数据
        if (!empty($list)) {
            foreach ($list as $item) {
                // 检查isSpread属性
                if (isset($item['isSpread']) && $item['isSpread'] == 0) {
                    // 如果isSpread=0，添加到spreadList
                    $res['spread_list'][] = $item;
                } else {
                    // 否则添加到menuList
                    $res['menu_list'][] = $item;
                }
            }
        }
        
        return ApiResponse::success($res);
    }

    public function listMenuIdsByRoleId(){
        $params = $this->params;
        $roleId = $params['id'];
        $roleMenuService = new SysRoleMenuService();
        $menuIds = $roleMenuService->getRoleMenuByRoleId($roleId);
        return ApiResponse::success($menuIds);
    }

    public function saveRoleMenu()
    {
        $params = $this->params;
        $roleMenuService = new SysRoleMenuService();
        $res = $roleMenuService->saveRoleMenu($params);
        return ApiResponse::success($res);
    }

    public function cascaderList()
    {
        $res = $this->service->cascaderList();
        $res = (new SysMenuService())->cascaderList();
        return ApiResponse::success($res, '获取菜单列表成功');
    }

    public function addButtonAuth()
    {
        $params = $this->params;
        // 使用 explode 函数按冒号分割字符串
        $parts = explode(':', $params['auth']);

        // 初始化默认值
        $module = 'default';
        $controller = 'index';
        // 提取模块和控制器
        if (count($parts) >= 1) {
            $module = $parts[0]; // 模块
        }
        if (count($parts) >= 2) {
            $controller = $parts[1]; // 控制器
        }
        $data = [
          'parent_id'=>$params['parent_id'],
          'module'=>$module,
          'controller'=>$controller,
        ];
        $res = $this->service->addButtonAuth($data);
        return ApiResponse::success($res, '自动添加成功');
    }

    /**
     * 批量删除
     * @param
     * @return \think\Response
     */
    public function batchDelete()
    {
        try {
            $params = $this->params;
            if (empty($params['ids']) || !is_array($params['ids'])) {
                return ApiResponse::paramError('缺少批量主键参数ids');
            }
            $res = $this->service->batchDelete($params,true);
            return ApiResponse::success($res);
        } catch (Exception $e) {
            return ApiResponse::systemError($e->getMessage());
        }
    }

}