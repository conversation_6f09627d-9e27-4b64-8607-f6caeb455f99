<template>
  <div class="koi-flex">
    <KoiCard>
      <!-- 搜索表单 -->
      <el-form :inline="true">
        <el-form-item label="id" prop="id">
          <el-input v-model="searchParams.id" placeholder="请输入id" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
        <el-form-item label="技能名称" prop="name">
          <el-input v-model="searchParams.name" placeholder="请输入技能名称" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch" v-auth="['gameSkill:listPage']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 操作按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5" v-auth="['gameSkill:add']">
          <el-button type="primary" icon="plus" plain @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['gameSkill:update']">
          <el-button type="success" icon="edit" plain @click="handleUpdate" :disabled="single">修改</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['gameSkill:deleteById']">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete" :disabled="multiple">删除</el-button>
        </el-col>
      </el-row>

      <div class="h-20px"></div>
      
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableList"
        border
        empty-text="暂时没有数据哟🌻"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" prop="id" width="80" align="center"></el-table-column>
        <el-table-column label="技能名称" prop="name" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="技能描述" prop="desc" width="250" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="技能图标路径" prop="icon" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="最大等级" prop="max_level" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="消耗数值" prop="cost" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="消耗类型" prop="cost_type" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="冷却时间" prop="cooldown" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="施法距离" prop="cast_range" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="施法时间（秒）" prop="cast_time" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="技能类型" prop="skill_type" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="技能效果JSON" prop="effects" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="排序" prop="sorted" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              active-value="1"
              inactive-value="0"
              inline-prompt
              @change="handleSwitch(scope.row)"
              v-auth="['game:gameSkill:update']"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="create_time" width="180" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="更新时间" prop="update_time" width="180" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="操作" align="center" width="120" fixed="right" v-auth="['gameSkill:update', 'gameSkill:deleteById']">
          <template #default="{ row }">
            <el-tooltip content="修改🌻" placement="top">
              <el-button
                type="primary"
                icon="Edit"
                circle
                plain
                @click="handleUpdate(row)"
                v-auth="['gameSkill:update']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button
                type="danger"
                icon="Delete"
                circle
                plain
                @click="handleDelete(row)"
                v-auth="['gameSkill:deleteById']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="h-20px"></div>
      
      <!-- 分页 -->
      <el-pagination
        background
        v-model:current-page="searchParams.page_no"
        v-model:page-size="searchParams.page_size"
        v-show="total > 0"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <!-- 添加 OR 修改 -->
      <KoiDrawer
        ref="koiDrawerRef"
        :title="title"
        @koiConfirm="handleConfirm"
        @koiCancel="handleCancel"
        :loading="confirmLoading"
        size="100%"
      >
        <template #content>
          <el-form ref="formRef" :rules="rules" :model="form" label-width="120px" status-icon>
            <el-row>
              <el-col :span="12">
                <el-form-item label="id" prop="id">
                  <el-input v-model="form.id" placeholder="id" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="技能名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入技能名称" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="技能描述" prop="desc">
                  <el-input v-model="form.desc" type="textarea" :rows="3" placeholder="请输入技能描述" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="技能图标路径" prop="icon">
                  <el-input v-model="form.icon" placeholder="请输入技能图标路径" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大等级" prop="max_level">
                  <el-input-number v-model="form.max_level" :min="1" :max="100" placeholder="请输入最大等级" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="消耗数值" prop="cost">
                  <el-input-number v-model="form.cost" :min="0" :precision="2" :step="0.1" placeholder="请输入消耗数值" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="消耗类型" prop="cost_type">
                  <el-select v-model="form.cost_type" placeholder="请选择消耗类型" clearable style="width: 100%">
                    <el-option label="魔法值(mp)" value="mp" />
                    <el-option label="生命值(hp)" value="hp" />
                    <el-option label="怒气值(rage)" value="rage" />
                    <el-option label="能量值(energy)" value="energy" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="冷却时间" prop="cooldown">
                  <el-input-number v-model="form.cooldown" :min="0" :precision="2" :step="0.1" placeholder="请输入冷却时间" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="施法距离" prop="cast_range">
                  <el-input-number v-model="form.cast_range" :min="0" placeholder="请输入施法距离" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="施法时间（秒）" prop="cast_time">
                  <el-input-number v-model="form.cast_time" :min="0" :precision="2" :step="0.1" placeholder="请输入施法时间（秒）" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="技能类型" prop="skill_type">
                  <el-select v-model="form.skill_type" placeholder="请选择技能类型" clearable style="width: 100%">
                    <el-option label="主动技能(active)" value="active" />
                    <el-option label="被动技能(passive)" value="passive" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="排序" prop="sorted">
                  <el-input-number v-model="form.sorted" :min="1" placeholder="请输入排序" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio value="1">启用</el-radio>
                    <el-radio value="0">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              
              <!-- 技能效果编辑区域 -->
              <el-col :span="24">
                <el-divider content-position="center">技能效果编辑</el-divider>
                <el-form-item label="技能效果" prop="effects">
                  <div class="effects-editor">
                    <el-card v-for="(effect, effectIndex) in effectsArray" :key="effectIndex" class="effect-card mb-10px">
                      <template #header>
                        <div class="effect-card-header">
                          <span>效果 #{{ effectIndex + 1 }}</span>
                          <el-button type="danger" size="small" @click="removeEffect(effectIndex)" icon="Delete" circle plain></el-button>
                        </div>
                      </template>
                      
                      <el-row :gutter="20">
                        <el-col :span="8">
                          <el-form-item label="效果类型" label-width="100px">
                            <el-select v-model="effect.type" placeholder="请选择效果类型" style="width: 100%">
                              <el-option label="直接伤害(direct_damage)" value="direct_damage" />
                              <el-option label="持续伤害(dot_damage)" value="dot_damage" />
                              <el-option label="增益效果(buff)" value="buff" />
                              <el-option label="减益效果(debuff)" value="debuff" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="效果关键字" label-width="100px">
                            <el-select v-model="effect.key" placeholder="请选择效果关键字" style="width: 100%">
                              <el-option label="直接伤害(direct_damage)" value="direct_damage" />
                              <el-option label="持续伤害(dot_damage)" value="dot_damage" />
                              <el-option label="增益效果(buff)" value="buff" />
                              <el-option label="减益效果(debuff)" value="debuff" />
                            </el-select>
                          </el-form-item>
                          <!-- <el-form-item label="效果关键字" label-width="100px">
                            <el-input v-model="effect.key" placeholder="请输入效果关键字" />
                          </el-form-item> -->
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="触发概率" label-width="100px">
                            <el-input-number v-model="effect.chance" :min="0" :max="1" :precision="2" :step="0.01" style="width: 100%" :formatter="formatProbabilityInput" :parser="parseProbabilityInput" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="基础效果值" label-width="100px">
                            <el-input-number v-model="effect.value" :precision="2" :step="0.1" style="width: 100%" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="每级成长" label-width="100px">
                            <el-input-number v-model="effect.level_scale" :precision="2" :step="0.1" style="width: 100%" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="元素属性" label-width="100px">
                            <el-select v-model="effect.element" placeholder="请选择元素属性" clearable style="width: 100%">
                              <el-option label="火(fire)" value="fire" />
                              <el-option label="水(water)" value="water" />
                              <el-option label="土(earth)" value="earth" />
                              <el-option label="风(wind)" value="wind" />
                              <el-option label="光(light)" value="light" />
                              <el-option label="暗(dark)" value="dark" />
                              <el-option label="无" :value="null" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="目标" label-width="100px">
                            <el-select v-model="effect.target" placeholder="请选择目标" style="width: 100%">
                              <el-option label="全部(all)" value="all" />
                              <el-option label="自身(self)" value="self" />
                              <el-option label="敌方(enemy)" value="enemy" />
                              <el-option label="BOSS(boss)" value="boss" />
                              <el-option label="精英怪(elite)" value="elite" />
                              <el-option label="小怪(little)" value="little" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="目标数量" label-width="100px">
                            <el-input-number v-model="effect.target_num" :min="0" style="width: 100%" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="目标选择规则" label-width="100px">
                            <el-select v-model="effect.target_rule" placeholder="请选择目标规则" style="width: 100%">
                              <el-option label="生命最低(lowest_hp)" value="lowest_hp" />
                              <el-option label="生命最高(highest_hp)" value="highest_hp" />
                              <el-option label="随机(random)" value="random" />
                              <el-option label="最近(nearest)" value="nearest" />
                              <el-option label="最远(farthest)" value="farthest" />
                              <el-option label="默认(none)" value="none" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="影响范围" label-width="100px">
                            <el-input-number v-model="effect.aoe_radius" :min="0" style="width: 100%" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="范围类型" label-width="100px">
                            <el-select v-model="effect.aoe_type" placeholder="请选择范围类型" style="width: 100%">
                              <el-option label="圆形(circle)" value="circle" />
                              <el-option label="扇形(sector)" value="sector" />
                              <el-option label="矩形(rectangle)" value="rectangle" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="持续时间" label-width="100px">
                            <el-input-number v-model="effect.duration" :min="0" style="width: 100%" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="最大叠加层数" label-width="100px">
                            <el-input-number v-model="effect.max_stacks" :min="1" style="width: 100%" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="是否可被驱散" label-width="100px">
                            <el-switch v-model="effect.dispels" active-text="是" inactive-text="否" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      
                      <!-- 触发条件 -->
                      <el-divider content-position="left">触发条件</el-divider>
                      <div v-for="(condition, condIndex) in effect.condition" :key="condIndex" class="condition-item mb-10px">
                        <el-row :gutter="20">
                          <el-col :span="8">
                            <el-form-item label="属性" label-width="100px">
                              <el-input v-model="condition.attr" placeholder="请输入属性名称" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="8">
                            <el-form-item label="比较运算符" label-width="100px">
                              <el-select v-model="condition.expr" placeholder="请选择比较运算符" style="width: 100%">
                                <el-option label="等于(==)" value="==" />
                                <el-option label="不等于(!=)" value="!=" />
                                <el-option label="大于(>)" value=">" />
                                <el-option label="小于(<)" value="<" />
                                <el-option label="大于等于(>=)" value=">=" />
                                <el-option label="小于等于(<=)" value="<=" />
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="7">
                            <el-form-item label="条件值" label-width="100px">
                              <el-input-number v-model="condition.value" :precision="2" :step="0.1" style="width: 100%" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="1" class="flex-center">
                            <el-button type="danger" size="small" @click="removeCondition(effectIndex, condIndex)" icon="Delete" circle plain></el-button>
                          </el-col>
                        </el-row>
                      </div>
                      <el-button type="primary" size="small" @click="addCondition(effectIndex)" icon="Plus">添加条件</el-button>
                    </el-card>
                    <el-button type="primary" @click="addEffect" icon="Plus">添加效果</el-button>
                  </div>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="创建时间" prop="create_time">
                  <el-date-picker
                    v-model="form.create_time"
                    type="datetime"
                    placeholder="选择创建时间"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="更新时间" prop="update_time">
                  <el-date-picker
                    v-model="form.update_time"
                    type="datetime"
                    placeholder="选择更新时间"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </KoiDrawer>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="gameSkill">
import type { FormInstance } from 'element-plus';
import { nextTick, ref, reactive, onMounted, computed, watch } from 'vue';
import {
  koiNoticeSuccess,
  koiNoticeError,
  koiMsgSuccess,
  koiMsgError,
  koiMsgWarning,
  koiMsgBox,
  koiMsgInfo
} from "@/utils/koi.ts";
import { listPage, getById, add, update, deleteById, batchDelete, updateStatus, getSorted } from '@/api/game/gameSkill';
import { useKoiDict } from "@/hooks/dicts/index.ts";

const { koiDicts } = useKoiDict(["sys_switch_status"]);

// 表格数据
const loading = ref(false);
const tableList = ref<any[]>([]);
const selectedIds = ref<any[]>([]);

// 总条数
const total = ref<number>(0);

// 搜索表单
const searchParams = reactive({
  page_no: 1,
  page_size: 10,
  id: '',
  name: ''
});

// 重置搜索表单
const resetSearch = () => {
  searchParams.page_no = 1;
  searchParams.page_size = 10;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page_no' && key !== 'page_size') {
      searchParams[key] = '';
    }
  });
  handleListPage();
};

// 查询数据
const handleSearch = () => {
  searchParams.page_no = 1;
  handleListPage();
};

// 加载表格数据
const handleListPage = async () => {
  loading.value = true;
  try {
    const res = await listPage(searchParams);
    if (res.code === 1) {
      tableList.value = res.data.records;
      total.value = res.data.total;
    } else {
      koiMsgError(res.msg || '获取数据失败');
    }
  } catch (error) {
    console.error(error);
    koiMsgError('获取数据失败');
  } finally {
    loading.value = false;
  }
};

const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
// 表格选择
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
};

// 分页变化
const handleSizeChange = (size: number) => {
  searchParams.page_size = size;
  handleListPage();
};

const handleCurrentChange = (page: number) => {
  searchParams.page_no = page;
  handleListPage();
};

// 表单相关
const koiDrawerRef = ref();
const title = ref('游戏技能表');
const formRef = ref<FormInstance>();
const form = ref<any>({
  id: '',
  name: '',
  desc: '',
  icon: '',
  max_level: '',
  cost: '',
  cost_type: '',
  cooldown: '',
  cast_range: '',
  cast_time: '',
  skill_type: '',
  effects: '',
  sorted: '',
  status: '1', // 默认启用
  create_time: '',
  update_time: ''
});

// 技能效果数组
const effectsArray = ref<any[]>([]);

// 监听form.effects变化，解析JSON字符串为effectsArray
watch(() => form.value.effects, (newVal) => {
  if (newVal) {
    try {
      const effectsObj = typeof newVal === 'string' ? JSON.parse(newVal) : newVal;
      effectsArray.value = effectsObj.effects || [];
    } catch (error) {
      console.error('解析effects JSON失败:', error);
      effectsArray.value = [];
    }
  } else {
    effectsArray.value = [];
  }
}, { immediate: true });

// 监听effectsArray变化，更新form.effects
watch(effectsArray, (newVal) => {
  form.value.effects = JSON.stringify({ effects: newVal });
}, { deep: true });

// 添加新效果
const addEffect = () => {
  effectsArray.value.push({
    type: 'direct_damage',
    chance: 1.0,
    key: 'direct_damage',
    value: 0,
    level_scale: 0,
    element: 'fire',
    target: 'enemy',
    target_num: 1,
    target_rule: 'lowest_hp',
    aoe_radius: 0,
    aoe_type: 'circle',
    duration: 0,
    max_stacks: 1,
    dispels: true,
    condition: []
  });
};

// 移除效果
const removeEffect = (index: number) => {
  effectsArray.value.splice(index, 1);
};

// 添加条件
const addCondition = (effectIndex: number) => {
  if (!effectsArray.value[effectIndex].condition) {
    effectsArray.value[effectIndex].condition = [];
  }
  effectsArray.value[effectIndex].condition.push({
    attr: '',
    expr: '>=',
    value: 0
  });
};

// 移除条件
const removeCondition = (effectIndex: number, conditionIndex: number) => {
  effectsArray.value[effectIndex].condition.splice(conditionIndex, 1);
};

// 格式化概率显示
const formatProbability = (val: number) => {
  return `${(val * 100).toFixed(0)}%`;
};

// 格式化概率输入框显示
const formatProbabilityInput = (val: number) => {
  return `${(val * 100).toFixed(0)}%`;
};

// 解析概率输入
const parseProbabilityInput = (val: string) => {
  return parseFloat(val.replace('%', '')) / 100;
};

// 表单验证规则
const rules = reactive({
  // 没有必填字段，不进行校验
});

// 确认按钮loading
const confirmLoading = ref(false);

// 新增
const handleAdd = () => {
  // 打开抽屉
  koiDrawerRef.value.koiOpen();
  // 重置表单
  resetForm();
  // 设置标题
  title.value = '添加游戏技能表';
};

// 清空表单数据
const resetForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (formRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      formRef.value.resetFields();
    }
  });
  form.value = {
  id: '',
  name: '',
  desc: '',
  icon: '',
  max_level: 1,
  cost: 0,
  cost_type: 'mp',
  cooldown: 0,
  cast_range: 0,
  cast_time: 0,
  skill_type: 'active',
  effects: '{"effects":[]}',
  sorted: 1,
  status: '1', // 默认启用
  create_time: '',
  update_time: ''
  };
  effectsArray.value = [];
};

// 回显数据
const handleEcho = async (id: any) => {
  if (id == null || id == "") {
    koiMsgWarning("请选择需要修改的数据🌻");
    return;
  }
  try {
    const res = await getById(id);
    if (res.code === 1) {
      form.value = res.data;
    } else {
      koiNoticeError(res.msg || '获取详情失败');
    }
  } catch (error) {
    console.log(error);
    koiNoticeError('获取详情失败，请刷新重试🌻');
  }
};

// 修改
const handleUpdate = async (row?: any) => {
  // 打开抽屉
  koiDrawerRef.value.koiOpen();
  // 重置表单
  resetForm();
  // 设置标题
  title.value = '修改游戏技能表';
  
  const id = row ? row.id : selectedIds.value[0];
  if (id == null || id == "") {
    koiMsgError("请选中需要修改的数据🌻");
    return;
  }
  // 回显数据
  handleEcho(id);
};

// 确认提交
const handleConfirm = () => {
  if (!formRef.value) return;
  confirmLoading.value = true;
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (form.value.id != null && form.value.id != "") {
        try {
          const res = await update(form.value);
          if (res.code === 1) {
            koiMsgSuccess('修改成功🌻');
            confirmLoading.value = false;
            koiDrawerRef.value.koiQuickClose();
            resetForm();
            handleListPage();
          } else {
            koiMsgError(res.msg || '修改失败');
            confirmLoading.value = false;
          }
        } catch (error) {
          console.error(error);
          confirmLoading.value = false;
          koiMsgError('修改失败，请重试');
        }
      } else {
        try {
          const res = await add(form.value);
          if (res.code === 1) {
            koiMsgSuccess('添加成功🌻');
            confirmLoading.value = false;
            koiDrawerRef.value.koiQuickClose();
            resetForm();
            handleListPage();
          } else {
            koiMsgError(res.msg || '添加失败');
            confirmLoading.value = false;
          }
        } catch (error) {
          console.error(error);
          confirmLoading.value = false;
          koiMsgError('添加失败，请重试');
        }
      }
    } else {
      koiMsgError('验证失败，请检查填写内容🌻');
      confirmLoading.value = false;
    }
  });
};

// 取消
const handleCancel = () => {
  koiDrawerRef.value.koiClose();
};

// 删除
const handleDelete = (row: any) => {
  const id = row.id;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认删除该数据么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await deleteById(id);
        handleListPage();
        koiNoticeSuccess("删除成功🌻");
      } catch (error) {
        console.log(error);
        handleListPage();
        koiNoticeError("删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedIds.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认进行批量删除么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await batchDelete(selectedIds.value);
        handleListPage();
        koiNoticeSuccess("批量删除成功🌻");
      } catch (error) {
        console.log(error);
        handleListPage();
        koiNoticeError("批量删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 状态switch
const handleSwitch = (row: any) => {
  let text = row.status === "0" ? "停用" : "启用";
  koiMsgBox("确认要[" + text + "]-[" + row.id + "]吗？")
    .then(async () => {
      if (!row.id || !row.status) {
        row.status = row.status == "0" ? "1" : "0";
        koiMsgWarning("请选择需要修改的数据🌻");
        return;
      }
      try {
        await updateStatus(row.id, row.status);
        koiNoticeSuccess("修改成功🌻");
      } catch (error) {
        handleListPage();
        console.log(error);
        koiNoticeError("修改失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      row.status = row.status == "0" ? "1" : "0";
      koiMsgError("已取消🌻");
    });
};

// 获取最新排序数字
const handleSorted = async () => {
  try {
    const res = await getSorted({});
    if (res.code === 1) {
      form.value.sorted = res.data;
    }
  } catch (error) {
    console.log(error);
    koiMsgError("数据查询失败，请重试🌻");
  }
};

// 初始化
onMounted(() => {
  handleListPage();
});
</script>

<style scoped>
.mb-10px {
  margin-bottom: 10px;
}
.mt-10px {
  margin-top: 10px;
}
.effect-card {
  margin-bottom: 15px;
}
.effect-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.condition-item {
  padding: 10px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>