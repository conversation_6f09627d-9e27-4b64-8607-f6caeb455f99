<template>
  <div>
    <div ref="divRef" style="height: 667px;width: 750px;"></div>
  </div>
  
</template>

<script setup lang="ts">
import { AiEditor } from "aieditor";
import "aieditor/dist/style.css";
import { onMounted, onUnmounted, ref, watch, PropType } from "vue";
import { getToken } from "@/utils/storage.ts";

//v-model传值
const props = defineProps({
  modelValue: {
    type: String as PropType<string>,
    default: ""
  },
  placeholder: {
    type: String as PropType<string>,
    default: ""
  }
});
const emit = defineEmits(["update:modelValue"]);

// aieditor使用
const divRef = ref();
let aiEditor: AiEditor | null = null;
const token = getToken();
onMounted(() => {
  aiEditor = new AiEditor({
    element: divRef.value as Element,
    placeholder: props.placeholder,
    content: props.modelValue,
    onChange: (editor: AiEditor) => {
      let newContent = editor.getHtml();
      // console.log(newContent);
      emit("update:modelValue", newContent);
    },
    image: {
      allowBase64: true,
      // defaultSize: 350,
      // customMenuInvoke: (editor: AiEditor) => {},
      uploadUrl: "https://game.miwudalu.com/dogadmin/sysFile/uploadByAiEditor",
      uploadFormName: "file", //上传时的文件表单名称
      uploadHeaders: {
        Authorization: "Bearer " + token
      },
      // uploader: (file, uploadUrl, headers, formName) => {
      //   //可自定义图片上传逻辑
      // },
      // uploaderEvent: {
      //   onUploadBefore: (file, uploadUrl, headers) => {
      //     //监听图片上传之前，此方法可以不用回任何内容，但若返回 false，则终止上传
      //   },
      //   onSuccess: (file, response) => {
      //     console.log('file',file);
      //     console.log('response',response);
      //     //监听图片上传成功
      //     //注意：
      //     // 1、如果此方法返回 false，则图片不会被插入到编辑器
      //     // 2、可以在这里返回一个新的 json 给编辑器
      //   },
      //   onFailed: (file, response) => {
      //     //监听图片上传失败，或者返回的 json 信息不正确
      //   },
      //   onError: (file, error) => {
      //     //监听图片上传错误，比如网络超时等
      //   }
      // },
      bubbleMenuItems: ["AlignLeft", "AlignCenter", "AlignRight", "delete"]
    }
  });
});

onUnmounted(() => {
  aiEditor && aiEditor.destroy();
});


// 监听 modelValue 的变化，并更新编辑器内容
watch(() => props.modelValue, (newValue) => {
  if (aiEditor && newValue !== aiEditor.getHtml()) {
    // console.log('aiEditor 11',newValue);
    aiEditor.setContent(newValue);
  }
});
</script>
