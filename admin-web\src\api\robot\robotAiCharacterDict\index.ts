import koi from "@/utils/axios.ts";

// AI角色通用字典表 接口
enum API {
  LIST_PAGE = "/robot/robotAiCharacterDict/listPage",
  GET_BY_ID = "/robot/robotAiCharacterDict/getById",
  UPDATE = "/robot/robotAiCharacterDict/update",
  ADD = "/robot/robotAiCharacterDict/add",
  UPDATE_STATUS = "/robot/robotAiCharacterDict/updateStatus",
  DELETE = "/robot/robotAiCharacterDict/deleteById",
  BATCH_DELETE = "/robot/robotAiCharacterDict/batchDelete",
  GET_SORTED = "/robot/robotAiCharacterDict/getSorted"
}

// 多条件分页查询
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "?id=" + id);
};

// 根据ID进行修改
export const update = (data: any) => {
  return koi.post(API.UPDATE, data);
};

// 添加
export const add = (data: any) => {
  return koi.post(API.ADD, data);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE + "?id=" + id);
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids});
};

// 修改状态
export const updateStatus = (id: any, status: any) => {
  return koi.post(API.UPDATE_STATUS, { id: id, status });
};

// 获取排序数据
export const getSorted = (params: any) => {
  return koi.get(API.GET_SORTED, params);
};