<?php

namespace app\robot\controller;

use app\robot\service\RobotAiCharacterService;
use app\dogadmin\common\ApiResponse;
use app\dogadmin\controller\Base;

/**
 * 机器人 控制器
 */
class RobotAiCharacter extends Base
{
    /**
     * @var RobotAiCharacterService
     */
    protected $service;
    
    /**
     * 初始化方法
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = new RobotAiCharacterService();
        $this->params = $this->request->param();
        $this->searchKey = [
            ['id' => 'like'],
            ['name' => 'like'],
        ];
    }
    
    // 所有基础CRUD方法均继承自Base控制器
     // 如需自定义方法，请在此处添加
}