<?php

namespace app\dogadmin\controller;

use app\dogadmin\common\ApiResponse;
use app\dogadmin\service\SysDictTypeService;

class SysDictType extends Base
{
    /**
     * @var SysDictTypeService
     */
    protected $service;
    public function initialize(): void
    {
        $this->service = new SysDictTypeService();
        $this->params = $this->request->param();
        $this->searchKey = [
            ['dict_name'=>'like'],
        ];
    }

    public function listDictType()
    {
        $params = $this->params;
        $res = $this->service->listDictType($params);
        return ApiResponse::success($res, '获取字典类型成功');
    }
}