const base_url = "https://wpd.0598777.cn/api/v1/";
import dayjs from '@/utils/dayjs.js'
// const _ = require('@/utils/lodash.min');
import store from '@/store/index.js';

function isWeiXinBrowser() {
	// #ifdef H5
	// window.navigator.userAgent属性包含了浏览器类型、版本、操作系统类型、浏览器引擎类型等信息，这个属性可以用来判断浏览器类型
	let ua = window.navigator.userAgent.toLowerCase();
	// 通过正则表达式匹配ua中是否含有MicroMessenger字符串
	if (ua.match(/MicroMessenger/i) == 'micromessenger') {
		return true;
	} else {
		return false;
	}
	// #endif
	return false;
}

function formatTime(time) {
	if (('' + time).length === 10) {
		time = parseInt(time) * 1000
	} else {
		time = +time
	}
	const d = new Date(time)
	const now = Date.now()

	const diff = (now - d) / 1000

	if (diff < 30) {
		return '刚刚'
	} else if (diff < 3600) {
		// less 1 hour
		return Math.ceil(diff / 60) + '分钟前'
	} else if (diff < 3600 * 24) {
		return Math.ceil(diff / 3600) + '小时前'
	} else if (diff < 3600 * 24 * 2) {
		return '1天前'
	}
	return dayjs(time).format('YYYY-MM-DD HH:mm')
}

function getQueryVariable(variable) {
	var query = window.location.search.substring(1);
	var vars = query.split("&");
	for (var i = 0; i < vars.length; i++) {
		var pair = vars[i].split("=");
		if (pair[0] == variable) {
			return pair[1];
		}
	}
	return false;
}

function deepClone(obj = {}) {
	if (typeof obj !== 'object' || obj == null) {
		return obj;
	}
	let result;
	if (result instanceof Array) {
		result = [];
	} else {
		result = {};
	}
	for (let key in obj) {
		if (Object.hasOwnProperty.call(obj, key)) {
			result[key] = deepClone(obj[key]);
		}
	}
	return result;
}

function checkPhone(value) {
	return /^1[123456789]\d{9}$/.test(value)
}

function convertObjectToArray(obj) {
	return Object.entries(obj).map(([value, text]) => ({ text, value: value }));
}

function extractTextArray(objArray) {
	return objArray.map(item => item.text);
}

function getType(value) {
	const type = typeof value;

	if (type !== 'object') {
		// 基本数据类型（排除null，因为typeof null也返回'object'）
		return type;
	}

	if (value === null) {
		return 'null';
	}

	// Object.prototype.toString.call()方法判断对象类型
	const objectType = Object.prototype.toString.call(value);

	// 从格式[object Xxxx]中提取类型名
	return objectType.slice(8, -1).toLowerCase();
}

function removeHtml(content) {
	let dd = content.replace(/<[^>]+>/g, ""); //截取html标签
	let dds = dd.replace(/&nbsp;/ig, ""); //截取空格等特殊标签
	return dds.substring(0, 100) + "...";
}

function getCurrentPagePath() {
	// 获取当前页面的路径部分，包括参数
	var path = window.location.pathname + window.location.search;

	// 要排除的路径
	var excludedPath = "/pages/login/ok";

	// 如果当前路径是要排除的路径，返回一个空字符串，否则返回当前路径
	var basePath = '/pages/index/index';

	if (path.includes(excludedPath)) {

	} else {
		uni.setStorageSync('beforePageFullPath', path);
	}

	return path;
}

function splitArray(array, chunkSize) {
	const result = [];
	for (let i = 0; i < array.length; i += chunkSize) {
		result.push(array.slice(i, i + chunkSize));
	}
	return result;
}

function getAppChannel() {
	// #ifdef APP-PLUS
	return 'app'
	// #endif
	// #ifdef MP-WEIXIN
	return 'weapp'
	// #endif
	// #ifdef H5
	return isWeixinBrowser() ? 'wechat' : 'h5'
	// #endif
}

function isTokenExpired() {
	// 获取本地存储的token到期时间戳
	const tokenExpiration = uni.getStorageSync('tokenExpiration');
	// 获取当前时间戳
	const now = parseInt(new Date().getTime() / 1000);
	// 比较当前时间戳是否大于token的到期时间戳
	return now > tokenExpiration;
}

function isToken() {
	// 获取本地存储的token到期时间戳
	let token = uni.getStorageSync('token');
	console.log('token',token);
	if (token) {
		return true;
	} else {
		return false;
	}
}


/**
 * 将给定的日期字符串转换为相对时间描述或格式化日期字符串。
 * 
 * @param {string} dateString - 输入的日期字符串，格式如 "2025-02-19 20:54:50"
 * @returns {string} - 转换后的相对时间或格式化日期字符串
 */
function formatRelativeTime(dateString) {
	// const inputDate = new Date(dateString);
	// const now = new Date();
	// const diffInSeconds = Math.floor((now - inputDate) / 1000);
	const inputDate = dayjs(dateString).unix();
	const now = dayjs().unix();
	const diffInSeconds = now - inputDate;
	const minute = 60;
	const hour = 60 * minute;
	const day = 24 * hour;
	const thirtyDays = 30 * day;

	if (diffInSeconds < 0) {
		// 如果输入日期在未来，返回原日期字符串
		return formatDate(dateString);
	} else if (diffInSeconds < minute) {
		return '刚刚';
	} else if (diffInSeconds < hour) {
		const minutesAgo = Math.floor(diffInSeconds / minute);
		return `${minutesAgo}分钟前`;
	} else if (diffInSeconds < day) {
		const hoursAgo = Math.floor(diffInSeconds / hour);
		return `${hoursAgo}小时前`;
	} else if (diffInSeconds < thirtyDays) {
		const daysAgo = Math.floor(diffInSeconds / day);
		return `${daysAgo}天前`;
	} else {
		return formatDate(dateString);
	}
}

/**
 * 格式化日期为 "dd-MM-yy HH:mm" 格式。
 * 
 * @param {Date} date - 要格式化的日期对象
 * @returns {string} - 格式化后的日期字符串
 */
function formatDate(date) {
	return dayjs(date).format('YY-MM-DD HH:mm');
	// const pad = (num) => num.toString().padStart(2, '0');
	// const d = date.getDate();
	// const m = pad(date.getMonth() + 1); // 月份从0开始
	// const y = date.getFullYear().toString().slice(-2);
	// const H = pad(date.getHours());
	// const M = pad(date.getMinutes());
	// return `${y}-${m}-${d} ${H}:${M}`;
}

function setTabbarNum(index,num){
	if(num > 0){
		uni.setTabBarBadge({
		  index: index,
		  text: num+''
		})
	}else{
		uni.removeTabBarBadge({
			index: index
		})
	}
	
}

export {
	base_url,
	isWeiXinBrowser,
	formatTime,
	getQueryVariable,
	deepClone,
	checkPhone,
	convertObjectToArray,
	extractTextArray,
	getType,
	removeHtml,
	getCurrentPagePath,
	splitArray,
	getAppChannel,
	isTokenExpired,
	formatRelativeTime,
	isToken,
	setTabbarNum
};