<template>
	<view class="container">
		<view class="content">
			<!-- <view class="">ID: {{userInfo.user_id}}</view> -->
			<view class="avatar-container">
				<image class="avatar" :src="userInfo.avatar_url" @click="changeAvatar" />
				<text class="change-avatar">点击修改头像</text>
			</view>
			<view class="nickname-container">
				<tui-list-cell :hover="false" :padding="listCellPadding" unlined>
					<tui-input v-model="userInfo.user_id" padding="20rpx 30rpx" border-color="#979BB5" label="ID" radius="30" inputBorder disabled></tui-input>
				</tui-list-cell>
				<tui-list-cell :hover="false" :padding="listCellPadding" unlined>
					<tui-input v-model="userInfo.nickname" padding="20rpx 30rpx" border-color="#979BB5" label="昵称" radius="30" inputBorder
						placeholder="请输入昵称"></tui-input>
				</tui-list-cell>
				<tui-list-cell :hover="false" :padding="listCellPadding" unlined>
					<tui-textarea textareaBorder radius="20" label="简介" border-color="#979BB5" placeholder="请输入简介" flexStart isCounter></tui-textarea>
				</tui-list-cell>
				<!-- <tui-list-cell :hover="false" :padding="listCellPadding" unlined>
					<tui-input padding="20rpx 30rpx" border-color="#979BB5" label="游戏ID" radius="30" inputBorder
						placeholder="游戏ID"></tui-input>
				</tui-list-cell>
				<tui-list-cell :hover="false" :padding="listCellPadding" unlined>
					<tui-input padding="20rpx 30rpx" border-color="#979BB5" label="服务器" radius="30" inputBorder
						placeholder="请输入姓名"></tui-input>
				</tui-list-cell> -->
			</view>
			<tui-button>保存</tui-button>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex';
	export default {
		data() {
			return {
				listCellPadding: "20rpx 30rpx" 
			}
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		methods: {
			changeAvatar() {
				// 处理修改头像的逻辑
			}
		}
	}
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		// padding: 20px;
		background-color: #f5f5f5;

		.content {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;
			background-color: #fff;
			padding: 40rpx;

			.avatar-container {
				display: flex;
				flex-direction: column;
				align-items: center;
				// margin-bottom: 20px;

				.avatar {
					width: 100px;
					height: 100px;
					border-radius: 50%;
				}

				.change-avatar {
					margin-top: 10px;
					font-size: 16px;
					color: #000;
				}
			}

			.nickname-container {
				display: flex;
				align-items: center;
				flex-direction: column;
				width: 100%;
				margin-bottom: 20px;

				.nickname-input {
					flex: 1;
					padding: 10px;
					font-size: 16px;
					border: 1px solid #ccc;
					border-radius: 5px;
				}

				.char-count {
					margin-left: 10px;
					font-size: 16px;
					color: #999;
				}
			}

			.save-button {
				width: 100%;
				padding: 10px;
				font-size: 16px;
				color: #fff;
				background-color: #007bff;
				border: none;
				border-radius: 5px;
			}
		}
	}
</style>