<?php

namespace app\{{module_name}}\service;

use app\{{module_name}}\model\{{controller_name}}Model;
use app\dogadmin\service\BaseService;
/**
 * {{controller_name}} 服务类
 */
class {{controller_name}}Service extends BaseService
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new {{controller_name}}Model();
    }
    
    // 所有基础CRUD方法均继承自BaseService
    // 如需自定义方法，请在此处添加
}