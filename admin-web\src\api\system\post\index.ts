// 导入二次封装axios
import koi from "@/utils/axios.ts";

// 统一管理接口
enum API {
  LIST_PAGE = "/koi/sysPost/listPage",
  GET_BY_ID = "/koi/sysPost/getById",
  UPDATE = "/koi/sysPost/update",
  ADD = "/koi/sysPost/add",
  DELETE = "/koi/sysPost/deleteById",
  BATCH_DELETE = "/koi/sysPost/batchDelete",
  UPDATE_STATUE = "/koi/sysPost/updateStatus",
  LIST_NORMAL_POST = "/koi/sysPost/listNormalPost",
  ASSIGN_USER_POST = "/koi/sysPost/assignUserPost",
  LIST_POST_ELSELECT = "/koi/sysPost/listPostElSelect",
  GET_SORTED = "/koi/sysPost/getSorted"
}

// 多条件分页查询数据
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "/" + id);
};

// 根据ID进行修改
export const update = (data: any) => {
  return koi.post(API.UPDATE, data);
};

// 添加
export const add = (data: any) => {
  return koi.post(API.ADD, data);
};

// 删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE + "/" + id);
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, {ids});
};

// 修改状态
export const updateStatus = (id: any, status: any) => {
  return koi.post(API.UPDATE_STATUE + "/" + id + "/" + status);
};

// 查询所有正常岗位[穿梭框]
export const listNormalPost = (user_id: any) => {
  return koi.get(API.LIST_NORMAL_POST + "/" + user_id);
};

// 根据当前用户ID分配岗位
export const assignUserPost = (user_id: any, postIds: any) => {
  if(!postIds || postIds.length === 0){
    postIds = [-1];
  }
  return koi.get(API.ASSIGN_USER_POST + "/" + user_id + "/" + postIds);
};

// 查询岗位下拉框
export const listPostElSelect = () => {
  return koi.get(API.LIST_POST_ELSELECT);
};

// 获取最新排序
export const getSorted = () => {
  return koi.get(API.GET_SORTED);
};


