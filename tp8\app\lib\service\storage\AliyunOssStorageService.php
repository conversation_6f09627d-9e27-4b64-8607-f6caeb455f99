<?php
declare(strict_types=1);

namespace app\lib\service\storage;

use app\lib\exception\dogadmin\FileException;
use app\lib\service\storage\CloudStorageService;
use think\File;

/**
 * 阿里云OSS存储服务
 */
class AliyunOssStorageService extends CloudStorageService
{
    /**
     * OSS客户端
     * @var mixed
     */
    private $client;
    
    /**
     * 存储桶名
     * @var string
     */
    private string $bucket = '';
    
    /**
     * 地域节点
     * @var string
     */
    private string $endpoint = '';
    
    /**
     * 域名
     * @var string
     */
    private string $domain = '';
    
    /**
     * 初始化阿里云OSS配置
     */
    protected function init(): void
    {
        $this->storageType = 'aliyun_oss';
        
        if (empty($this->config['accessKeyId']) || empty($this->config['accessKeySecret']) || 
            empty($this->config['bucket']) || empty($this->config['endpoint'])) {
            throw new FileException(['message' => '阿里云OSS配置信息不完整'], 4); // 4 = 配置错误
        }
        
        $this->bucket = $this->config['bucket'];
        $this->endpoint = $this->config['endpoint'];
        $this->domain = $this->config['domain'] ?? '';
        
        // 检查是否安装了阿里云OSS SDK
        if (!class_exists('\\OSS\\OssClient')) {
            throw new FileException(['message' => '请先安装阿里云OSS SDK: composer require aliyuncs/oss-sdk-php'], 4); // 4 = 配置错误
        }
        
        try {
            $this->client = new \OSS\OssClient(
                $this->config['accessKeyId'],
                $this->config['accessKeySecret'],
                $this->endpoint
            );
            
        } catch (\Exception $e) {
            throw new FileException(['message' => '阿里云OSS初始化失败: ' . $e->getMessage()], 4); // 4 = 配置错误
        }
    }
    
    /**
     * 上传文件到阿里云OSS
     * 
     * @param File|string $file 文件对象或本地路径
     * @param string $path 上传路径
     * @param string $fileName 文件名
     * @param string|bool $nameMode 文件名模式：
     *                             - 字符串: 使用自定义文件名（保留扩展名）
     *                             - true: 使用原始文件名
     *                             - false: 生成唯一文件名（默认）
     * @return array
     */
    public function upload($file, string $path = '', string $fileName = '', $nameMode = false): array
    {
        try {
            $info = $this->parseFileInfo($file);
            $this->validateFile($file, $this->config['rules'] ?? []);
            if (empty($fileName)) {
                $fileName = $this->generateFileName($info['originalName'], $path, $nameMode);
            } else {
                $fileName = $path . $fileName;
            }
            $result = $this->client->uploadFile(
                $this->bucket,
                $fileName,
                $info['realPath'],
                [
                    \OSS\OssClient::OSS_HEADERS => [
                        'Content-Type' => $info['mime']
                    ]
                ]
            );
            return [
                'success' => true,
                'key' => $fileName,
                'etag' => trim($result['info']['etag'], '"'),
                'url' => $this->getUrl($fileName),
                'size' => $info['size'],
                'originalName' => $info['originalName'],
                'extension' => $info['extension'],
                'mimeType' => $info['mime'],
                'storageType' => $this->storageType
            ];
        } catch (FileException $e) {
            throw $e;
        } catch (\OSS\Core\OssException $e) {
            throw new FileException([
                'message' => '阿里云OSS上传失败: ' . $e->getMessage(),
                'file' => $info['originalName'] ?? '',
                'errorCode' => $e->getErrorCode()
            ], 5);
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '阿里云OSS上传异常: ' . $e->getMessage(),
                'file' => $info['originalName'] ?? ''
            ], 5);
        }
    }
    
    /**
     * 删除文件
     * 
     * @param string $key 文件key
     * @return bool
     */
    public function delete(string $key): bool
    {
        try {
            $this->client->deleteObject($this->bucket, $key);
            return true;
            
        } catch (\OSS\Core\OssException $e) {
            throw new FileException([
                'message' => '阿里云OSS删除失败: ' . $e->getMessage(),
                'key' => $key,
                'errorCode' => $e->getErrorCode()
            ], 8); // 8 = 文件删除失败
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '阿里云OSS删除异常: ' . $e->getMessage(),
                'key' => $key
            ], 8); // 8 = 文件删除失败
        }
    }
    
    /**
     * 获取文件URL
     * 
     * @param string $key 文件key
     * @return string
     */
    public function getUrl(string $key): string
    {
        if (!empty($this->domain)) {
            return rtrim($this->domain, '/') . '/' . ltrim($key, '/');
        }
        
        // 使用默认域名
        return sprintf('https://%s.%s/%s', 
            $this->bucket, 
            $this->endpoint, 
            ltrim($key, '/')
        );
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param string $key 文件key
     * @return bool
     */
    public function exists(string $key): bool
    {
        try {
            return $this->client->doesObjectExist($this->bucket, $key);
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取预签名URL
     * 
     * @param string $key 文件key
     * @param int $expires 过期时间（秒）
     * @return string
     */
    public function getPresignedUrl(string $key, int $expires = 3600): string
    {
        try {
            $signedUrl = $this->client->signUrl(
                $this->bucket,
                $key,
                $expires
            );
            
            return $signedUrl;
            
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '获取预签名URL失败: ' . $e->getMessage(),
                'key' => $key
            ], 4); // 4 = 配置错误
        }
    }
    
    /**
     * 批量删除文件
     * 
     * @param array $keys 文件key数组
     * @return array
     */
    public function deleteMultiple(array $keys): array
    {
        try {
            $result = $this->client->deleteObjects($this->bucket, $keys);
            
            return [
                'success' => true,
                'deleted' => $result['info']['deleted'] ?? [],
                'errors' => $result['info']['delete_errors'] ?? []
            ];
            
        } catch (\Exception $e) {
            throw new FileException([
                'message' => '阿里云OSS批量删除失败: ' . $e->getMessage(),
                'keys' => $keys
            ], 8); // 8 = 文件删除失败
        }
    }
    
    // validateFile方法兼容本地路径
    protected function validateFile($file, array $rules = []): void
    {
        $info = $this->parseFileInfo($file);
        if (isset($rules['maxSize']) && $info['size'] > $rules['maxSize']) {
            throw new FileException([
                'file' => $info['originalName'],
                'size' => $info['size'],
                'maxSize' => $rules['maxSize']
            ], 1);
        }
        if (isset($rules['allowedTypes']) && !in_array($info['extension'], $rules['allowedTypes'])) {
            throw new FileException([
                'file' => $info['originalName'],
                'type' => $info['extension'],
                'allowedTypes' => $rules['allowedTypes']
            ], 3);
        }
    }
}