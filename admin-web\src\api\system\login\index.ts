import koi from "@/utils/axios.ts";

// 统一管理接口
enum API {
  KOI_LOGIN = "/dogadmin/sysUser/login",
  GET_CAPTCHA = "/dogadmin/sysUser/captcha",
  KOI_LAYOUT = "/dogadmin/auth/logout",
  KOI_REGISTER = "/dogadmin/auth/register"
}

// 登录
export const koiLogin = (params: any) => {
  return koi.post(API.KOI_LOGIN, params);
};

// 获取验证码
export const getCaptcha = () => {
  return koi.get(API.GET_CAPTCHA);
};

// 退出登录
export const koiLogout = () => {
  return koi.get(API.KOI_LAYOUT);
};

// 注册
export const koiRegister = (params: any) => {
  return koi.post(API.KOI_REGISTER, params);
};


