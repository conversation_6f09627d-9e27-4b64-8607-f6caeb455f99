<template>
	<view>
		<view class="card-list-wrap">
			<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback">
				<view class="card-item" v-for="(item,index) in dataList" :key="index">
					<card :info="item"></card>
					<view class="set-btn-wrap">
						<text class="del" @click="showModalDel(item)">删除</text>
					</view>
				</view>
			</mescroll-body>
		</view>
	</view>
</template>

<script>
	import { getExchangeListByUserId, delExchangeByUserId } from '@/api/exchange.js';
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	import card from '@/pages/exchange/components/card.vue';
	export default {
		mixins: [MescrollMixin],
		components: {
			card
		},
		data() {
			return {
				dataList: []
			}
		},
		methods: {
			upCallback(page) {
				this._getData(page);
			},
			_getData(page) {
				let data = {
					pageNo: page.num,
					pageSize: page.size,
				}

				getExchangeListByUserId(data).then(res => {
					let curPageData = res.data;
					let curPageLen = curPageData.length;
					if (page.num == 1) this.dataList = [];
					this.dataList = this.dataList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen);
				}).catch(err => {
					this.mescroll.endErr()
				})
			},
			showModalDel(info){
				let that = this;
				uni.showModal({
					title: '提示',
					content: '【删除后无法恢复】是否删除该交流信息？',
					success: function (res) {
						if (res.confirm) {
							console.log('用户点击确定');
							that._delInfo(info);
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			_delInfo(info){
				let data = {
					exchangeId: info.exchangeId
				}
				delExchangeByUserId(data).then(res=>{
					uni.showToast({
						title: '操作成功',
						icon:'none'
					})
					this.mescroll.resetUpScroll();
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.card-list-wrap {
		padding: 26rpx;

		.card-item {
			margin-bottom: 20rpx;
		}
	}
	.set-btn-wrap{
		font-size: 28rpx;
		background-color: #fff;
		padding: 20rpx;
		display: flex;
		justify-content: flex-end;
		.del{
			padding: 10rpx 20rpx;
			color: #fff;
			background-color: #EB0909;
			border-radius: 12rpx;
		}
	}
</style>