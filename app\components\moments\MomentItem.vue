<template>
  <view class="moment-item">
    <!-- 用户信息 -->
    <view class="moment-header">
      <image class="user-avatar" :src="moment.user.avatar" mode="aspectFill" @tap="viewProfile(moment.user)" />
      <view class="user-info">
        <text class="user-name">{{ moment.user.name }}</text>
        <text class="moment-time">{{ formatTime(moment.createTime) }}</text>
      </view>
      <view class="more-btn" @tap="showMoreOptions">
        <text class="more-icon">⋯</text>
      </view>
    </view>
    
    <!-- 动态内容 -->
    <view class="moment-content">
      <!-- 文字内容 -->
      <view v-if="moment.content" class="text-content">
        <text class="content-text" :class="{ 'text-expanded': isTextExpanded }">{{ moment.content }}</text>
        <text v-if="moment.content.length > 120 && !isTextExpanded" class="expand-btn" @tap="expandText">全文</text>
      </view>
      
      <!-- 图片内容 -->
      <view v-if="moment.images && moment.images.length > 0" class="images-container">
        <view class="images-grid" :class="getImageGridClass(moment.images.length)">
          <image 
            v-for="(image, index) in moment.images" 
            :key="index"
            class="moment-image"
            :src="image"
            mode="aspectFill"
            @tap="previewImage(image, moment.images)"
          />
        </view>
      </view>
      
      <!-- 位置信息 -->
      <view v-if="moment.location" class="location-info" @tap="viewLocation">
        <text class="location-icon">📍</text>
        <text class="location-text">{{ moment.location }}</text>
      </view>
    </view>
    
    <!-- 互动区域 -->
    <view class="moment-actions">
      <view class="action-btn" @tap="toggleLike">
        <text class="action-icon" :class="{ 'liked': moment.isLiked }">{{ moment.isLiked ? '❤️' : '🤍' }}</text>
      </view>
      <view class="action-btn" @tap="showCommentInput">
        <text class="action-icon">💬</text>
      </view>
    </view>
    
    <!-- 点赞和评论 -->
    <view v-if="hasInteractions" class="interactions">
      <!-- 点赞列表 -->
      <view v-if="moment.likes && moment.likes.length > 0" class="likes-section">
        <text class="like-icon">❤️</text>
        <text class="likes-text">
          <text 
            v-for="(like, index) in moment.likes" 
            :key="like.id"
            class="like-user"
            @tap="viewProfile(like.user)"
          >
            {{ like.user.name }}{{ index < moment.likes.length - 1 ? '，' : '' }}
          </text>
        </text>
      </view>
      
      <!-- 评论列表 -->
      <view v-if="moment.comments && moment.comments.length > 0" class="comments-section">
        <view 
          v-for="comment in moment.comments" 
          :key="comment.id"
          class="comment-item"
        >
          <text class="comment-user" @tap="viewProfile(comment.user)">{{ comment.user.name }}</text>
          <text v-if="comment.replyTo" class="reply-to">回复</text>
          <text v-if="comment.replyTo" class="reply-user" @tap="viewProfile(comment.replyTo)">{{ comment.replyTo.name }}</text>
          <text class="comment-content">：{{ comment.content }}</text>
        </view>
      </view>
    </view>
    
    <!-- 评论输入框 -->
    <view v-if="showCommentBox" class="comment-input-box">
      <input 
        class="comment-input" 
        type="text" 
        v-model="commentText"
        placeholder="写评论..."
        placeholder-class="comment-placeholder"
        @confirm="submitComment"
        :focus="commentInputFocus"
      />
      <button class="comment-submit" @tap="submitComment" :disabled="!commentText.trim()">发送</button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MomentItem',
  props: {
    moment: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isTextExpanded: false,
      showCommentBox: false,
      commentInputFocus: false,
      commentText: ''
    }
  },
  computed: {
    hasInteractions() {
      return (this.moment.likes && this.moment.likes.length > 0) || 
             (this.moment.comments && this.moment.comments.length > 0)
    }
  },
  methods: {
    formatTime(timestamp) {
      const now = new Date()
      const time = new Date(timestamp)
      const diff = now.getTime() - time.getTime()
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`
      
      return time.toLocaleDateString('zh-CN')
    },
    
    getImageGridClass(count) {
      if (count === 1) return 'grid-single'
      if (count <= 4) return 'grid-small'
      return 'grid-normal'
    },
    
    expandText() {
      this.isTextExpanded = true
    },
    
    toggleLike() {
      this.$emit('like', this.moment.id)
    },
    
    showCommentInput() {
      this.showCommentBox = !this.showCommentBox
      if (this.showCommentBox) {
        this.$nextTick(() => {
          this.commentInputFocus = true
        })
      }
    },
    
    submitComment() {
      if (!this.commentText.trim()) return
      
      this.$emit('comment', {
        momentId: this.moment.id,
        content: this.commentText.trim()
      })
      
      this.commentText = ''
      this.showCommentBox = false
      this.commentInputFocus = false
    },
    
    previewImage(current, images) {
      uni.previewImage({
        current,
        urls: images
      })
    },
    
    viewProfile(user) {
      this.$emit('viewProfile', user)
    },
    
    viewLocation() {
      this.$emit('viewLocation', this.moment.location)
    },
    
    showMoreOptions() {
      this.$emit('showMore', this.moment)
    }
  }
}
</script>

<style scoped>
.moment-item {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 32rpx;
}

.moment-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
  background-color: #f0f0f0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.moment-time {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
}

.more-btn {
  padding: 8rpx;
}

.more-icon {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.5);
}

.moment-content {
  margin-bottom: 24rpx;
}

.text-content {
  margin-bottom: 16rpx;
}

.content-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.8);
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-expanded {
  display: block;
  -webkit-line-clamp: unset;
}

.expand-btn {
  color: #00C78B;
  font-size: 28rpx;
  margin-left: 8rpx;
}

.images-container {
  margin-bottom: 16rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.grid-single .moment-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 8rpx;
}

.grid-small .moment-image {
  width: calc((100% - 8rpx) / 2);
  height: 200rpx;
  border-radius: 8rpx;
}

.grid-normal .moment-image {
  width: calc((100% - 16rpx) / 3);
  height: 200rpx;
  border-radius: 8rpx;
}

.location-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.location-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 26rpx;
  color: #00C78B;
}

.moment-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16rpx;
}

.action-btn {
  margin-left: 32rpx;
  padding: 8rpx;
}

.action-icon {
  font-size: 32rpx;
}

.liked {
  color: #FF4249;
}

.interactions {
  background-color: #F7F7F9;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.likes-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.like-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  margin-top: 4rpx;
}

.likes-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.4;
}

.like-user {
  color: #00C78B;
}

.comments-section {
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  padding-top: 12rpx;
}

.comment-item {
  margin-bottom: 8rpx;
  font-size: 28rpx;
  line-height: 1.4;
}

.comment-user, .reply-user {
  color: #00C78B;
}

.reply-to {
  color: rgba(0, 0, 0, 0.5);
  margin: 0 8rpx;
}

.comment-content {
  color: rgba(0, 0, 0, 0.8);
}

.comment-input-box {
  display: flex;
  align-items: center;
  background-color: #F7F7F9;
  border-radius: 24rpx;
  padding: 8rpx 16rpx;
}

.comment-input {
  flex: 1;
  font-size: 28rpx;
  padding: 12rpx 16rpx;
  background-color: transparent;
}

.comment-placeholder {
  color: rgba(0, 0, 0, 0.5);
}

.comment-submit {
  background-color: #00C78B;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  margin-left: 16rpx;
}

.comment-submit[disabled] {
  background-color: #B9B9B9;
  color: rgba(255, 255, 255, 0.8);
}
</style>
