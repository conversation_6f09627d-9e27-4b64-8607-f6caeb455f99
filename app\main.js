import App from './App'
import tui from './common/httpRequest'
// #ifndef VUE3
import store from './store'
import Vue from 'vue'
Vue.config.productionTip = false
App.mpType = 'app'
import mixins from '@/mixins/common.js'
Vue.mixin(mixins);
// Vue.config.ignoredElements = ['wx-open-launch-weapp'];

Vue.prototype.tui = tui
const app = new Vue({
	store,
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif
