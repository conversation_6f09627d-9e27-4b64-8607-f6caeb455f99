<?php

/**
 * 代码生成工具
 * 用法：php make.php <类型> <模块名/类名>
 * 例如：
 *   php make.php model demo/User     # 创建模型
 *   php make.php controller demo/User # 创建控制器
 *   php make.php service demo/User    # 创建服务
 */

// 定义基础目录
$baseDir = dirname(__DIR__) . '/app/';

// 获取命令行参数
$type = $argv[1] ?? '';
$path = $argv[2] ?? '';

if (empty($type) || empty($path)) {
    die("用法：php make.php <类型> <模块名/类名>\n例如：\n  php make.php model demo/User     # 创建模型\n  php make.php controller demo/User # 创建控制器\n  php make.php service demo/User    # 创建服务\n");
}

// 解析模块名和类名
$parts = explode('/', $path);
if (count($parts) != 2) {
    die("参数格式错误，正确格式为：<模块名>/<类名>\n");
}

$moduleName = $parts[0];
$className = $parts[1];

// 检查模块是否存在
$moduleDir = $baseDir . $moduleName;
if (!is_dir($moduleDir)) {
    die("模块 {$moduleName} 不存在，请先创建模块\n");
}

// 根据类型创建不同的文件
switch (strtolower($type)) {
    case 'model':
        createModel($moduleDir, $moduleName, $className);
        break;
    case 'controller':
        createController($moduleDir, $moduleName, $className);
        break;
    case 'service':
        createService($moduleDir, $moduleName, $className);
        break;
    default:
        die("不支持的类型：{$type}，支持的类型有：model, controller, service\n");
}

/**
 * 创建模型
 * @param string $moduleDir 模块目录
 * @param string $moduleName 模块名
 * @param string $className 类名
 */
function createModel($moduleDir, $moduleName, $className)
{
    $modelContent = "<?php\n\nnamespace app\\{$moduleName}\\model;\n\n/**\n * {$className} 模型\n */\nclass {$className} extends BaseModel\n{\n    // 设置表名\n    protected \$name = ''; // 请设置表名\n\n    // 设置字段信息\n    protected \$schema = [\n        'id'          => 'int',\n        'create_time' => 'datetime',\n        'update_time' => 'datetime',\n        'delete_time' => 'datetime',\n        // 在这里添加更多字段\n    ];\n\n    // 设置字段自动完成\n    protected \$autoWriteTimestamp = true;\n\n    // 追加属性\n    protected \$append = [];\n\n    /**\n     * 关联定义示例\n     * @return \\think\\model\\relation\\HasOne\n     */\n    /*\n    public function relation()\n    {\n        return \$this->hasOne(RelatedModel::class, 'foreign_key', 'primary_key');\n    }\n    */\n}\n";

    $modelPath = $moduleDir . '/model/' . $className . '.php';
    if (!file_exists($modelPath)) {
        file_put_contents($modelPath, $modelContent);
        echo "创建模型: {$modelPath}\n";
    } else {
        echo "模型已存在: {$modelPath}\n";
    }
}

/**
 * 创建控制器
 * @param string $moduleDir 模块目录
 * @param string $moduleName 模块名
 * @param string $className 类名
 */
function createController($moduleDir, $moduleName, $className)
{
    $controllerContent = "<?php\n\nnamespace app\\{$moduleName}\\controller;\n\nuse app\\{$moduleName}\\service\\{$className}Service;\n\n/**\n * {$className} 控制器\n */\nclass {$className}Controller extends Base\n{\n    /**\n     * 初始化方法\n     * @return void\n     */\n    protected function initialize(): void\n    {\n        parent::initialize();\n        \$this->service = new {$className}Service();\n        \$this->searchKey = [\n            // 在这里定义可搜索字段\n            // 例如：'name' => 'like',\n            // 'status' => 'eq',\n        ];\n    }\n\n    /**\n     * 自定义方法示例\n     * @return \\think\\Response\n     */\n    public function customAction()\n    {\n        \$params = \$this->params;\n        // 实现自定义逻辑\n        return json(['code' => 200, 'msg' => '操作成功', 'data' => []]);\n    }\n}\n";

    $controllerPath = $moduleDir . '/controller/' . $className . 'Controller.php';
    if (!file_exists($controllerPath)) {
        file_put_contents($controllerPath, $controllerContent);
        echo "创建控制器: {$controllerPath}\n";
    } else {
        echo "控制器已存在: {$controllerPath}\n";
    }
}

/**
 * 创建服务
 * @param string $moduleDir 模块目录
 * @param string $moduleName 模块名
 * @param string $className 类名
 */
function createService($moduleDir, $moduleName, $className)
{
    $serviceContent = "<?php\n\nnamespace app\\{$moduleName}\\service;\n\nuse app\\{$moduleName}\\model\\{$className};\n\n/**\n * {$className} 服务类\n */\nclass {$className}Service extends BaseService\n{\n    /**\n     * 构造函数\n     */\n    public function __construct()\n    {\n        \$this->model = new {$className}();\n    }\n\n    /**\n     * 获取分页列表数据\n     * @param array \$params 请求参数\n     * @param array \$searchKey 搜索字段配置\n     * @return array 分页数据\n     */\n    public function listPage(array \$params, array \$searchKey = []): array\n    {\n        // 构建查询条件\n        \$where = [];\n        \n        // 处理搜索条件\n        foreach (\$searchKey as \$key => \$type) {\n            if (isset(\$params[\$key]) && \$params[\$key] !== '') {\n                if (\$type === 'like') {\n                    \$where[] = [\$key, 'like', '%' . \$params[\$key] . '%'];\n                } else {\n                    \$where[] = [\$key, \$type, \$params[\$key]];\n                }\n            }\n        }\n        \n        // 分页参数\n        \$page = \$params['page'] ?? 1;\n        \$limit = \$params['limit'] ?? 15;\n        \n        // 排序\n        \$order = \$params['order'] ?? 'id desc';\n        \n        // 查询数据\n        \$list = \$this->model\n            ->where(\$where)\n            ->order(\$order)\n            ->paginate([\n                'list_rows' => \$limit,\n                'page' => \$page,\n            ]);\n        \n        return [\n            'code' => 0,\n            'msg' => '获取成功',\n            'count' => \$list->total(),\n            'data' => \$list->items(),\n        ];\n    }\n\n    /**\n     * 获取单条数据\n     * @param array \$params 请求参数\n     * @return array 单条数据\n     */\n    public function getById(array \$params): array\n    {\n        if (empty(\$params['id'])) {\n            return ['code' => 400, 'msg' => 'ID不能为空', 'data' => null];\n        }\n        \n        \$data = \$this->model->find(\$params['id']);\n        if (!\$data) {\n            return ['code' => 404, 'msg' => '数据不存在', 'data' => null];\n        }\n        \n        return ['code' => 0, 'msg' => '获取成功', 'data' => \$data];\n    }\n\n    /**\n     * 新增数据\n     * @param array \$params 请求参数\n     * @return array 新增结果\n     */\n    public function add(array \$params): array\n    {\n        // 数据验证\n        // 可以在这里添加数据验证逻辑\n        \n        try {\n            \$id = \$this->model->insertGetId(\$params);\n            return ['code' => 0, 'msg' => '添加成功', 'data' => ['id' => \$id]];\n        } catch (\Exception \$e) {\n            return ['code' => 500, 'msg' => '添加失败：' . \$e->getMessage(), 'data' => null];\n        }\n    }\n\n    /**\n     * 更新数据\n     * @param array \$params 请求参数\n     * @return array 更新结果\n     */\n    public function update(array \$params): array\n    {\n        if (empty(\$params['id'])) {\n            return ['code' => 400, 'msg' => 'ID不能为空', 'data' => null];\n        }\n        \n        // 数据验证\n        // 可以在这里添加数据验证逻辑\n        \n        try {\n            \$this->model->update(\$params);\n            return ['code' => 0, 'msg' => '更新成功', 'data' => null];\n        } catch (\Exception \$e) {\n            return ['code' => 500, 'msg' => '更新失败：' . \$e->getMessage(), 'data' => null];\n        }\n    }\n\n    /**\n     * 删除数据\n     * @param array \$params 请求参数\n     * @return array 删除结果\n     */\n    public function deleteById(array \$params): array\n    {\n        if (empty(\$params['id'])) {\n            return ['code' => 400, 'msg' => 'ID不能为空', 'data' => null];\n        }\n        \n        try {\n            \$this->model->destroy(\$params['id']);\n            return ['code' => 0, 'msg' => '删除成功', 'data' => null];\n        } catch (\Exception \$e) {\n            return ['code' => 500, 'msg' => '删除失败：' . \$e->getMessage(), 'data' => null];\n        }\n    }\n\n    /**\n     * 自定义方法示例\n     * @param array \$params 请求参数\n     * @return array 处理结果\n     */\n    public function customMethod(array \$params): array\n    {\n        // 实现自定义业务逻辑\n        return ['code' => 0, 'msg' => '操作成功', 'data' => []];\n    }\n}\n";

    $servicePath = $moduleDir . '/service/' . $className . 'Service.php';
    if (!file_exists($servicePath)) {
        file_put_contents($servicePath, $serviceContent);
        echo "创建服务: {$servicePath}\n";
    } else {
        echo "服务已存在: {$servicePath}\n";
    }
}

echo "\n{$className} {$type} 创建完成!\n";