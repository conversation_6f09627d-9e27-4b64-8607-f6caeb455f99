<template>
  <div class="home-container">
    <div class="welcome-section">
      <h1 class="welcome-title">欢迎使用 dogAdmin</h1>
      <p class="welcome-subtitle">高效、安全的企业级管理平台</p>
      
      <div class="dog-container">
        <div class="dog">
          <div class="dog-body">
            <div class="dog-tail"></div>
            <div class="dog-head">
              <div class="dog-ears">
                <div class="dog-ear"></div>
                <div class="dog-ear"></div>
              </div>
              <div class="dog-face">
                <div class="dog-eyes">
                  <div class="dog-eye"></div>
                  <div class="dog-eye"></div>
                </div>
                <div class="dog-nose"></div>
                <div class="dog-mouth"></div>
              </div>
            </div>
            <div class="dog-legs">
              <div class="dog-leg"></div>
              <div class="dog-leg"></div>
              <div class="dog-leg"></div>
              <div class="dog-leg"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="homePage">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('dogAdmin首页加载完成')
})
</script>

<style lang="scss" scoped>
.home-container {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.welcome-section {
  text-align: center;
  width: 100%;
  max-width: 1200px;
}

.welcome-title {
  font-size: 36px;
  color: var(--el-color-primary);
  margin-bottom: 10px;
  font-weight: bold;
}

.welcome-subtitle {
  font-size: 18px;
  color: var(--el-text-color-secondary);
  margin-bottom: 60px;
}

.dog-container {
  width: 100%;
  height: 200px;
  position: relative;
  overflow: hidden;
}

.dog {
  position: absolute;
  left: -100px;
  top: 50%;
  transform: translateY(-50%);
  animation: run 10s linear infinite;
}

@keyframes run {
  0% {
    left: -100px;
  }
  100% {
    left: calc(100% + 100px);
  }
}

.dog-body {
  width: 80px;
  height: 40px;
  background-color: #8B4513;
  border-radius: 20px;
  position: relative;
  animation: body-move 0.5s infinite alternate;
}

@keyframes body-move {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-5px);
  }
}

.dog-head {
  width: 50px;
  height: 50px;
  background-color: #8B4513;
  border-radius: 50%;
  position: absolute;
  left: -20px;
  top: -25px;
  animation: head-move 0.5s infinite alternate;
}

@keyframes head-move {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(5deg);
  }
}

.dog-ears {
  position: relative;
  width: 100%;
  height: 100%;
}

.dog-ear {
  width: 20px;
  height: 20px;
  background-color: #8B4513;
  border-radius: 50% 50% 0 0;
  position: absolute;
  top: -10px;
}

.dog-ear:first-child {
  left: 5px;
  transform: rotate(-30deg);
}

.dog-ear:last-child {
  right: 5px;
  transform: rotate(30deg);
}

.dog-face {
  position: relative;
  width: 100%;
  height: 100%;
}

.dog-eyes {
  position: absolute;
  width: 100%;
  top: 15px;
  display: flex;
  justify-content: space-around;
}

.dog-eye {
  width: 8px;
  height: 8px;
  background-color: #000;
  border-radius: 50%;
  animation: blink 3s infinite;
}

@keyframes blink {
  0%, 95%, 100% {
    transform: scaleY(1);
  }
  97.5% {
    transform: scaleY(0);
  }
}

.dog-nose {
  width: 12px;
  height: 8px;
  background-color: #000;
  border-radius: 50%;
  position: absolute;
  top: 25px;
  left: 50%;
  transform: translateX(-50%);
}

.dog-mouth {
  width: 15px;
  height: 5px;
  border-bottom: 2px solid #000;
  border-radius: 0 0 50% 50%;
  position: absolute;
  top: 35px;
  left: 50%;
  transform: translateX(-50%);
}

.dog-tail {
  width: 30px;
  height: 10px;
  background-color: #8B4513;
  border-radius: 5px;
  position: absolute;
  right: -15px;
  top: 5px;
  transform-origin: left center;
  animation: tail-wag 0.3s infinite alternate;
}

@keyframes tail-wag {
  0% {
    transform: rotate(-20deg);
  }
  100% {
    transform: rotate(20deg);
  }
}

.dog-legs {
  position: absolute;
  width: 100%;
  bottom: -10px;
  display: flex;
  justify-content: space-between;
}

.dog-leg {
  width: 10px;
  height: 20px;
  background-color: #8B4513;
  border-radius: 5px;
}

.dog-leg:nth-child(1) {
  animation: front-leg 0.25s infinite alternate;
}

.dog-leg:nth-child(2) {
  animation: back-leg 0.25s infinite alternate;
}

.dog-leg:nth-child(3) {
  animation: back-leg 0.25s infinite alternate-reverse;
}

.dog-leg:nth-child(4) {
  animation: front-leg 0.25s infinite alternate-reverse;
}

@keyframes front-leg {
  0% {
    transform: rotate(-30deg);
  }
  100% {
    transform: rotate(30deg);
  }
}

@keyframes back-leg {
  0% {
    transform: rotate(-15deg);
  }
  100% {
    transform: rotate(15deg);
  }
}
</style>
