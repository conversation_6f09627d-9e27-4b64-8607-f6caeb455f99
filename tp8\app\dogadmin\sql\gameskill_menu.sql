-- 添加游戏技能表菜单


-- 添加游戏技能表主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `name`, `component`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('游戏技能表', 122, '2', '/game/gameSkill/index', 'gameSkillPage', 'common/gameSkill/index', 'Menu', 'game:gameSkill:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的菜单ID
SET @menuId = LAST_INSERT_ID();

-- 添加游戏技能表按钮权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `auth`, `status`, `is_hide`,`create_time`, `update_time`) VALUES
('查询', @menuId, '3', 'game:gameSkill:listPage', '1','0', NOW(), NOW()),
('获取详情', @menuId, '3', 'game:gameSkill:getById', '1','0', NOW(), NOW()),
('获取排序', @menuId, '3', 'game:gameSkill:getSorted', '1','0', NOW(), NOW()),
('新增', @menuId, '3', 'game:gameSkill:add', '1','0', NOW(), NOW()),
('修改', @menuId, '3', 'game:gameSkill:update', '1','0', NOW(), NOW()),
('删除', @menuId, '3', 'game:gameSkill:deleteById', '1','0', NOW(), NOW()),
('批量删除', @menuId, '3', 'game:gameSkill:batchDelete', '1','0', NOW(), NOW()),
('更新状态', @menuId, '3', 'game:gameSkill:updateStatus', '1','0', NOW(), NOW());