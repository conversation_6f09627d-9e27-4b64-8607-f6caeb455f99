<template>
  <view class="publish-container">
    <!-- 文字输入区域 -->
    <view class="text-input-section">
      <textarea 
        class="content-input"
        v-model="content"
        placeholder="这一刻的想法..."
        placeholder-class="input-placeholder"
        :maxlength="1000"
        :auto-height="true"
        :focus="autoFocus"
      />
      <view class="char-count">{{ content.length }}/1000</view>
    </view>
    
    <!-- 图片选择区域 -->
    <view class="images-section">
      <view class="images-grid">
        <view 
          v-for="(image, index) in selectedImages" 
          :key="index"
          class="image-item"
        >
          <image class="selected-image" :src="image" mode="aspectFill" />
          <view class="delete-btn" @tap="removeImage(index)">
            <text class="delete-icon">×</text>
          </view>
        </view>
        
        <view 
          v-if="selectedImages.length < 9" 
          class="add-image-btn"
          @tap="chooseImage"
        >
          <text class="add-icon">+</text>
        </view>
      </view>
    </view>
    
    <!-- 位置选择 -->
    <view class="location-section" @tap="chooseLocation">
      <view class="location-item">
        <text class="location-icon">📍</text>
        <text class="location-text">{{ selectedLocation || '所在位置' }}</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 提醒谁看 -->
    <view class="remind-section" @tap="chooseRemindUsers">
      <view class="remind-item">
        <text class="remind-icon">@</text>
        <text class="remind-text">{{ remindText }}</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 谁可以看 -->
    <view class="privacy-section" @tap="choosePrivacy">
      <view class="privacy-item">
        <text class="privacy-icon">👁️</text>
        <text class="privacy-text">{{ privacyText }}</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 发布按钮 -->
    <view class="publish-actions">
      <button class="cancel-btn" @tap="cancel">取消</button>
      <button 
        class="publish-btn" 
        :class="{ 'btn-disabled': !canPublish }"
        @tap="publish"
        :disabled="!canPublish"
      >
        发表
      </button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PublishMoment',
  props: {
    autoFocus: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      content: '',
      selectedImages: [],
      selectedLocation: '',
      remindUsers: [],
      privacy: 'public' // public, friends, private
    }
  },
  computed: {
    canPublish() {
      return this.content.trim() || this.selectedImages.length > 0
    },
    
    remindText() {
      if (this.remindUsers.length === 0) return '提醒谁看'
      if (this.remindUsers.length === 1) return `提醒 ${this.remindUsers[0].name}`
      return `提醒 ${this.remindUsers.length} 人`
    },
    
    privacyText() {
      const privacyMap = {
        public: '公开',
        friends: '仅朋友可见',
        private: '仅自己可见'
      }
      return privacyMap[this.privacy] || '公开'
    }
  },
  methods: {
    chooseImage() {
      const remainCount = 9 - this.selectedImages.length
      
      uni.chooseImage({
        count: remainCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.selectedImages = [...this.selectedImages, ...res.tempFilePaths]
        },
        fail: (err) => {
          console.error('选择图片失败:', err)
        }
      })
    },
    
    removeImage(index) {
      this.selectedImages.splice(index, 1)
    },
    
    chooseLocation() {
      // 这里可以集成地图选择位置的功能
      uni.showToast({
        title: '位置选择功能开发中',
        icon: 'none'
      })
    },
    
    chooseRemindUsers() {
      // 这里可以打开好友选择页面
      uni.showToast({
        title: '好友选择功能开发中',
        icon: 'none'
      })
    },
    
    choosePrivacy() {
      const items = ['公开', '仅朋友可见', '仅自己可见']
      
      uni.showActionSheet({
        itemList: items,
        success: (res) => {
          const privacyMap = ['public', 'friends', 'private']
          this.privacy = privacyMap[res.tapIndex]
        }
      })
    },
    
    publish() {
      if (!this.canPublish) return
      
      const momentData = {
        content: this.content.trim(),
        images: this.selectedImages,
        location: this.selectedLocation,
        remindUsers: this.remindUsers,
        privacy: this.privacy,
        createTime: new Date().getTime()
      }
      
      this.$emit('publish', momentData)
    },
    
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.publish-container {
  background-color: #F7F7F9;
  min-height: 100vh;
  padding: 32rpx;
}

.text-input-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.content-input {
  width: 100%;
  min-height: 200rpx;
  font-size: 32rpx;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.8);
  background-color: transparent;
}

.input-placeholder {
  color: rgba(0, 0, 0, 0.5);
}

.char-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
}

.images-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: calc((100% - 32rpx) / 3);
  height: 200rpx;
}

.selected-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #FF4249;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

.add-image-btn {
  width: calc((100% - 32rpx) / 3);
  height: 200rpx;
  border: 2rpx dashed rgba(0, 0, 0, 0.3);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F7F7F9;
}

.add-icon {
  font-size: 48rpx;
  color: rgba(0, 0, 0, 0.5);
}

.location-section, .remind-section, .privacy-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.location-item, .remind-item, .privacy-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
}

.location-icon, .remind-icon, .privacy-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
}

.location-text, .remind-text, .privacy-text {
  flex: 1;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.8);
}

.arrow {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.3);
}

.publish-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.cancel-btn, .publish-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
}

.cancel-btn {
  background-color: #ffffff;
  color: rgba(0, 0, 0, 0.8);
  margin-right: 20rpx;
  border: 2rpx solid #e5e5e5;
}

.publish-btn {
  background-color: #00C78B;
  color: #ffffff;
  margin-left: 20rpx;
}

.btn-disabled {
  background-color: #B9B9B9;
  color: rgba(255, 255, 255, 0.8);
}
</style>
