import http from './index.js'

// 上传图片
export function uploadImg(data) {
	return http.request({
		url: 'mwdl/api/common/uploadImg',
		method: 'POST',
		data
	})
}

export function getRollNewsList(data) {
	return http.request({
		url: 'mwdl/api/common/getRollNewsList',
		method: 'POST',
		data
	})
}

export function getTabs(data) {
	return http.request({
		url: 'mwdl/api/common/getTabs',
		method: 'POST',
		data
	})
}
export const uploadImgUrl = 'mwdl/api/common/uploadImg';
