<?php
declare(strict_types=1);

namespace app\lib\exception;

use Throwable;

/**
 * 调试异常类
 * 用于开发调试时抛出异常，支持各种数据格式
 */
class DebugException extends BaseException
{
    /**
     * 原始数据
     * @var mixed
     */
    protected $rawData;

    /**
     * 数据类型
     * @var string
     */
    protected $dataType;

    /**
     * 构造函数
     *
     * @param mixed $data 调试数据，支持字符串、数组、对象、JSON等
     * @param int $code 错误码
     * @param string $message 自定义错误消息，为空时使用错误码映射的消息
     * @param Throwable|null $previous 上一个异常
     */
    public function __construct($data = null, int $code = 9999, string $message = '调试异常', ?Throwable $previous = null)
    {
        $this->rawData = $data;
        $this->dataType = $this->determineDataType($data);

        // 处理数据，转换为数组格式
        $processedData = $this->processData($data);
        
        // 如果是调试模式，修改错误消息为包含详细信息的JSON
        if (env('APP_DEBUG', true)) {
            $debugInfo = [
                'code' => $code,
                'msg' => $message,
                'data' => [
                    'type' => $this->dataType,
                    'debug_info' => $processedData,
                    'raw_data' => $this->rawData
                ]
            ];
            $message = json_encode($debugInfo, JSON_UNESCAPED_UNICODE);
        }

        // 调用父类构造函数
        parent::__construct($processedData, $code, $message, $previous);
    }

    /**
     * 确定数据类型
     * 根据传入的数据自动判断其类型，包括对JSON字符串的特殊处理
     *
     * @param mixed $data 需要确定类型的数据
     * @return string 确定的数据类型（null, string, json, array, object, boolean, integer, float等）
     */
    protected function determineDataType($data): string
    {
        if (is_null($data)) {
            return 'null';
        } elseif (is_string($data)) {
            // 检查是否是JSON字符串
            if ($this->isJson($data)) {
                return 'json';
            }
            return 'string';
        } elseif (is_array($data)) {
            return 'array';
        } elseif (is_object($data)) {
            return 'object';
        } elseif (is_bool($data)) {
            return 'boolean';
        } elseif (is_int($data)) {
            return 'integer';
        } elseif (is_float($data)) {
            return 'float';
        } else {
            return gettype($data);
        }
    }

    /**
     * 检查字符串是否是有效的JSON
     *
     * @param string $string 要检查的字符串
     * @return bool 是否是有效的JSON
     */
    protected function isJson(string $string): bool
    {
        json_decode($string);
        return (json_last_error() === JSON_ERROR_NONE);
    }

    /**
     * 处理数据，转换为适合存储的格式
     *
     * @param mixed $data 原始数据
     * @return array 处理后的数据
     */
    protected function processData($data): array
    {
        $result = [
            'type' => $this->dataType,
            'debug_info' => null
        ];

        switch ($this->dataType) {
            case 'null':
                $result['debug_info'] = 'NULL';
                break;
            case 'string':
                $result['debug_info'] = $data;
                break;
            case 'json':
                $result['debug_info'] = json_decode($data, true);
                break;
            case 'array':
                $result['debug_info'] = $data;
                break;
            case 'object':
                // 尝试转换对象为数组
                if (method_exists($data, 'toArray')) {
                    $result['debug_info'] = $data->toArray();
                } else {
                    $result['debug_info'] = $this->objectToArray($data);
                }
                $result['class'] = get_class($data);
                break;
            case 'boolean':
                $result['debug_info'] = $data ? 'true' : 'false';
                break;
            default:
                $result['debug_info'] = (string)$data;
        }

        return $result;
    }

    /**
     * 将对象转换为数组
     *
     * @param object $object 要转换的对象
     * @return array 转换后的数组
     */
    protected function objectToArray($object): array
    {
        if (is_object($object)) {
            // 如果对象实现了JsonSerializable接口
            if ($object instanceof \JsonSerializable) {
                return (array)$object->jsonSerialize();
            }

            // 尝试将对象转换为数组
            $objectVars = get_object_vars($object);

            // 如果对象没有公共属性，尝试使用反射获取所有属性
            if (empty($objectVars)) {
                $reflection = new \ReflectionClass($object);
                $properties = $reflection->getProperties();

                foreach ($properties as $property) {
                    $property->setAccessible(true);
                    $name = $property->getName();
                    $value = $property->getValue($object);
                    $objectVars[$name] = $value;
                }
            }

            return $objectVars;
        }

        return [];
    }

    /**
     * 获取原始数据
     *
     * @return mixed 原始数据
     */
    public function getRawData()
    {
        return $this->rawData;
    }

    /**
     * 获取数据类型
     *
     * @return string 数据类型
     */
    public function getDataType(): string
    {
        return $this->dataType;
    }
    /**
     * 静态方法：快速抛出调试异常
     *
     * @param mixed $data 调试数据
     * @param string $message 自定义错误消息
     * @throws DebugException
     */
    public static function dump($data, string $message = '调试数据'): void
    {
        throw new self($data, 9999, $message);
    }

    /**
     * 静态方法：打印数据并继续执行（不抛出异常）
     *
     * @param mixed $data 调试数据
     * @param string $message 自定义消息
     * @return void
     */
    public static function print($data, string $message = '调试数据'): void
    {
        $instance = new self($data, 9999, $message);
        echo '<pre>';
        echo '<h2>' . htmlspecialchars($message) . '</h2>';
        echo '<h3>数据类型: ' . $instance->getDataType() . '</h3>';
        echo '<div style="background:#f5f5f5;padding:10px;border:1px solid #ddd;margin:10px 0;">';
        print_r($instance->getData());
        echo '</div>';
        echo '</pre>';
    }
}
