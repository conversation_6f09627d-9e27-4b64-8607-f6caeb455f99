<template>
  <div class="koi-flex">
    <KoiCard>
      <!-- 搜索表单 -->
      <el-form :inline="true">
        <el-form-item label="id" prop="id">
          <el-input v-model="searchParams.id" placeholder="请输入id" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="searchParams.name" placeholder="请输入姓名" clearable style="width: 220px" @keyup.enter.native="handleListPage" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" plain @click="handleSearch" v-auth="['robot:robotAiCharacter:listPage']">搜索</el-button>
          <el-button type="danger" icon="refresh" plain @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 操作按钮 -->
      <el-row :gutter="10">
        <el-col :span="1.5" v-auth="['robot:robotAiCharacter:add']">
          <el-button type="primary" icon="plus" plain @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['robot:robotAiCharacter:update']">
          <el-button type="success" icon="edit" plain @click="handleUpdate" :disabled="single">修改</el-button>
        </el-col>
        <el-col :span="1.5" v-auth="['robot:robotAiCharacter:deleteById']">
          <el-button type="danger" icon="delete" plain @click="handleBatchDelete" :disabled="multiple">删除</el-button>
        </el-col>
      </el-row>

      <div class="h-20px"></div>
      
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableList"
        border
        empty-text="暂时没有数据哟🌻"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" prop="id" width="80" align="center"></el-table-column>
        <el-table-column label="姓名" prop="name" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="昵称" prop="nickname" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="性别" prop="gender" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="头像URL" prop="avatar" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="职业" prop="occupation" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="性格描述" prop="personality" width="250" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="说话风格" prop="speaking_style" width="250" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="口头禅" prop="catchphrases" width="250" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="禁用词汇" prop="forbidden_words" width="250" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="Emoji使用频率" prop="emoji_preference" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="擅长评论的主题（JSON）" prop="topic_preference" width="250" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="评论长度偏好" prop="comment_length_range" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="响应速度" prop="response_speed" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="活跃时间段" prop="active_time" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="背景故事" prop="backstory" width="250" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="语言偏好" prop="language_preference" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="是否启用（1=是，0=否）" prop="is_active" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="状态标识" prop="status" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              active-value="1"
              inactive-value="0"
              inline-prompt
              @change="handleSwitch(scope.row)"
              v-auth="['robot:robotAiCharacter:update']"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="create_time" width="180" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="更新时间" prop="update_time" width="180" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="操作" align="center" width="120" fixed="right" v-auth="['robot:robotAiCharacter:update', 'robot:robotAiCharacter:deleteById']">
          <template #default="{ row }">
            <el-tooltip content="修改🌻" placement="top">
              <el-button
                type="primary"
                icon="Edit"
                circle
                plain
                @click="handleUpdate(row)"
                v-auth="['robot:robotAiCharacter:update']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除🌻" placement="top">
              <el-button
                type="danger"
                icon="Delete"
                circle
                plain
                @click="handleDelete(row)"
                v-auth="['robot:robotAiCharacter:deleteById']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="h-20px"></div>
      
      <!-- 分页 -->
      <el-pagination
        background
        v-model:current-page="searchParams.page_no"
        v-model:page-size="searchParams.page_size"
        v-show="total > 0"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <!-- 添加 OR 修改 -->
      <KoiDrawer
        ref="koiDrawerRef"
        :title="title"
        @koiConfirm="handleConfirm"
        @koiCancel="handleCancel"
        :loading="confirmLoading"
      >
        <template #content>
          <el-form ref="formRef" :rules="rules" :model="form" label-width="150px" status-icon>
            <!-- 随机生成按钮 -->
            <el-row style="margin-bottom: 20px;">
              <el-col :span="24">
                <el-button type="primary" icon="Refresh" @click="handleRandomGenerate">随机生成角色</el-button>
                <el-button type="success" icon="MagicStick" @click="handleRandomPersonality">随机性格</el-button>
                <el-button type="warning" icon="ChatDotRound" @click="handleRandomSpeakingStyle">随机说话风格</el-button>
                <el-button type="info" icon="ChatLineRound" @click="handleRandomCatchphrases">随机口头禅</el-button>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="id" prop="id">
                  <el-input v-model="form.id" placeholder="id" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="form.name" placeholder="请输入姓名" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="昵称" prop="nickname">
                  <el-input v-model="form.nickname" placeholder="请输入昵称" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-select v-model="form.gender" placeholder="请选择性别" clearable style="width: 100%">
                    <el-option
                      v-for="item in dictData.gender || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="头像URL" prop="avatar">
                  <el-input v-model="form.avatar" placeholder="请输入头像URL" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职业" prop="occupation">
                  <el-select v-model="form.occupation" placeholder="请选择职业" clearable style="width: 100%">
                    <el-option
                      v-for="item in dictData.occupation || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="性格描述" prop="personality">
                  <el-select v-model="form.personality" placeholder="请选择性格" clearable style="width: 100%" multiple>
                    <el-option
                      v-for="item in dictData.personality || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="说话风格" prop="speaking_style">
                  <el-select v-model="form.speaking_style" placeholder="请选择说话风格" clearable style="width: 100%" multiple>
                    <el-option
                      v-for="item in dictData.speaking_style || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="口头禅" prop="catchphrases">
                  <el-select v-model="form.catchphrases" placeholder="请选择口头禅" clearable style="width: 100%" multiple>
                    <el-option
                      v-for="item in dictData.catchphrases || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="禁用词汇" prop="forbidden_words">
                  <el-input v-model="form.forbidden_words" type="textarea" :rows="3" placeholder="请输入禁用词汇" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Emoji使用频率" prop="emoji_preference">
                  <el-select v-model="form.emoji_preference" placeholder="请选择Emoji使用频率" clearable style="width: 100%">
                    <el-option
                      v-for="item in dictData.emoji_preference || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="擅长评论的主题" prop="topic_preference">
                  <el-input v-model="form.topic_preference" type="textarea" :rows="3" placeholder="请输入擅长评论的主题（JSON）" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="评论长度偏好" prop="comment_length_range">
                  <el-input v-model="form.comment_length_range" placeholder="请输入评论长度偏好" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="响应速度" prop="response_speed">
                  <el-select v-model="form.response_speed" placeholder="请选择响应速度" clearable style="width: 100%">
                    <el-option
                      v-for="item in dictData.response_speed || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="活跃时间段" prop="active_time">
                  <el-select v-model="form.active_time" placeholder="请选择活跃时间段" clearable style="width: 100%">
                    <el-option
                      v-for="item in dictData.active_time || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="背景故事" prop="backstory">
                  <el-input v-model="form.backstory" type="textarea" :rows="3" placeholder="请输入背景故事" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="语言偏好" prop="language_preference">
                  <el-select v-model="form.language_preference" placeholder="请选择语言偏好" clearable style="width: 100%">
                    <el-option
                      v-for="item in dictData.language_preference || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否启用" prop="is_active">
                  <el-select v-model="form.is_active" placeholder="请选择是否启用" clearable style="width: 100%">
                    <el-option
                      v-for="item in dictData.is_active || []"
                      :key="item.id"
                      :label="item.dict_name"
                      :value="item.dict_value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="状态标识" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio value="1">启用</el-radio>
                    <el-radio value="0">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="创建时间" prop="create_time">
                  <el-date-picker
                    v-model="form.create_time"
                    type="datetime"
                    placeholder="选择创建时间"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="更新时间" prop="update_time">
                  <el-date-picker
                    v-model="form.update_time"
                    type="datetime"
                    placeholder="选择更新时间"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </KoiDrawer>
    </KoiCard>
  </div>
</template>

<script setup lang="ts" name="robotAiCharacter">
import type { FormInstance } from 'element-plus';
import { nextTick, ref, reactive, onMounted } from 'vue';
import {
  koiNoticeSuccess,
  koiNoticeError,
  koiMsgSuccess,
  koiMsgError,
  koiMsgWarning,
  koiMsgBox,
  koiMsgInfo
} from "@/utils/koi.ts";
import { listPage, getById, add, update, deleteById, batchDelete, updateStatus, getSorted } from '@/api/robot/robotAiCharacter/index.ts';
import { getAllActiveDict } from '@/api/robot/robotAiCharacterDict/index.ts';
import { useKoiDict } from "@/hooks/dicts/index.ts";

const { koiDicts } = useKoiDict(["sys_switch_status"]);

// 字典数据
const dictData = ref<any>({});

// 表格数据
const loading = ref(false);
const tableList = ref<any[]>([]);
const selectedIds = ref<any[]>([]);

// 总条数
const total = ref<number>(0);

// 搜索表单
const searchParams = reactive({
  page_no: 1,
  page_size: 10,
  id: '',
  name: ''
});

// 重置搜索表单
const resetSearch = () => {
  searchParams.page_no = 1;
  searchParams.page_size = 10;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page_no' && key !== 'page_size') {
      searchParams[key] = '';
    }
  });
  handleListPage();
};

// 查询数据
const handleSearch = () => {
  searchParams.page_no = 1;
  handleListPage();
};

// 加载表格数据
const handleListPage = async () => {
  loading.value = true;
  try {
    const res = await listPage(searchParams);
    if (res.code === 1) {
      tableList.value = res.data.records;
      total.value = res.data.total;
    } else {
      koiMsgError(res.msg || '获取数据失败');
    }
  } catch (error) {
    console.error(error);
    koiMsgError('获取数据失败');
  } finally {
    loading.value = false;
  }
};

const single = ref<boolean>(true); // 非单个禁用
const multiple = ref<boolean>(true); // 非多个禁用
// 表格选择
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id);
  single.value = selection.length != 1; // 单选
  multiple.value = !selection.length; // 多选
};

// 分页变化
const handleSizeChange = (size: number) => {
  searchParams.page_size = size;
  handleListPage();
};

const handleCurrentChange = (page: number) => {
  searchParams.page_no = page;
  handleListPage();
};

// 表单相关
const koiDrawerRef = ref();
const title = ref('机器人');
const formRef = ref<FormInstance>();
const form = ref<any>({
  id: '',
  name: '',
  nickname: '',
  gender: '',
  avatar: '',
  occupation: '',
  personality: [],
  speaking_style: [],
  catchphrases: [],
  forbidden_words: '',
  emoji_preference: '',
  topic_preference: '',
  comment_length_range: '140字以内',
  response_speed: '',
  active_time: '',
  backstory: '',
  language_preference: '',
  is_active: '',
  status: '1', // 默认启用
  create_time: '',
  update_time: ''
});

// 表单验证规则
const rules = reactive({
  name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
  occupation: [{ required: true, message: '职业不能为空', trigger: 'blur' }],
  personality: [{ required: true, message: '性格描述不能为空', trigger: 'blur' }],
  speaking_style: [{ required: true, message: '说话风格不能为空', trigger: 'blur' }]
});

// 确认按钮loading
const confirmLoading = ref(false);

// 新增
const handleAdd = () => {
  // 打开抽屉
  koiDrawerRef.value.koiOpen();
  // 重置表单
  resetForm();
  // 设置标题
  title.value = '添加机器人';
};

// 清空表单数据
const resetForm = () => {
  // 等待 DOM 更新完成
  nextTick(() => {
    if (formRef.value) {
      // 重置该表单项，将其值重置为初始值，并移除校验结果
      formRef.value.resetFields();
    }
  });
  form.value = {
  id: '',
  name: '',
  nickname: '',
  gender: '',
  avatar: '',
  occupation: '',
  personality: [],
  speaking_style: [],
  catchphrases: [],
  forbidden_words: '',
  emoji_preference: '',
  topic_preference: '',
  comment_length_range: '140字以内',
  response_speed: '',
  active_time: '',
  backstory: '',
  language_preference: '',
  is_active: '',
  status: '1', // 默认启用
  create_time: '',
  update_time: ''
  };
};

// 回显数据
const handleEcho = async (id: any) => {
  if (id == null || id == "") {
    koiMsgWarning("请选择需要修改的数据🌻");
    return;
  }
  try {
    const res = await getById(id);
    if (res.code === 1) {
      form.value = res.data;
      // 将字符串转换为数组用于多选框显示
      if (form.value.personality && typeof form.value.personality === 'string') {
        form.value.personality = form.value.personality.split(',').filter(item => item.trim());
      }
      if (form.value.speaking_style && typeof form.value.speaking_style === 'string') {
        form.value.speaking_style = form.value.speaking_style.split(',').filter(item => item.trim());
      }
      if (form.value.catchphrases && typeof form.value.catchphrases === 'string') {
        form.value.catchphrases = form.value.catchphrases.split(',').filter(item => item.trim());
      }
    } else {
      koiNoticeError(res.msg || '获取详情失败');
    }
  } catch (error) {
    console.log(error);
    koiNoticeError('获取详情失败，请刷新重试🌻');
  }
};

// 修改
const handleUpdate = async (row?: any) => {
  // 打开抽屉
  koiDrawerRef.value.koiOpen();
  // 重置表单
  resetForm();
  // 设置标题
  title.value = '修改机器人';
  
  const id = row ? row.id : selectedIds.value[0];
  if (id == null || id == "") {
    koiMsgError("请选中需要修改的数据🌻");
    return;
  }
  // 回显数据
  handleEcho(id);
};

// 确认提交
const handleConfirm = () => {
  if (!formRef.value) return;
  confirmLoading.value = true;
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 准备提交数据，将数组转换为字符串
      const submitData = { ...form.value };
      if (Array.isArray(submitData.personality)) {
        submitData.personality = submitData.personality.join(',');
      }
      if (Array.isArray(submitData.speaking_style)) {
        submitData.speaking_style = submitData.speaking_style.join(',');
      }
      if (Array.isArray(submitData.catchphrases)) {
        submitData.catchphrases = submitData.catchphrases.join(',');
      }

      if (form.value.id != null && form.value.id != "") {
        try {
          const res = await update(submitData);
          if (res.code === 1) {
            koiMsgSuccess('修改成功🌻');
            confirmLoading.value = false;
            koiDrawerRef.value.koiQuickClose();
            resetForm();
            handleListPage();
          } else {
            koiMsgError(res.msg || '修改失败');
            confirmLoading.value = false;
          }
        } catch (error) {
          console.error(error);
          confirmLoading.value = false;
          koiMsgError('修改失败，请重试');
        }
      } else {
        try {
          const res = await add(submitData);
          if (res.code === 1) {
            koiMsgSuccess('添加成功🌻');
            confirmLoading.value = false;
            koiDrawerRef.value.koiQuickClose();
            resetForm();
            handleListPage();
          } else {
            koiMsgError(res.msg || '添加失败');
            confirmLoading.value = false;
          }
        } catch (error) {
          console.error(error);
          confirmLoading.value = false;
          koiMsgError('添加失败，请重试');
        }
      }
    } else {
      koiMsgError('验证失败，请检查填写内容🌻');
      confirmLoading.value = false;
    }
  });
};

// 取消
const handleCancel = () => {
  koiDrawerRef.value.koiClose();
};

// 删除
const handleDelete = (row: any) => {
  const id = row.id;
  if (id == null || id == "") {
    koiMsgWarning("请选中需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认删除该数据么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await deleteById(id);
        handleListPage();
        koiNoticeSuccess("删除成功🌻");
      } catch (error) {
        console.log(error);
        handleListPage();
        koiNoticeError("删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedIds.value.length == 0) {
    koiMsgInfo("请选择需要删除的数据🌻");
    return;
  }
  koiMsgBox("您确认进行批量删除么？删除后将无法进行恢复？")
    .then(async () => {
      try {
        await batchDelete(selectedIds.value);
        handleListPage();
        koiNoticeSuccess("批量删除成功🌻");
      } catch (error) {
        console.log(error);
        handleListPage();
        koiNoticeError("批量删除失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      koiMsgError("已取消🌻");
    });
};

// 状态switch
const handleSwitch = (row: any) => {
  let text = row.status === "0" ? "停用" : "启用";
  koiMsgBox("确认要[" + text + "]-[" + row.id + "]吗？")
    .then(async () => {
      if (!row.id || !row.status) {
        row.status = row.status == "0" ? "1" : "0";
        koiMsgWarning("请选择需要修改的数据🌻");
        return;
      }
      try {
        await updateStatus(row.id, row.status);
        koiNoticeSuccess("修改成功🌻");
      } catch (error) {
        handleListPage();
        console.log(error);
        koiNoticeError("修改失败，请刷新重试🌻");
      }
    })
    .catch(() => {
      row.status = row.status == "0" ? "1" : "0";
      koiMsgError("已取消🌻");
    });
};

// 获取最新排序数字
const handleSorted = async () => {
  try {
    const res = await getSorted({});
    if (res.code === 1) {
      form.value.sorted = res.data;
    }
  } catch (error) {
    console.log(error);
    koiMsgError("数据查询失败，请重试🌻");
  }
};

// 加载字典数据
const loadDictData = async () => {
  try {
    const res = await getAllActiveDict();
    if (res.code === 1) {
      dictData.value = res.data;
    } else {
      koiMsgError('获取字典数据失败');
    }
  } catch (error) {
    console.error(error);
    koiMsgError('获取字典数据失败');
  }
};

// 随机选择数组中的元素
const getRandomItem = (arr: any[]) => {
  if (!arr || arr.length === 0) return null;
  return arr[Math.floor(Math.random() * arr.length)];
};

// 随机选择多个元素
const getRandomItems = (arr: any[], count: number = 2) => {
  if (!arr || arr.length === 0) return [];
  const shuffled = [...arr].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.min(count, arr.length));
};

// 随机生成完整角色
const handleRandomGenerate = () => {
  // 随机选择基础信息
  const genderItem = getRandomItem(dictData.value.gender || []);
  const occupationItem = getRandomItem(dictData.value.occupation || []);
  const emojiItem = getRandomItem(dictData.value.emoji_preference || []);
  const responseSpeedItem = getRandomItem(dictData.value.response_speed || []);
  const activeTimeItem = getRandomItem(dictData.value.active_time || []);
  const languageItem = getRandomItem(dictData.value.language_preference || []);
  const isActiveItem = getRandomItem(dictData.value.is_active || []);

  // 随机选择多个性格特征
  const personalityItems = getRandomItems(dictData.value.personality || [], 3);
  const speakingStyleItems = getRandomItems(dictData.value.speaking_style || [], 2);
  const catchphrasesItems = getRandomItems(dictData.value.catchphrases || [], 5);

  // 填充表单
  if (genderItem) form.value.gender = genderItem.dict_value;
  if (occupationItem) form.value.occupation = occupationItem.dict_value;
  if (emojiItem) form.value.emoji_preference = emojiItem.dict_value;
  if (responseSpeedItem) form.value.response_speed = responseSpeedItem.dict_value;
  if (activeTimeItem) form.value.active_time = activeTimeItem.dict_value;
  if (languageItem) form.value.language_preference = languageItem.dict_value;
  if (isActiveItem) form.value.is_active = isActiveItem.dict_value;

  form.value.personality = personalityItems.map(item => item.dict_value);
  form.value.speaking_style = speakingStyleItems.map(item => item.dict_value);
  form.value.catchphrases = catchphrasesItems.map(item => item.dict_value);

  koiMsgSuccess('随机生成角色成功！');
};

// 随机生成性格
const handleRandomPersonality = () => {
  const personalityItems = getRandomItems(dictData.value.personality || [], 3);
  form.value.personality = personalityItems.map(item => item.dict_value);
  koiMsgSuccess('随机生成性格成功！');
};

// 随机生成说话风格
const handleRandomSpeakingStyle = () => {
  const speakingStyleItems = getRandomItems(dictData.value.speaking_style || [], 2);
  form.value.speaking_style = speakingStyleItems.map(item => item.dict_value);
  koiMsgSuccess('随机生成说话风格成功！');
};

// 随机生成口头禅
const handleRandomCatchphrases = () => {
  const catchphrasesItems = getRandomItems(dictData.value.catchphrases || [], 5);
  form.value.catchphrases = catchphrasesItems.map(item => item.dict_value);
  koiMsgSuccess('随机生成口头禅成功！');
};

// 初始化
onMounted(() => {
  handleListPage();
  loadDictData();
});
</script>

<style scoped>
.mb-10px {
  margin-bottom: 10px;
}
.mt-10px {
  margin-top: 10px;
}
</style>