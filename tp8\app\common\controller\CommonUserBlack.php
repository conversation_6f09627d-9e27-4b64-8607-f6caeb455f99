<?php

namespace app\common\controller;

use app\common\service\CommonUserBlackService;
use app\common\common\ApiResponse;
use app\dogadmin\controller\Base;

/**
 * 拉黑 控制器
 */
class CommonUserBlack extends Base
{
    /**
     * @var CommonUserBlackService
     */
    protected $service;
    
    /**
     * 初始化方法
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = new CommonUserBlackService();
        $this->params = $this->request->param();
        $this->searchKey = [
            ['create_time' => 'between'],
        ];
    }
    
    // 所有基础CRUD方法均继承自Base控制器
     // 如需自定义方法，请在此处添加
}