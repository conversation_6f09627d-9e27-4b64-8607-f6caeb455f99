<?php
// 事件定义文件
return [
    'bind'      => [
    ],

    'listen'    => [
        'AppInit'  => [],
        'HttpRun'  => [],
        'HttpEnd'  => [],
        'LogLevel' => [],
        'LogWrite' => [],
        // 用户注册成功事件
        'app\common_api\event\UserRegistered' => [
            'app\common_api\listener\UserRegisteredListener',
            'app\game_api\listener\UserRegisteredListener',
        ],
    ],

    'subscribe' => [
    ],
];
